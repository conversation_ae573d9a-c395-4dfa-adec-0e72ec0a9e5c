"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/pricing",{

/***/ "./components/ui/pixel-card.js":
/*!*************************************!*\
  !*** ./components/ui/pixel-card.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PixelCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nclass Pixel {\n    getRandomValue(min, max) {\n        return Math.random() * (max - min) + min;\n    }\n    draw() {\n        const centerOffset = this.maxSizeInteger * 0.5 - this.size * 0.5;\n        this.ctx.fillStyle = this.color;\n        this.ctx.fillRect(this.x + centerOffset, this.y + centerOffset, this.size, this.size);\n    }\n    appear() {\n        this.isIdle = false;\n        if (this.counter <= this.delay) {\n            this.counter += this.counterStep;\n            return;\n        }\n        if (this.size >= this.maxSize) {\n            this.isShimmer = true;\n        }\n        if (this.isShimmer) {\n            this.shimmer();\n        } else {\n            this.size += this.sizeStep;\n        }\n        this.draw();\n    }\n    disappear() {\n        this.isShimmer = false;\n        this.counter = 0;\n        if (this.size <= 0) {\n            this.isIdle = true;\n            return;\n        } else {\n            this.size -= 0.1;\n        }\n        this.draw();\n    }\n    shimmer() {\n        if (this.size >= this.maxSize) {\n            this.isReverse = true;\n        } else if (this.size <= this.minSize) {\n            this.isReverse = false;\n        }\n        if (this.isReverse) {\n            this.size -= this.speed;\n        } else {\n            this.size += this.speed;\n        }\n    }\n    constructor(canvas, context, x, y, color, speed, delay){\n        this.width = canvas.width;\n        this.height = canvas.height;\n        this.ctx = context;\n        this.x = x;\n        this.y = y;\n        this.color = color;\n        this.speed = this.getRandomValue(0.1, 0.9) * speed;\n        this.size = 0;\n        this.sizeStep = Math.random() * 0.4;\n        this.minSize = 0.5;\n        this.maxSizeInteger = 2;\n        this.maxSize = this.getRandomValue(this.minSize, this.maxSizeInteger);\n        this.delay = delay;\n        this.counter = 0;\n        this.counterStep = Math.random() * 4 + (this.width + this.height) * 0.01;\n        this.isIdle = false;\n        this.isReverse = false;\n        this.isShimmer = false;\n    }\n}\nfunction getEffectiveSpeed(value, reducedMotion) {\n    const min = 0;\n    const max = 100;\n    const throttle = 0.001;\n    const parsed = parseInt(value, 10);\n    if (parsed <= min || reducedMotion) {\n        return min;\n    } else if (parsed >= max) {\n        return max * throttle;\n    } else {\n        return parsed * throttle;\n    }\n}\n/**\n * Variants adapted for the pricing theme\n */ const VARIANTS = {\n    default: {\n        activeColor: null,\n        gap: 5,\n        speed: 35,\n        colors: \"#f8fafc,#f1f5f9,#cbd5e1\",\n        noFocus: false\n    },\n    purple: {\n        activeColor: \"#e9d5ff\",\n        gap: 6,\n        speed: 40,\n        colors: \"#e9d5ff,#c4b5fd,#8b5cf6\",\n        noFocus: false\n    },\n    blue: {\n        activeColor: \"#dbeafe\",\n        gap: 5,\n        speed: 30,\n        colors: \"#dbeafe,#93c5fd,#3b82f6\",\n        noFocus: false\n    },\n    highlighted: {\n        activeColor: \"#e9d5ff\",\n        gap: 4,\n        speed: 60,\n        colors: \"#e9d5ff,#c4b5fd,#8b5cf6,#dbeafe,#93c5fd\",\n        noFocus: false\n    }\n};\nfunction PixelCard(param) {\n    let { variant = \"default\", gap, speed, colors, noFocus, className = \"\", children } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pixelsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const timePreviousRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(performance.now());\n    const [reducedMotion, setReducedMotion] = useState(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) {\n            setReducedMotion(window.matchMedia(\"(prefers-reduced-motion: reduce)\").matches);\n        }\n    }, []);\n    const variantCfg = VARIANTS[variant] || VARIANTS.default;\n    const finalGap = gap !== null && gap !== void 0 ? gap : variantCfg.gap;\n    const finalSpeed = speed !== null && speed !== void 0 ? speed : variantCfg.speed;\n    const finalColors = colors !== null && colors !== void 0 ? colors : variantCfg.colors;\n    const finalNoFocus = noFocus !== null && noFocus !== void 0 ? noFocus : variantCfg.noFocus;\n    const initPixels = ()=>{\n        if (!containerRef.current || !canvasRef.current) return;\n        const rect = containerRef.current.getBoundingClientRect();\n        const width = Math.floor(rect.width);\n        const height = Math.floor(rect.height);\n        const ctx = canvasRef.current.getContext(\"2d\");\n        canvasRef.current.width = width;\n        canvasRef.current.height = height;\n        canvasRef.current.style.width = \"\".concat(width, \"px\");\n        canvasRef.current.style.height = \"\".concat(height, \"px\");\n        const colorsArray = finalColors.split(\",\");\n        const pxs = [];\n        for(let x = 0; x < width; x += parseInt(finalGap, 10)){\n            for(let y = 0; y < height; y += parseInt(finalGap, 10)){\n                const color = colorsArray[Math.floor(Math.random() * colorsArray.length)];\n                const dx = x - width / 2;\n                const dy = y - height / 2;\n                const distance = Math.sqrt(dx * dx + dy * dy);\n                const delay = reducedMotion ? 0 : distance;\n                pxs.push(new Pixel(canvasRef.current, ctx, x, y, color, getEffectiveSpeed(finalSpeed, reducedMotion), delay));\n            }\n        }\n        pixelsRef.current = pxs;\n    };\n    const doAnimate = (fnName)=>{\n        var _canvasRef_current;\n        animationRef.current = requestAnimationFrame(()=>doAnimate(fnName));\n        const timeNow = performance.now();\n        const timePassed = timeNow - timePreviousRef.current;\n        const timeInterval = 1000 / 60; // ~60 FPS\n        if (timePassed < timeInterval) return;\n        timePreviousRef.current = timeNow - timePassed % timeInterval;\n        const ctx = (_canvasRef_current = canvasRef.current) === null || _canvasRef_current === void 0 ? void 0 : _canvasRef_current.getContext(\"2d\");\n        if (!ctx || !canvasRef.current) return;\n        ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);\n        let allIdle = true;\n        for(let i = 0; i < pixelsRef.current.length; i++){\n            const pixel = pixelsRef.current[i];\n            pixel[fnName]();\n            if (!pixel.isIdle) {\n                allIdle = false;\n            }\n        }\n        if (allIdle) {\n            cancelAnimationFrame(animationRef.current);\n        }\n    };\n    const handleAnimation = (name)=>{\n        cancelAnimationFrame(animationRef.current);\n        animationRef.current = requestAnimationFrame(()=>doAnimate(name));\n    };\n    const onMouseEnter = ()=>handleAnimation(\"appear\");\n    const onMouseLeave = ()=>handleAnimation(\"disappear\");\n    const onFocus = (e)=>{\n        if (e.currentTarget.contains(e.relatedTarget)) return;\n        handleAnimation(\"appear\");\n    };\n    const onBlur = (e)=>{\n        if (e.currentTarget.contains(e.relatedTarget)) return;\n        handleAnimation(\"disappear\");\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initPixels();\n        const observer = new ResizeObserver(()=>{\n            initPixels();\n        });\n        if (containerRef.current) {\n            observer.observe(containerRef.current);\n        }\n        return ()=>{\n            observer.disconnect();\n            cancelAnimationFrame(animationRef.current);\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        finalGap,\n        finalSpeed,\n        finalColors,\n        finalNoFocus\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"relative overflow-hidden rounded-xl border border-white/10 isolate transition-colors duration-200 ease-[cubic-bezier(0.5,1,0.89,1)] select-none min-h-[400px] \".concat(className),\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onFocus: finalNoFocus ? undefined : onFocus,\n        onBlur: finalNoFocus ? undefined : onBlur,\n        tabIndex: finalNoFocus ? -1 : 0,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                className: \"absolute inset-0 w-full h-full block pointer-events-none\",\n                ref: canvasRef\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\pixel-card.js\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\pixel-card.js\",\n        lineNumber: 260,\n        columnNumber: 5\n    }, this);\n}\n_s(PixelCard, \"HeekizPLUKtvxmyXJ0VDaWjZs9A=\");\n_c = PixelCard;\nvar _c;\n$RefreshReg$(_c, \"PixelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ui/pixel-card.js\n"));

/***/ })

});