"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/pricing",{

/***/ "./components/pricing-section.js":
/*!***************************************!*\
  !*** ./components/pricing-section.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PricingCard: function() { return /* binding */ PricingCard; },\n/* harmony export */   PricingFAQ: function() { return /* binding */ PricingFAQ; },\n/* harmony export */   PricingSection: function() { return /* binding */ PricingSection; },\n/* harmony export */   defaultFAQs: function() { return /* binding */ defaultFAQs; },\n/* harmony export */   defaultPricingPlans: function() { return /* binding */ defaultPricingPlans; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _animations_animate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./animations/animate */ \"./components/animations/animate.js\");\n/* harmony import */ var _ui_enhanced_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/enhanced-button */ \"./components/ui/enhanced-button.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,X!=!lucide-react */ \"__barrel_optimize__?names=Check,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ PricingCard,defaultPricingPlans,PricingFAQ,defaultFAQs,PricingSection auto */ \n\n\n\n\n\n// import PixelCard from \"./ui/pixel-card\";\n// Pricing card component\nconst PricingCard = (param)=>{\n    let { title, price, description, features, buttonText, buttonLink, highlighted = false, delay = 0 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animations_animate__WEBPACK_IMPORTED_MODULE_1__.Animate, {\n        type: \"slide\",\n        delay: delay,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            className: \"relative \".concat(highlighted ? \"transform scale-105 z-10\" : \"\"),\n            whileHover: {\n                y: highlighted ? -8 : -5,\n                scale: highlighted ? 1.08 : 1.02,\n                transition: {\n                    duration: 0.2\n                }\n            },\n            children: [\n                highlighted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 z-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-theme-purple to-theme-blue text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                        children: \"MOST POPULAR\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 37,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PixelCard, {\n                    variant: highlighted ? \"highlighted\" : \"default\",\n                    className: \"\".concat(highlighted ? \"border-2 border-theme-purple/50 shadow-2xl shadow-theme-purple/20 bg-gradient-to-br from-theme-purple/5 to-theme-blue/5\" : \"border border-white/10 subtle-glass\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 p-5 md:p-6 flex flex-col h-full z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg md:text-xl font-bold mb-1\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mb-3\",\n                                        children: description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold mb-2\",\n                                        children: price\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-grow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 my-4 text-sm\",\n                                    children: features.slice(0, 6).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                feature.included ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Check, {\n                                                    className: \"h-4 w-4 text-theme-blue shrink-0 mr-2 mt-0.5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.X, {\n                                                    className: \"h-4 w-4 text-muted-foreground shrink-0 mr-2 mt-0.5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: feature.included ? \"\" : \"text-muted-foreground\",\n                                                    children: feature.text\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                            lineNumber: 63,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: buttonLink,\n                                className: \"mt-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_enhanced_button__WEBPACK_IMPORTED_MODULE_2__.EnhancedButton, {\n                                    size: \"md\",\n                                    color: highlighted ? \"purple\" : \"blue\",\n                                    className: \"w-full \".concat(highlighted ? \"bg-theme-purple text-white hover:bg-theme-purple/90\" : \"bg-transparent border text-foreground dark:border-white/20 dark:hover:border-white/40 border-black/50 hover:border-black/70\"),\n                                    children: buttonText\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PricingCard;\n// Pricing plans data\nconst defaultPricingPlans = [\n    {\n        title: \"Free Plan\",\n        price: \"Free\",\n        description: \"Perfect for trying out our service.\",\n        features: [\n            {\n                text: \"3 free voiceovers\",\n                included: true\n            },\n            {\n                text: \"Basic voice options\",\n                included: true\n            },\n            {\n                text: \"PDF & script upload\",\n                included: true\n            },\n            {\n                text: \"MP4 video generation\",\n                included: true\n            },\n            {\n                text: \"Voice cloning\",\n                included: false\n            },\n            {\n                text: \"Priority support\",\n                included: false\n            }\n        ],\n        buttonText: \"Get Started\",\n        buttonLink: \"/dashboard\",\n        highlighted: false,\n        delay: 0.1\n    },\n    {\n        title: \"Starter Plan\",\n        price: \"$9.99\",\n        description: \"Most popular for regular users.\",\n        features: [\n            {\n                text: \"Unlimited voiceovers\",\n                included: true\n            },\n            {\n                text: \"All voice options\",\n                included: true\n            },\n            {\n                text: \"PDF & script upload\",\n                included: true\n            },\n            {\n                text: \"MP4 video generation\",\n                included: true\n            },\n            {\n                text: \"1 voice clone\",\n                included: true\n            },\n            {\n                text: \"Email support\",\n                included: true\n            }\n        ],\n        buttonText: \"Choose Starter\",\n        buttonLink: \"/\",\n        highlighted: true,\n        delay: 0.2\n    },\n    {\n        title: \"Premium Plan\",\n        price: \"$19.99\",\n        description: \"Best for power users and teams.\",\n        features: [\n            {\n                text: \"Unlimited voiceovers\",\n                included: true\n            },\n            {\n                text: \"All voice options\",\n                included: true\n            },\n            {\n                text: \"PDF & script upload\",\n                included: true\n            },\n            {\n                text: \"MP4 video generation\",\n                included: true\n            },\n            {\n                text: \"5 voice clones\",\n                included: true\n            },\n            {\n                text: \"Priority support\",\n                included: true\n            }\n        ],\n        buttonText: \"Choose Premium\",\n        buttonLink: \"/\",\n        highlighted: false,\n        delay: 0.3\n    }\n];\n// FAQ component\nconst PricingFAQ = (param)=>{\n    let { faqs } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-24 max-w-3xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animations_animate__WEBPACK_IMPORTED_MODULE_1__.Animate, {\n                type: \"fade\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl md:text-3xl font-bold mb-8 text-center\",\n                    children: \"Frequently Asked Questions\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: faqs.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animations_animate__WEBPACK_IMPORTED_MODULE_1__.Animate, {\n                        type: \"slide\",\n                        delay: 0.1 * (index + 1),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"subtle-glass rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-2\",\n                                    children: faq.question\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: faq.answer\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = PricingFAQ;\n// Default FAQ data\nconst defaultFAQs = [\n    {\n        question: \"How do I choose the right plan?\",\n        answer: \"Choose a plan based on your usage needs. If you're just getting started, the Free plan gives you 3 voiceovers to try our service. For regular use, the Starter plan offers unlimited voiceovers and 1 voice clone. For power users who need multiple voice clones, the Premium plan provides 5 voice clones and priority support.\"\n    },\n    {\n        question: \"Can I change my plan at any time?\",\n        answer: \"Yes, you can upgrade or downgrade your plan at any time. Upgrades take effect immediately, while downgrades will take effect at the end of your current billing cycle.\"\n    },\n    {\n        question: \"What is voice cloning and how does it work?\",\n        answer: \"Voice cloning allows you to create custom AI voices based on audio samples. Free users cannot access this feature. Starter plan users can create 1 custom voice, while Premium users can create up to 5 custom voices for their presentations.\"\n    }\n];\n// Main pricing section component\nfunction PricingSection(param) {\n    let { pricingPlans = defaultPricingPlans, faqs = defaultFAQs, showFAQ = true } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-12 md:py-16\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-5xl mx-auto items-center\",\n                children: pricingPlans.map((plan, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(plan.highlighted ? \"md:mt-0\" : \"md:mt-6\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PricingCard, {\n                            ...plan\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this),\n            showFAQ && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PricingFAQ, {\n                faqs: faqs\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 206,\n                columnNumber: 19\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n_c2 = PricingSection;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PricingCard\");\n$RefreshReg$(_c1, \"PricingFAQ\");\n$RefreshReg$(_c2, \"PricingSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/pricing-section.js\n"));

/***/ })

});