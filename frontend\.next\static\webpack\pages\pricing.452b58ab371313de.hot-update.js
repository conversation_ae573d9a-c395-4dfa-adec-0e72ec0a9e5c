"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/pricing",{

/***/ "./components/pricing-section.js":
/*!***************************************!*\
  !*** ./components/pricing-section.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PricingCard: function() { return /* binding */ PricingCard; },\n/* harmony export */   PricingFAQ: function() { return /* binding */ PricingFAQ; },\n/* harmony export */   PricingSection: function() { return /* binding */ PricingSection; },\n/* harmony export */   defaultFAQs: function() { return /* binding */ defaultFAQs; },\n/* harmony export */   defaultPricingPlans: function() { return /* binding */ defaultPricingPlans; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _animations_animate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./animations/animate */ \"./components/animations/animate.js\");\n/* harmony import */ var _ui_enhanced_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/enhanced-button */ \"./components/ui/enhanced-button.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,X!=!lucide-react */ \"__barrel_optimize__?names=Check,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _ui_pixel_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/pixel-card */ \"./components/ui/pixel-card.js\");\n/* __next_internal_client_entry_do_not_use__ PricingCard,defaultPricingPlans,PricingFAQ,defaultFAQs,PricingSection auto */ \n\n\n\n\n\n\n// Pricing card component\nconst PricingCard = (param)=>{\n    let { title, price, description, features, buttonText, buttonLink, highlighted = false, delay = 0 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animations_animate__WEBPACK_IMPORTED_MODULE_1__.Animate, {\n        type: \"slide\",\n        delay: delay,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            className: \"relative \".concat(highlighted ? \"transform scale-105 z-10\" : \"\"),\n            whileHover: {\n                y: highlighted ? -8 : -5,\n                scale: highlighted ? 1.08 : 1.02,\n                transition: {\n                    duration: 0.2\n                }\n            },\n            children: [\n                highlighted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 z-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-theme-purple to-theme-blue text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                        children: \"MOST POPULAR\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 37,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_pixel_card__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    variant: highlighted ? \"highlighted\" : \"default\",\n                    className: \"h-full w-full \".concat(highlighted ? \"border-2 border-theme-purple/50 shadow-2xl shadow-theme-purple/20 bg-gradient-to-br from-theme-purple/5 to-theme-blue/5\" : \"border border-white/10 subtle-glass\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 p-5 md:p-6 flex flex-col h-full z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg md:text-xl font-bold mb-1\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mb-3\",\n                                        children: description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold mb-2\",\n                                        children: price\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-grow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 my-4 text-sm\",\n                                    children: features.slice(0, 6).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                feature.included ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Check, {\n                                                    className: \"h-4 w-4 text-theme-blue shrink-0 mr-2 mt-0.5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__.X, {\n                                                    className: \"h-4 w-4 text-muted-foreground shrink-0 mr-2 mt-0.5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: feature.included ? \"\" : \"text-muted-foreground\",\n                                                    children: feature.text\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                            lineNumber: 63,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: buttonLink,\n                                className: \"mt-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_enhanced_button__WEBPACK_IMPORTED_MODULE_2__.EnhancedButton, {\n                                    size: \"md\",\n                                    color: highlighted ? \"purple\" : \"blue\",\n                                    className: \"w-full \".concat(highlighted ? \"bg-theme-purple text-white hover:bg-theme-purple/90\" : \"bg-transparent border text-foreground dark:border-white/20 dark:hover:border-white/40 border-black/50 hover:border-black/70\"),\n                                    children: buttonText\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PricingCard;\n// Pricing plans data\nconst defaultPricingPlans = [\n    {\n        title: \"Free Plan\",\n        price: \"Free\",\n        description: \"Perfect for trying out our service.\",\n        features: [\n            {\n                text: \"3 free voiceovers\",\n                included: true\n            },\n            {\n                text: \"Basic voice options\",\n                included: true\n            },\n            {\n                text: \"PDF & script upload\",\n                included: true\n            },\n            {\n                text: \"MP4 video generation\",\n                included: true\n            },\n            {\n                text: \"Voice cloning\",\n                included: false\n            },\n            {\n                text: \"Priority support\",\n                included: false\n            }\n        ],\n        buttonText: \"Get Started\",\n        buttonLink: \"/dashboard\",\n        highlighted: false,\n        delay: 0.1\n    },\n    {\n        title: \"Starter Plan\",\n        price: \"$9.99\",\n        description: \"Most popular for regular users.\",\n        features: [\n            {\n                text: \"Unlimited voiceovers\",\n                included: true\n            },\n            {\n                text: \"All voice options\",\n                included: true\n            },\n            {\n                text: \"PDF & script upload\",\n                included: true\n            },\n            {\n                text: \"MP4 video generation\",\n                included: true\n            },\n            {\n                text: \"1 voice clone\",\n                included: true\n            },\n            {\n                text: \"Email support\",\n                included: true\n            }\n        ],\n        buttonText: \"Choose Starter\",\n        buttonLink: \"/\",\n        highlighted: true,\n        delay: 0.2\n    },\n    {\n        title: \"Premium Plan\",\n        price: \"$19.99\",\n        description: \"Best for power users and teams.\",\n        features: [\n            {\n                text: \"Unlimited voiceovers\",\n                included: true\n            },\n            {\n                text: \"All voice options\",\n                included: true\n            },\n            {\n                text: \"PDF & script upload\",\n                included: true\n            },\n            {\n                text: \"MP4 video generation\",\n                included: true\n            },\n            {\n                text: \"5 voice clones\",\n                included: true\n            },\n            {\n                text: \"Priority support\",\n                included: true\n            }\n        ],\n        buttonText: \"Choose Premium\",\n        buttonLink: \"/\",\n        highlighted: false,\n        delay: 0.3\n    }\n];\n// FAQ component\nconst PricingFAQ = (param)=>{\n    let { faqs } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-24 max-w-3xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animations_animate__WEBPACK_IMPORTED_MODULE_1__.Animate, {\n                type: \"fade\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl md:text-3xl font-bold mb-8 text-center\",\n                    children: \"Frequently Asked Questions\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: faqs.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animations_animate__WEBPACK_IMPORTED_MODULE_1__.Animate, {\n                        type: \"slide\",\n                        delay: 0.1 * (index + 1),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"subtle-glass rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-2\",\n                                    children: faq.question\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: faq.answer\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = PricingFAQ;\n// Default FAQ data\nconst defaultFAQs = [\n    {\n        question: \"How do I choose the right plan?\",\n        answer: \"Choose a plan based on your usage needs. If you're just getting started, the Free plan gives you 3 voiceovers to try our service. For regular use, the Starter plan offers unlimited voiceovers and 1 voice clone. For power users who need multiple voice clones, the Premium plan provides 5 voice clones and priority support.\"\n    },\n    {\n        question: \"Can I change my plan at any time?\",\n        answer: \"Yes, you can upgrade or downgrade your plan at any time. Upgrades take effect immediately, while downgrades will take effect at the end of your current billing cycle.\"\n    },\n    {\n        question: \"What is voice cloning and how does it work?\",\n        answer: \"Voice cloning allows you to create custom AI voices based on audio samples. Free users cannot access this feature. Starter plan users can create 1 custom voice, while Premium users can create up to 5 custom voices for their presentations.\"\n    }\n];\n// Main pricing section component\nfunction PricingSection(param) {\n    let { pricingPlans = defaultPricingPlans, faqs = defaultFAQs, showFAQ = true } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-12 md:py-16\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-5xl mx-auto items-center\",\n                children: pricingPlans.map((plan, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(plan.highlighted ? \"md:mt-0\" : \"md:mt-6\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PricingCard, {\n                            ...plan\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this),\n            showFAQ && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PricingFAQ, {\n                faqs: faqs\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 206,\n                columnNumber: 19\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n_c2 = PricingSection;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PricingCard\");\n$RefreshReg$(_c1, \"PricingFAQ\");\n$RefreshReg$(_c2, \"PricingSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/pricing-section.js\n"));

/***/ })

});