/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/pricing";
exports.ids = ["pages/pricing"];
exports.modules = {

/***/ "__barrel_optimize__?names=Check,HelpCircle,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***********************************************************************************************************!*\
  !*** __barrel_optimize__?names=Check,HelpCircle,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Check: () => (/* reexport safe */ _icons_check_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   HelpCircle: () => (/* reexport safe */ _icons_help_circle_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_check_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/check.js */ \"./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _icons_help_circle_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/help-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/help-circle.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGVjayxIZWxwQ2lyY2xlLFghPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFDbUQ7QUFDVyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXByZXNlbnRlci8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzP2ExYzAiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZWNrIH0gZnJvbSBcIi4vaWNvbnMvY2hlY2suanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBIZWxwQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvaGVscC1jaXJjbGUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBYIH0gZnJvbSBcIi4vaWNvbnMveC5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Check,HelpCircle,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Check,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!************************************************************************************************!*\
  !*** __barrel_optimize__?names=Check,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Check: () => (/* reexport safe */ _icons_check_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_check_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/check.js */ \"./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGVjayxYIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUNtRCIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXByZXNlbnRlci8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzP2Y4MjciXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZWNrIH0gZnJvbSBcIi4vaWNvbnMvY2hlY2suanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBYIH0gZnJvbSBcIi4vaWNvbnMveC5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Check,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Github,Heart,Linkedin,Mail,Twitter!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Github,Heart,Linkedin,Mail,Twitter!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Github: () => (/* reexport safe */ _icons_github_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Heart: () => (/* reexport safe */ _icons_heart_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Linkedin: () => (/* reexport safe */ _icons_linkedin_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Mail: () => (/* reexport safe */ _icons_mail_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Twitter: () => (/* reexport safe */ _icons_twitter_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_github_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/github.js */ \"./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _icons_heart_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/heart.js */ \"./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _icons_linkedin_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/linkedin.js */ \"./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _icons_mail_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/mail.js */ \"./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _icons_twitter_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/twitter.js */ \"./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1HaXRodWIsSGVhcnQsTGlua2VkaW4sTWFpbCxUd2l0dGVyIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUNxRDtBQUNGO0FBQ007QUFDUiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXByZXNlbnRlci8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzPzg5ZDQiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEdpdGh1YiB9IGZyb20gXCIuL2ljb25zL2dpdGh1Yi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhlYXJ0IH0gZnJvbSBcIi4vaWNvbnMvaGVhcnQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBMaW5rZWRpbiB9IGZyb20gXCIuL2ljb25zL2xpbmtlZGluLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWFpbCB9IGZyb20gXCIuL2ljb25zL21haWwuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUd2l0dGVyIH0gZnJvbSBcIi4vaWNvbnMvdHdpdHRlci5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Github,Heart,Linkedin,Mail,Twitter!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Github,Linkedin,LogOut,Menu,Twitter,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***********************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Github,Linkedin,LogOut,Menu,Twitter,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Github: () => (/* reexport safe */ _icons_github_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Linkedin: () => (/* reexport safe */ _icons_linkedin_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   LogOut: () => (/* reexport safe */ _icons_log_out_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Menu: () => (/* reexport safe */ _icons_menu_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Twitter: () => (/* reexport safe */ _icons_twitter_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_github_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/github.js */ \"./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _icons_linkedin_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/linkedin.js */ \"./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _icons_log_out_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/log-out.js */ \"./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _icons_menu_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/menu.js */ \"./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _icons_twitter_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/twitter.js */ \"./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1HaXRodWIsTGlua2VkaW4sTG9nT3V0LE1lbnUsVHdpdHRlcixVc2VyLFghPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNxRDtBQUNJO0FBQ0g7QUFDTDtBQUNNO0FBQ04iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wcmVzZW50ZXIvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz81NTJiIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBHaXRodWIgfSBmcm9tIFwiLi9pY29ucy9naXRodWIuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBMaW5rZWRpbiB9IGZyb20gXCIuL2ljb25zL2xpbmtlZGluLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9nT3V0IH0gZnJvbSBcIi4vaWNvbnMvbG9nLW91dC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1lbnUgfSBmcm9tIFwiLi9pY29ucy9tZW51LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVHdpdHRlciB9IGZyb20gXCIuL2ljb25zL3R3aXR0ZXIuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBVc2VyIH0gZnJvbSBcIi4vaWNvbnMvdXNlci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFggfSBmcm9tIFwiLi9pY29ucy94LmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Github,Linkedin,LogOut,Menu,Twitter,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Moon,Sun!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*************************************************************************************************!*\
  !*** __barrel_optimize__?names=Moon,Sun!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Moon: () => (/* reexport safe */ _icons_moon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Sun: () => (/* reexport safe */ _icons_sun_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_moon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/moon.js */ \"./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _icons_sun_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/sun.js */ \"./node_modules/lucide-react/dist/esm/icons/sun.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Nb29uLFN1biE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDaUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wcmVzZW50ZXIvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz9kOWI5Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNb29uIH0gZnJvbSBcIi4vaWNvbnMvbW9vbi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFN1biB9IGZyb20gXCIuL2ljb25zL3N1bi5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Moon,Sun!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpricing&preferredRegion=&absolutePagePath=.%2Fpages%5Cpricing.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpricing&preferredRegion=&absolutePagePath=.%2Fpages%5Cpricing.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _pages_pricing_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\pricing.js */ \"./pages/pricing.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_pricing_js__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_pricing_js__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_pricing_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/pricing\",\n        pathname: \"/pricing\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_pricing_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpricing&preferredRegion=&absolutePagePath=.%2Fpages%5Cpricing.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/animations/Aurora.js":
/*!*****************************************!*\
  !*** ./components/animations/Aurora.js ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Aurora)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ogl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ogl */ \"ogl\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([ogl__WEBPACK_IMPORTED_MODULE_1__]);\nogl__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst VERT = `#version 300 es\r\nin vec2 position;\r\nvoid main() {\r\n  gl_Position = vec4(position, 0.0, 1.0);\r\n}\r\n`;\nconst FRAG = `#version 300 es\r\nprecision highp float;\r\n\r\nuniform float uTime;\r\nuniform float uAmplitude;\r\nuniform vec3 uColorStops[3];\r\nuniform vec2 uResolution;\r\nuniform float uBlend;\r\n\r\nout vec4 fragColor;\r\n\r\nvec3 permute(vec3 x) {\r\n  return mod(((x * 34.0) + 1.0) * x, 289.0);\r\n}\r\n\r\nfloat snoise(vec2 v){\r\n  const vec4 C = vec4(\r\n      0.211324865405187, 0.366025403784439,\r\n      -0.577350269189626, 0.024390243902439\r\n  );\r\n  vec2 i  = floor(v + dot(v, C.yy));\r\n  vec2 x0 = v - i + dot(i, C.xx);\r\n  vec2 i1 = (x0.x > x0.y) ? vec2(1.0, 0.0) : vec2(0.0, 1.0);\r\n  vec4 x12 = x0.xyxy + C.xxzz;\r\n  x12.xy -= i1;\r\n  i = mod(i, 289.0);\r\n\r\n  vec3 p = permute(\r\n      permute(i.y + vec3(0.0, i1.y, 1.0))\r\n    + i.x + vec3(0.0, i1.x, 1.0)\r\n  );\r\n\r\n  vec3 m = max(\r\n      0.5 - vec3(\r\n          dot(x0, x0),\r\n          dot(x12.xy, x12.xy),\r\n          dot(x12.zw, x12.zw)\r\n      ), \r\n      0.0\r\n  );\r\n  m = m * m;\r\n  m = m * m;\r\n\r\n  vec3 x = 2.0 * fract(p * C.www) - 1.0;\r\n  vec3 h = abs(x) - 0.5;\r\n  vec3 ox = floor(x + 0.5);\r\n  vec3 a0 = x - ox;\r\n  m *= 1.79284291400159 - 0.85373472095314 * (a0*a0 + h*h);\r\n\r\n  vec3 g;\r\n  g.x  = a0.x  * x0.x  + h.x  * x0.y;\r\n  g.yz = a0.yz * x12.xz + h.yz * x12.yw;\r\n  return 130.0 * dot(m, g);\r\n}\r\n\r\nstruct ColorStop {\r\n  vec3 color;\r\n  float position;\r\n};\r\n\r\n#define COLOR_RAMP(colors, factor, finalColor) {              \\\r\n  int index = 0;                                            \\\r\n  for (int i = 0; i < 2; i++) {                               \\\r\n     ColorStop currentColor = colors[i];                    \\\r\n     bool isInBetween = currentColor.position <= factor;    \\\r\n     index = int(mix(float(index), float(i), float(isInBetween))); \\\r\n  }                                                         \\\r\n  ColorStop currentColor = colors[index];                   \\\r\n  ColorStop nextColor = colors[index + 1];                  \\\r\n  float range = nextColor.position - currentColor.position; \\\r\n  float lerpFactor = (factor - currentColor.position) / range; \\\r\n  finalColor = mix(currentColor.color, nextColor.color, lerpFactor); \\\r\n}\r\n\r\nvoid main() {\r\n  vec2 uv = gl_FragCoord.xy / uResolution;\r\n  \r\n  ColorStop colors[3];\r\n  colors[0] = ColorStop(uColorStops[0], 0.0);\r\n  colors[1] = ColorStop(uColorStops[1], 0.5);\r\n  colors[2] = ColorStop(uColorStops[2], 1.0);\r\n  \r\n  vec3 rampColor;\r\n  COLOR_RAMP(colors, uv.x, rampColor);\r\n  \r\n  float height = snoise(vec2(uv.x * 2.0 + uTime * 0.1, uTime * 0.25)) * 0.5 * uAmplitude;\r\n  height = exp(height);\r\n  height = (uv.y * 2.0 - height + 0.2);\r\n  float intensity = 0.6 * height;\r\n  \r\n  // midPoint is fixed; uBlend controls the transition width.\r\n  float midPoint = 0.20;\r\n  float auroraAlpha = smoothstep(midPoint - uBlend * 0.5, midPoint + uBlend * 0.5, intensity);\r\n  \r\n  vec3 auroraColor = intensity * rampColor;\r\n  \r\n  // Premultiplied alpha output.\r\n  fragColor = vec4(auroraColor * auroraAlpha, auroraAlpha);\r\n}\r\n`;\nfunction Aurora(props) {\n    const { colorStops = [\n        \"#00d8ff\",\n        \"#7cff67\",\n        \"#00d8ff\"\n    ], amplitude = 1.0, blend = 0.5 } = props;\n    const propsRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(props);\n    propsRef.current = props;\n    const ctnDom = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const ctn = ctnDom.current;\n        if (!ctn) return;\n        const renderer = new ogl__WEBPACK_IMPORTED_MODULE_1__.Renderer({\n            alpha: true,\n            premultipliedAlpha: true,\n            antialias: true\n        });\n        const gl = renderer.gl;\n        gl.clearColor(0, 0, 0, 0);\n        gl.enable(gl.BLEND);\n        gl.blendFunc(gl.ONE, gl.ONE_MINUS_SRC_ALPHA);\n        gl.canvas.style.backgroundColor = \"transparent\";\n        let program;\n        function resize() {\n            if (!ctn) return;\n            const width = ctn.offsetWidth;\n            const height = ctn.offsetHeight;\n            renderer.setSize(width, height);\n            if (program) {\n                program.uniforms.uResolution.value = [\n                    width,\n                    height\n                ];\n            }\n        }\n        window.addEventListener(\"resize\", resize);\n        const geometry = new ogl__WEBPACK_IMPORTED_MODULE_1__.Triangle(gl);\n        if (geometry.attributes.uv) {\n            delete geometry.attributes.uv;\n        }\n        const colorStopsArray = colorStops.map((hex)=>{\n            const c = new ogl__WEBPACK_IMPORTED_MODULE_1__.Color(hex);\n            return [\n                c.r,\n                c.g,\n                c.b\n            ];\n        });\n        program = new ogl__WEBPACK_IMPORTED_MODULE_1__.Program(gl, {\n            vertex: VERT,\n            fragment: FRAG,\n            uniforms: {\n                uTime: {\n                    value: 0\n                },\n                uAmplitude: {\n                    value: amplitude\n                },\n                uColorStops: {\n                    value: colorStopsArray\n                },\n                uResolution: {\n                    value: [\n                        ctn.offsetWidth,\n                        ctn.offsetHeight\n                    ]\n                },\n                uBlend: {\n                    value: blend\n                }\n            }\n        });\n        const mesh = new ogl__WEBPACK_IMPORTED_MODULE_1__.Mesh(gl, {\n            geometry,\n            program\n        });\n        ctn.appendChild(gl.canvas);\n        let animateId = 0;\n        const update = (t)=>{\n            animateId = requestAnimationFrame(update);\n            const { time = t * 0.01, speed = 1.0 } = propsRef.current;\n            program.uniforms.uTime.value = time * speed * 0.1;\n            program.uniforms.uAmplitude.value = propsRef.current.amplitude ?? 1.0;\n            program.uniforms.uBlend.value = propsRef.current.blend ?? blend;\n            const stops = propsRef.current.colorStops ?? colorStops;\n            program.uniforms.uColorStops.value = stops.map((hex)=>{\n                const c = new ogl__WEBPACK_IMPORTED_MODULE_1__.Color(hex);\n                return [\n                    c.r,\n                    c.g,\n                    c.b\n                ];\n            });\n            renderer.render({\n                scene: mesh\n            });\n        };\n        animateId = requestAnimationFrame(update);\n        resize();\n        return ()=>{\n            cancelAnimationFrame(animateId);\n            window.removeEventListener(\"resize\", resize);\n            if (ctn && gl.canvas.parentNode === ctn) {\n                ctn.removeChild(gl.canvas);\n            }\n            gl.getExtension(\"WEBGL_lose_context\")?.loseContext();\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        amplitude\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ctnDom,\n        className: \"w-full h-full\"\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\Aurora.js\",\n        lineNumber: 205,\n        columnNumber: 10\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/animations/Aurora.js\n");

/***/ }),

/***/ "./components/animations/BlurText.js":
/*!*******************************************!*\
  !*** ./components/animations/BlurText.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst buildKeyframes = (from, steps)=>{\n    const keys = new Set([\n        ...Object.keys(from),\n        ...steps.flatMap((s)=>Object.keys(s))\n    ]);\n    const keyframes = {};\n    keys.forEach((k)=>{\n        keyframes[k] = [\n            from[k],\n            ...steps.map((s)=>s[k])\n        ];\n    });\n    return keyframes;\n};\nconst BlurText = ({ text = \"\", delay = 200, className = \"\", animateBy = \"words\", direction = \"top\", threshold = 0.1, rootMargin = \"0px\", animationFrom, animationTo, easing = (t)=>t, onAnimationComplete, stepDuration = 0.35 })=>{\n    const elements = animateBy === \"words\" ? text.split(\" \") : text.split(\"\");\n    const [inView, setInView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 确保组件只在客户端渲染后执行动画\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsMounted(true);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!ref.current || !isMounted) return;\n        const observer = new IntersectionObserver(([entry])=>{\n            if (entry.isIntersecting) {\n                setInView(true);\n                observer.unobserve(ref.current);\n            }\n        }, {\n            threshold,\n            rootMargin\n        });\n        observer.observe(ref.current);\n        return ()=>observer.disconnect();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        threshold,\n        rootMargin,\n        isMounted\n    ]);\n    const defaultFrom = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>direction === \"top\" ? {\n            filter: \"blur(10px)\",\n            opacity: 0,\n            y: -50\n        } : {\n            filter: \"blur(10px)\",\n            opacity: 0,\n            y: 50\n        }, [\n        direction\n    ]);\n    const defaultTo = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>[\n            {\n                filter: \"blur(5px)\",\n                opacity: 0.5,\n                y: direction === \"top\" ? 5 : -5\n            },\n            {\n                filter: \"blur(0px)\",\n                opacity: 1,\n                y: 0\n            }\n        ], [\n        direction\n    ]);\n    const fromSnapshot = animationFrom ?? defaultFrom;\n    const toSnapshots = animationTo ?? defaultTo;\n    const stepCount = toSnapshots.length + 1;\n    const totalDuration = stepDuration * (stepCount - 1);\n    const times = Array.from({\n        length: stepCount\n    }, (_, i)=>stepCount === 1 ? 0 : i / (stepCount - 1));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: `blur-text ${className} flex flex-wrap justify-center`,\n        children: elements.map((segment, index)=>{\n            const animateKeyframes = buildKeyframes(fromSnapshot, toSnapshots);\n            const spanTransition = {\n                duration: totalDuration,\n                times,\n                delay: index * delay / 1000\n            };\n            spanTransition.ease = easing;\n            // 如果组件尚未挂载，使用静态渲染\n            if (!isMounted) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-block\",\n                    children: [\n                        segment === \" \" ? \"\\xa0\" : segment,\n                        animateBy === \"words\" && index < elements.length - 1 && \"\\xa0\"\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\BlurText.js\",\n                    lineNumber: 106,\n                    columnNumber: 13\n                }, undefined);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.span, {\n                className: \"inline-block will-change-[transform,filter,opacity]\",\n                initial: fromSnapshot,\n                animate: inView ? animateKeyframes : fromSnapshot,\n                transition: spanTransition,\n                onAnimationComplete: index === elements.length - 1 ? onAnimationComplete : undefined,\n                children: [\n                    segment === \" \" ? \"\\xa0\" : segment,\n                    animateBy === \"words\" && index < elements.length - 1 && \"\\xa0\"\n                ]\n            }, index, true, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\BlurText.js\",\n                lineNumber: 117,\n                columnNumber: 11\n            }, undefined);\n        })\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\BlurText.js\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BlurText);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/animations/BlurText.js\n");

/***/ }),

/***/ "./components/animations/PricingAnimations.js":
/*!****************************************************!*\
  !*** ./components/animations/PricingAnimations.js ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedFeatureList: () => (/* binding */ AnimatedFeatureList),\n/* harmony export */   AnimatedPriceTag: () => (/* binding */ AnimatedPriceTag),\n/* harmony export */   AnimatedTableCell: () => (/* binding */ AnimatedTableCell),\n/* harmony export */   AnimatedTableRow: () => (/* binding */ AnimatedTableRow),\n/* harmony export */   FloatingElements: () => (/* binding */ FloatingElements),\n/* harmony export */   PulseEffect: () => (/* binding */ PulseEffect)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,X!=!lucide-react */ \"__barrel_optimize__?names=Check,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ FloatingElements,AnimatedPriceTag,AnimatedFeatureList,PulseEffect,AnimatedTableRow,AnimatedTableCell auto */ \n\n\n\n// Floating elements component\nfunction FloatingElements({ count = 10, colors = [\n    \"#3A29FF\",\n    \"#FF94B4\",\n    \"#FF3232\"\n], minSize = 10, maxSize = 40, minOpacity = 0.05, maxOpacity = 0.15, minDuration = 15, maxDuration = 30, className = \"\" }) {\n    const [elements, setElements] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // 确保组件只在客户端渲染后执行\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsMounted(true);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!isMounted) return;\n        // Generate random floating elements\n        const generateElements = ()=>{\n            const newElements = [];\n            for(let i = 0; i < count; i++){\n                const size = Math.floor(Math.random() * (maxSize - minSize) + minSize);\n                const opacity = Math.random() * (maxOpacity - minOpacity) + minOpacity;\n                const duration = Math.random() * (maxDuration - minDuration) + minDuration;\n                const delay = Math.random() * 5;\n                const color = colors[Math.floor(Math.random() * colors.length)];\n                const shape = Math.random() > 0.5 ? \"circle\" : \"square\";\n                // Random position\n                const left = `${Math.random() * 100}%`;\n                const top = `${Math.random() * 100}%`;\n                newElements.push({\n                    id: i,\n                    size,\n                    opacity,\n                    duration,\n                    delay,\n                    color,\n                    shape,\n                    left,\n                    top\n                });\n            }\n            setElements(newElements);\n        };\n        generateElements();\n    }, [\n        count,\n        colors,\n        minSize,\n        maxSize,\n        minOpacity,\n        maxOpacity,\n        minDuration,\n        maxDuration,\n        isMounted\n    ]);\n    // 如果组件尚未挂载，返回一个空的容器\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `absolute inset-0 overflow-hidden pointer-events-none ${className}`\n        }, void 0, false, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n            lineNumber: 65,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `absolute inset-0 overflow-hidden pointer-events-none ${className}`,\n        children: elements.map((element)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                className: \"absolute\",\n                style: {\n                    left: element.left,\n                    top: element.top,\n                    width: element.size,\n                    height: element.size,\n                    backgroundColor: element.color,\n                    opacity: element.opacity,\n                    borderRadius: element.shape === \"circle\" ? \"50%\" : \"20%\"\n                },\n                animate: {\n                    y: [\n                        0,\n                        -30,\n                        0\n                    ],\n                    x: [\n                        0,\n                        15,\n                        0\n                    ],\n                    rotate: [\n                        0,\n                        180,\n                        0\n                    ]\n                },\n                transition: {\n                    duration: element.duration,\n                    delay: element.delay,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, element.id, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n// Price tag animation component\nfunction AnimatedPriceTag({ price, currency = \"$\", period = \"/mo\", isCustom = false, className = \"\" }) {\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsMounted(true);\n    }, []);\n    // 如果组件尚未挂载，使用静态渲染\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `inline-flex items-end ${className}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium mr-1\",\n                    children: currency\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-4xl md:text-5xl font-bold\",\n                    children: price\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this),\n                !isCustom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-muted-foreground ml-1 mb-1\",\n                    children: period\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n                    lineNumber: 120,\n                    columnNumber: 23\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `inline-flex items-end ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-medium mr-1\",\n                children: currency\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-4xl md:text-5xl font-bold\",\n                children: price\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            !isCustom && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm text-muted-foreground ml-1 mb-1\",\n                children: period\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n                lineNumber: 129,\n                columnNumber: 21\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n// Feature list animation component\nfunction AnimatedFeatureList({ features, delay = 0.5 }) {\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsMounted(true);\n    }, []);\n    // 如果组件尚未挂载，使用静态渲染\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n            className: \"space-y-3 my-6\",\n            children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex items-start\",\n                    children: [\n                        feature.included ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                            className: \"h-5 w-5 text-theme-blue shrink-0 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n                            lineNumber: 149,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                            className: \"h-5 w-5 text-muted-foreground shrink-0 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n                            lineNumber: 151,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: feature.included ? \"\" : \"text-muted-foreground\",\n                            children: feature.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        className: \"space-y-3 my-6\",\n        children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.li, {\n                className: \"flex items-start\",\n                initial: {\n                    x: -10,\n                    opacity: 0\n                },\n                animate: {\n                    x: 0,\n                    opacity: 1\n                },\n                transition: {\n                    delay: delay + index * 0.1\n                },\n                children: [\n                    feature.included ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                        className: \"h-5 w-5 text-theme-blue shrink-0 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                        className: \"h-5 w-5 text-muted-foreground shrink-0 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n                        lineNumber: 175,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: feature.included ? \"\" : \"text-muted-foreground\",\n                        children: feature.text\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, index, true, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n                lineNumber: 165,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, this);\n}\n// Pulse animation component\nfunction PulseEffect({ size = 200, color = \"rgba(58, 41, 255, 0.1)\", duration = 2.5, className = \"\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n            className: \"absolute rounded-full\",\n            style: {\n                width: size,\n                height: size,\n                backgroundColor: color,\n                top: `calc(50% - ${size / 2}px)`,\n                left: `calc(50% - ${size / 2}px)`\n            },\n            animate: {\n                scale: [\n                    1,\n                    1.5,\n                    1\n                ],\n                opacity: [\n                    0.1,\n                    0.2,\n                    0.1\n                ]\n            },\n            transition: {\n                duration,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n            }\n        }, void 0, false, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, this);\n}\n// Comparison table row animation component\nfunction AnimatedTableRow({ children, index = 0 }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.tr, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            delay: 0.1 * index,\n            duration: 0.5\n        },\n        className: \"border-b border-white/10\",\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, this);\n}\n// Comparison table cell animation component\nfunction AnimatedTableCell({ children, highlight = false }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.td, {\n        className: `p-4 text-center ${highlight ? \"bg-theme-purple/10\" : \"\"}`,\n        whileHover: {\n            backgroundColor: highlight ? \"rgba(156, 39, 176, 0.2)\" : \"rgba(255, 255, 255, 0.05)\"\n        },\n        transition: {\n            duration: 0.2\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\PricingAnimations.js\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/animations/PricingAnimations.js\n");

/***/ }),

/***/ "./components/animations/animate.js":
/*!******************************************!*\
  !*** ./components/animations/animate.js ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Animate: () => (/* binding */ Animate),\n/* harmony export */   AnimatePresence: () => (/* binding */ AnimatePresence),\n/* harmony export */   useRevealAnimation: () => (/* binding */ useRevealAnimation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ Animate,AnimatePresence,useRevealAnimation auto */ \n\n\nfunction Animate({ children, type = \"fade\", delay = 0, duration = 0.3, className = \"\", threshold = 0.1, once = true }) {\n    const controls = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useAnimation)();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView)(ref, {\n        threshold,\n        once\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (inView) {\n            controls.start(\"visible\");\n        } else if (!once) {\n            controls.start(\"hidden\");\n        }\n    }, [\n        controls,\n        inView,\n        once\n    ]);\n    const variants = {\n        fade: {\n            hidden: {\n                opacity: 0\n            },\n            visible: {\n                opacity: 1,\n                transition: {\n                    duration,\n                    delay\n                }\n            }\n        },\n        slide: {\n            hidden: {\n                opacity: 0,\n                y: 10\n            },\n            visible: {\n                opacity: 1,\n                y: 0,\n                transition: {\n                    duration,\n                    delay\n                }\n            }\n        },\n        scale: {\n            hidden: {\n                opacity: 0,\n                scale: 0.95\n            },\n            visible: {\n                opacity: 1,\n                scale: 1,\n                transition: {\n                    duration,\n                    delay\n                }\n            }\n        },\n        none: {\n            hidden: {},\n            visible: {}\n        }\n    };\n    if (type === \"none\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: className,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\animate.js\",\n            lineNumber: 47,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        ref: ref,\n        initial: \"hidden\",\n        animate: controls,\n        variants: variants[type],\n        className: className,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\animations\\\\animate.js\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\nfunction AnimatePresence({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\nfunction useRevealAnimation() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const revealElements = document.querySelectorAll(\".reveal\");\n        const observer = new IntersectionObserver((entries)=>{\n            entries.forEach((entry)=>{\n                if (entry.isIntersecting) {\n                    entry.target.classList.add(\"active\");\n                }\n            });\n        }, {\n            threshold: 0.1\n        });\n        revealElements.forEach((element)=>{\n            observer.observe(element);\n        });\n        return ()=>{\n            revealElements.forEach((element)=>{\n                observer.unobserve(element);\n            });\n        };\n    }, []);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/animations/animate.js\n");

/***/ }),

/***/ "./components/animations/animation-provider.js":
/*!*****************************************************!*\
  !*** ./components/animations/animation-provider.js ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimationProvider: () => (/* binding */ AnimationProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _animate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./animate */ \"./components/animations/animate.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_animate__WEBPACK_IMPORTED_MODULE_1__]);\n_animate__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ AnimationProvider auto */ \n\nfunction AnimationProvider({ children }) {\n    (0,_animate__WEBPACK_IMPORTED_MODULE_1__.useRevealAnimation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2FuaW1hdGlvbnMvYW5pbWF0aW9uLXByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRThDO0FBRXZDLFNBQVNDLGtCQUFrQixFQUFFQyxRQUFRLEVBQUU7SUFDNUNGLDREQUFrQkE7SUFFbEIscUJBQU87a0JBQUdFOztBQUNaIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcHJlc2VudGVyLy4vY29tcG9uZW50cy9hbmltYXRpb25zL2FuaW1hdGlvbi1wcm92aWRlci5qcz9kZWJmIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHVzZVJldmVhbEFuaW1hdGlvbiB9IGZyb20gXCIuL2FuaW1hdGVcIlxuXG5leHBvcnQgZnVuY3Rpb24gQW5pbWF0aW9uUHJvdmlkZXIoeyBjaGlsZHJlbiB9KSB7XG4gIHVzZVJldmVhbEFuaW1hdGlvbigpXG5cbiAgcmV0dXJuIDw+e2NoaWxkcmVufTwvPlxufVxuIl0sIm5hbWVzIjpbInVzZVJldmVhbEFuaW1hdGlvbiIsIkFuaW1hdGlvblByb3ZpZGVyIiwiY2hpbGRyZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/animations/animation-provider.js\n");

/***/ }),

/***/ "./components/auth/auth-provider.js":
/*!******************************************!*\
  !*** ./components/auth/auth-provider.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n// 初始化 Supabase 客户端\nconst supabaseUrl = \"https://mvdkqcttssakkuqxnqtk.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TZID0xA_7iFS9JIorcRuVcGc0SDzEckK4D4sVuMQ_S8\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(supabaseUrl, supabaseAnonKey);\n// 创建认证上下文\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 获取当前会话\n        const getSession = async ()=>{\n            const { data: { session } } = await supabase.auth.getSession();\n            setUser(session?.user || null);\n            setLoading(false);\n        };\n        getSession();\n        // 监听认证状态变化\n        const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session)=>{\n            setUser(session?.user || null);\n            setLoading(false);\n        });\n        return ()=>{\n            subscription.unsubscribe();\n        };\n    }, []);\n    // 登出函数\n    const signOut = async ()=>{\n        await supabase.auth.signOut();\n    };\n    // 提供认证上下文\n    const value = {\n        user,\n        loading,\n        signOut,\n        supabase\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\auth\\\\auth-provider.js\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n// 自定义钩子，用于访问认证上下文\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/auth/auth-provider.js\n");

/***/ }),

/***/ "./components/footer.js":
/*!******************************!*\
  !*** ./components/footer.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Github_Heart_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Heart,Linkedin,Mail,Twitter!=!lucide-react */ \"__barrel_optimize__?names=Github,Heart,Linkedin,Mail,Twitter!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_2__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\n\n\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    const footerLinks = [\n        {\n            title: \"Product\",\n            links: [\n                {\n                    name: \"Features\",\n                    href: \"/#features\"\n                },\n                {\n                    name: \"Pricing\",\n                    href: \"/pricing\"\n                },\n                {\n                    name: \"FAQ\",\n                    href: \"/#faq\"\n                }\n            ]\n        },\n        {\n            title: \"Resources\",\n            links: [\n                {\n                    name: \"Documentation\",\n                    href: \"#docs\"\n                },\n                {\n                    name: \"Tutorials\",\n                    href: \"#tutorials\"\n                },\n                {\n                    name: \"Blog\",\n                    href: \"#blog\"\n                }\n            ]\n        },\n        {\n            title: \"Company\",\n            links: [\n                {\n                    name: \"About\",\n                    href: \"#about\"\n                },\n                {\n                    name: \"Contact\",\n                    href: \"#contact\"\n                },\n                {\n                    name: \"Privacy\",\n                    href: \"#privacy\"\n                },\n                {\n                    name: \"Terms\",\n                    href: \"#terms\"\n                }\n            ]\n        }\n    ];\n    const socialLinks = [\n        {\n            name: \"GitHub\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Heart_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Github, {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                lineNumber: 39,\n                columnNumber: 29\n            }, this),\n            href: \"https://github.com\"\n        },\n        {\n            name: \"Twitter\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Heart_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Twitter, {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                lineNumber: 40,\n                columnNumber: 30\n            }, this),\n            href: \"https://twitter.com\"\n        },\n        {\n            name: \"LinkedIn\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Heart_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Linkedin, {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                lineNumber: 41,\n                columnNumber: 31\n            }, this),\n            href: \"https://linkedin.com\"\n        },\n        {\n            name: \"Email\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Heart_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Mail, {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                lineNumber: 42,\n                columnNumber: 28\n            }, this),\n            href: \"mailto:<EMAIL>\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"border-t border-white/10 bg-black/20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                            className: \"h-8 w-8 rounded-full bg-gradient-to-br from-theme-blue to-theme-purple\",\n                                            whileHover: {\n                                                scale: 1.1,\n                                                rotate: 5\n                                            },\n                                            transition: {\n                                                type: \"spring\",\n                                                stiffness: 400,\n                                                damping: 10\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold gradient-text\",\n                                            children: \"AI Presenter\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground mb-6 max-w-md\",\n                                    children: \"Create professional voiceovers for your presentations using advanced AI technology. Save time and enhance your content with natural-sounding voices.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: socialLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: link.href,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                            \"aria-label\": link.name,\n                                            children: link.icon\n                                        }, link.name, false, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        footerLinks.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold mb-4\",\n                                        children: section.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: section.links.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, link.name, false, {\n                                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, section.title, true, {\n                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-white/10 mt-12 pt-6 flex flex-col md:flex-row justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground mb-4 md:mb-0\",\n                            children: [\n                                \"\\xa9 \",\n                                currentYear,\n                                \" AI Presenter. All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground flex items-center\",\n                            children: [\n                                \"Made with \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Heart_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Heart, {\n                                    className: \"h-4 w-4 mx-1 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                                    lineNumber: 104,\n                                    columnNumber: 23\n                                }, this),\n                                \" by AI Presenter Team\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\footer.js\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./components/footer.js\n");

/***/ }),

/***/ "./components/header.js":
/*!******************************!*\
  !*** ./components/header.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_LogOut_Menu_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,LogOut,Menu,Twitter,User,X!=!lucide-react */ \"__barrel_optimize__?names=Github,Linkedin,LogOut,Menu,Twitter,User,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _components_mode_toggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/mode-toggle */ \"./components/mode-toggle.js\");\n/* harmony import */ var _components_ui_enhanced_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/enhanced-button */ \"./components/ui/enhanced-button.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.js\");\n/* harmony import */ var _components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/auth/auth-provider */ \"./components/auth/auth-provider.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_8__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_3__, _components_mode_toggle__WEBPACK_IMPORTED_MODULE_4__, _components_ui_enhanced_button__WEBPACK_IMPORTED_MODULE_5__, _lib_utils__WEBPACK_IMPORTED_MODULE_6__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_3__, _components_mode_toggle__WEBPACK_IMPORTED_MODULE_4__, _components_ui_enhanced_button__WEBPACK_IMPORTED_MODULE_5__, _lib_utils__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\n\n\n\n\nfunction Header() {\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUserMenuOpen, setIsUserMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, signOut } = (0,_components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    // Handle scroll event to change header appearance\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 10);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // Close mobile menu when window is resized to desktop size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            if (window.innerWidth >= 768) {\n                setIsMobileMenuOpen(false);\n            }\n        };\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    // Prevent body scroll when mobile menu is open\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobileMenuOpen) {\n            document.body.style.overflow = \"hidden\";\n        } else {\n            document.body.style.overflow = \"\";\n        }\n        return ()=>{\n            document.body.style.overflow = \"\";\n        };\n    }, [\n        isMobileMenuOpen\n    ]);\n    const toggleMobileMenu = ()=>{\n        setIsMobileMenuOpen(!isMobileMenuOpen);\n    };\n    const navItems = [\n        {\n            name: \"Home\",\n            href: \"/\"\n        },\n        {\n            name: \"Features\",\n            href: \"/#features\"\n        },\n        {\n            name: \"Pricing\",\n            href: \"/pricing\"\n        },\n        {\n            name: \"About\",\n            href: \"/#about\"\n        }\n    ];\n    const socialLinks = [\n        {\n            name: \"GitHub\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_LogOut_Menu_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Github, {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                lineNumber: 64,\n                columnNumber: 29\n            }, this),\n            href: \"https://github.com\"\n        },\n        {\n            name: \"Twitter\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_LogOut_Menu_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Twitter, {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                lineNumber: 65,\n                columnNumber: 30\n            }, this),\n            href: \"https://twitter.com\"\n        },\n        {\n            name: \"LinkedIn\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_LogOut_Menu_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Linkedin, {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                lineNumber: 66,\n                columnNumber: 31\n            }, this),\n            href: \"https://linkedin.com\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"fixed top-0 left-0 right-0 z-50 transition-all duration-300\", isScrolled ? \"bg-background/80 backdrop-blur-md shadow-sm py-2\" : \"bg-transparent py-4\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: \"h-8 w-8 rounded-full bg-gradient-to-br from-theme-blue to-theme-purple\",\n                                    whileHover: {\n                                        scale: 1.1,\n                                        rotate: 5\n                                    },\n                                    transition: {\n                                        type: \"spring\",\n                                        stiffness: 400,\n                                        damping: 10\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold gradient-text\",\n                                    children: \"AI Presenter\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"flex space-x-6\",\n                                children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: \"nav-link text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                            lineNumber: 95,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, item.name, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-4\",\n                                    children: [\n                                        socialLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: link.href,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                                \"aria-label\": link.name,\n                                                children: link.icon\n                                            }, link.name, false, {\n                                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mode_toggle__WEBPACK_IMPORTED_MODULE_4__.ModeToggle, {}, void 0, false, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setIsUserMenuOpen(!isUserMenuOpen),\n                                                    className: \"flex items-center space-x-2 p-2 rounded-full hover:bg-accent transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 rounded-full bg-primary/20 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_LogOut_Menu_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__.User, {\n                                                            className: \"h-4 w-4 text-primary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, this),\n                                                isUserMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 mt-2 w-48 bg-card border border-border rounded-md shadow-lg py-1 z-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-4 py-2 border-b border-border\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                            href: \"/dashboard\",\n                                                            className: \"block px-4 py-2 text-sm hover:bg-accent transition-colors\",\n                                                            onClick: ()=>setIsUserMenuOpen(false),\n                                                            children: \"Dashboard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                signOut();\n                                                                setIsUserMenuOpen(false);\n                                                            },\n                                                            className: \"flex items-center w-full text-left px-4 py-2 text-sm text-destructive hover:bg-destructive/10 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_LogOut_Menu_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__.LogOut, {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                                                    lineNumber: 152,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Sign Out\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_enhanced_button__WEBPACK_IMPORTED_MODULE_5__.EnhancedButton, {\n                                            size: \"sm\",\n                                            color: \"blue\",\n                                            className: \"bg-white text-black hover:bg-white/90\",\n                                            onClick: ()=>router.push(\"/login\"),\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center md:hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mode_toggle__WEBPACK_IMPORTED_MODULE_4__.ModeToggle, {}, void 0, false, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: toggleMobileMenu,\n                                            className: \"ml-2 p-2 rounded-md text-muted-foreground hover:text-foreground transition-colors\",\n                                            \"aria-label\": \"Toggle menu\",\n                                            children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_LogOut_Menu_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__.X, {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 37\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_LogOut_Menu_Twitter_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Menu, {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 65\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                children: isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"fixed inset-0 z-40 bg-background/95 backdrop-blur-md md:hidden\",\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex flex-col items-center space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"flex flex-col items-center space-y-6\",\n                                    children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: \"text-xl font-medium hover:text-theme-blue transition-colors\",\n                                                onClick: ()=>setIsMobileMenuOpen(false),\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                                lineNumber: 200,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, item.name, false, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                            lineNumber: 199,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                    lineNumber: 197,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 mt-8\",\n                                    children: socialLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: link.href,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-muted-foreground hover:text-foreground transition-colors\",\n                                            \"aria-label\": link.name,\n                                            children: link.icon\n                                        }, link.name, false, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                            lineNumber: 213,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                    lineNumber: 211,\n                                    columnNumber: 17\n                                }, this),\n                                user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_enhanced_button__WEBPACK_IMPORTED_MODULE_5__.EnhancedButton, {\n                                            size: \"lg\",\n                                            color: \"blue\",\n                                            className: \"w-full mt-8 bg-white text-black hover:bg-white/90\",\n                                            onClick: ()=>{\n                                                router.push(\"/dashboard\");\n                                                setIsMobileMenuOpen(false);\n                                            },\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                            lineNumber: 228,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_enhanced_button__WEBPACK_IMPORTED_MODULE_5__.EnhancedButton, {\n                                            size: \"lg\",\n                                            variant: \"outline\",\n                                            color: \"purple\",\n                                            className: \"w-full border-white/10 text-white hover:text-white hover:border-white/20\",\n                                            onClick: ()=>{\n                                                signOut();\n                                                setIsMobileMenuOpen(false);\n                                            },\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                            lineNumber: 239,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                    lineNumber: 227,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_enhanced_button__WEBPACK_IMPORTED_MODULE_5__.EnhancedButton, {\n                                    size: \"lg\",\n                                    color: \"blue\",\n                                    className: \"w-full mt-8 bg-white text-black hover:bg-white/90\",\n                                    onClick: ()=>{\n                                        router.push(\"/login\");\n                                        setIsMobileMenuOpen(false);\n                                    },\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                                    lineNumber: 253,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                            lineNumber: 196,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                        lineNumber: 195,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                    lineNumber: 188,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\header.js\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/header.js\n");

/***/ }),

/***/ "./components/mode-toggle.js":
/*!***********************************!*\
  !*** ./components/mode-toggle.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModeToggle: () => (/* binding */ ModeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Moon,Sun!=!lucide-react */ \"__barrel_optimize__?names=Moon,Sun!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_3__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ ModeToggle auto */ \n\n\n\n\nfunction ModeToggle() {\n    const { theme, setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    // Ensure component is mounted before accessing theme\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            className: \"p-2 rounded-md bg-transparent text-muted-foreground\",\n            disabled: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Sun, {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\mode-toggle.js\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\mode-toggle.js\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this);\n    }\n    const toggleTheme = ()=>{\n        setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n        whileTap: {\n            scale: 0.9\n        },\n        whileHover: {\n            scale: 1.1\n        },\n        transition: {\n            type: \"spring\",\n            stiffness: 400,\n            damping: 17\n        },\n        onClick: toggleTheme,\n        className: \"p-2 rounded-md bg-transparent text-muted-foreground hover:text-foreground transition-colors\",\n        \"aria-label\": \"Toggle theme\",\n        children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Sun, {\n            className: \"h-5 w-5 transition-transform duration-200 rotate-0\"\n        }, void 0, false, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\mode-toggle.js\",\n            lineNumber: 42,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Moon, {\n            className: \"h-5 w-5 transition-transform duration-200 rotate-0\"\n        }, void 0, false, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\mode-toggle.js\",\n            lineNumber: 44,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\mode-toggle.js\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/mode-toggle.js\n");

/***/ }),

/***/ "./components/page-header.js":
/*!***********************************!*\
  !*** ./components/page-header.js ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageHeader: () => (/* binding */ PageHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _components_animations_BlurText__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/animations/BlurText */ \"./components/animations/BlurText.js\");\n/* harmony import */ var _components_animations_Aurora__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/animations/Aurora */ \"./components/animations/Aurora.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__, _components_animations_BlurText__WEBPACK_IMPORTED_MODULE_2__, _components_animations_Aurora__WEBPACK_IMPORTED_MODULE_3__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_1__, _components_animations_BlurText__WEBPACK_IMPORTED_MODULE_2__, _components_animations_Aurora__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ PageHeader auto */ \n\n\n\nfunction PageHeader({ title, subtitle = null, titleClassName = \"text-2xl mb-6 text-4xl md:text-6xl font-bold\", subtitleClassName = \"text-xl md:text-2xl text-muted-foreground max-w-3xl mb-8 text-center\", height = \"500px\", children = null, colorStops = [\n    \"#3A29FF\",\n    \"#FF94B4\",\n    \"#FF3232\"\n], blend = 0.5, amplitude = 1.0, speed = 0.5 }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative overflow-hidden\",\n        style: {\n            height\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_Aurora__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    colorStops: colorStops,\n                    blend: blend,\n                    amplitude: amplitude,\n                    speed: speed\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\page-header.js\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\page-header.js\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container relative mx-auto px-4 h-full flex items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center text-center max-w-3xl mx-auto w-full\",\n                    children: [\n                        title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_BlurText__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            text: title,\n                            delay: 150,\n                            animateBy: \"words\",\n                            direction: \"top\",\n                            className: titleClassName\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\page-header.js\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this),\n                        subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_BlurText__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            text: subtitle,\n                            delay: 100,\n                            stepDuration: 0.5,\n                            animateBy: \"words\",\n                            direction: \"top\",\n                            className: subtitleClassName\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\page-header.js\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\page-header.js\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\page-header.js\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\page-header.js\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/page-header.js\n");

/***/ }),

/***/ "./components/pricing-comparison-table.js":
/*!************************************************!*\
  !*** ./components/pricing-comparison-table.js ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PricingComparisonTable: () => (/* binding */ PricingComparisonTable),\n/* harmony export */   defaultPricingPlans: () => (/* binding */ defaultPricingPlans)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_Check_HelpCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,HelpCircle,X!=!lucide-react */ \"__barrel_optimize__?names=Check,HelpCircle,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _animations_PricingAnimations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./animations/PricingAnimations */ \"./components/animations/PricingAnimations.js\");\n/* harmony import */ var _ui_enhanced_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/enhanced-button */ \"./components/ui/enhanced-button.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__, _animations_PricingAnimations__WEBPACK_IMPORTED_MODULE_2__, _ui_enhanced_button__WEBPACK_IMPORTED_MODULE_3__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_1__, _animations_PricingAnimations__WEBPACK_IMPORTED_MODULE_2__, _ui_enhanced_button__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ PricingComparisonTable,defaultPricingPlans auto */ \n\n\n\n\n\n// Pricing comparison table component\nfunction PricingComparisonTable({ plans = defaultPricingPlans }) {\n    // Extract all features from all plans\n    const allFeatures = plans.reduce((acc, plan)=>{\n        plan.features.forEach((feature)=>{\n            if (!acc.some((f)=>f.text === feature.text)) {\n                acc.push(feature);\n            }\n        });\n        return acc;\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.table, {\n            className: \"w-full border-collapse text-sm\",\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"p-3 text-left border-b border-white/10\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, this),\n                            plans.map((plan, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    className: `p-3 text-center border-b border-white/10 ${plan.highlighted ? \"bg-theme-purple/10\" : \"\"}`,\n                                    children: plan.title\n                                }, index, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                    lineNumber: 33,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                    children: [\n                        allFeatures.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animations_PricingAnimations__WEBPACK_IMPORTED_MODULE_2__.AnimatedTableRow, {\n                                index: featureIndex,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"p-3 text-left\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: feature.text\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 19\n                                                }, this),\n                                                feature.tooltip && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                    className: \"relative ml-2 cursor-help group\",\n                                                    whileHover: {\n                                                        scale: 1.1\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_HelpCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.HelpCircle, {\n                                                            className: \"h-3 w-3 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                                            lineNumber: 55,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 p-2 bg-black/80 rounded-md text-xs text-white opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-50\",\n                                                            children: feature.tooltip\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                                            lineNumber: 56,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this),\n                                    plans.map((plan, planIndex)=>{\n                                        const planFeature = plan.features.find((f)=>f.text === feature.text);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animations_PricingAnimations__WEBPACK_IMPORTED_MODULE_2__.AnimatedTableCell, {\n                                            highlight: plan.highlighted,\n                                            children: planFeature?.value ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: planFeature.value\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                                lineNumber: 71,\n                                                columnNumber: 23\n                                            }, this) : planFeature?.included ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_HelpCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Check, {\n                                                className: \"h-4 w-4 text-theme-blue mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                                lineNumber: 73,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_HelpCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.X, {\n                                                className: \"h-4 w-4 text-muted-foreground mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                                lineNumber: 75,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, planIndex, false, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                            lineNumber: 66,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                ]\n                            }, featureIndex, true, {\n                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animations_PricingAnimations__WEBPACK_IMPORTED_MODULE_2__.AnimatedTableRow, {\n                            index: allFeatures.length,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"p-3 text-left\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                plans.map((plan, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: `p-3 text-center ${plan.highlighted ? \"bg-theme-purple/10\" : \"\"}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: plan.buttonLink,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_enhanced_button__WEBPACK_IMPORTED_MODULE_3__.EnhancedButton, {\n                                                size: \"sm\",\n                                                color: plan.highlighted ? \"purple\" : \"blue\",\n                                                className: `w-full ${plan.highlighted ? \"bg-theme-purple text-white hover:bg-theme-purple/90\" : \"bg-transparent border text-foreground dark:border-white/20 dark:hover:border-white/40 border-black/50 hover:border-black/70\"}`,\n                                                children: plan.buttonText\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                                lineNumber: 92,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-comparison-table.js\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n// Default pricing plans data\nconst defaultPricingPlans = [\n    {\n        title: \"Free Plan\",\n        price: \"Free\",\n        description: \"Perfect for trying out our service.\",\n        features: [\n            {\n                text: \"Voiceover generations\",\n                included: true,\n                value: \"3\"\n            },\n            {\n                text: \"Basic voice options\",\n                included: true\n            },\n            {\n                text: \"PDF & script upload\",\n                included: true\n            },\n            {\n                text: \"MP4 video generation\",\n                included: true\n            },\n            {\n                text: \"Voice cloning\",\n                included: false\n            },\n            {\n                text: \"Priority support\",\n                included: false\n            }\n        ],\n        buttonText: \"Get Started\",\n        buttonLink: \"/dashboard\",\n        highlighted: false\n    },\n    {\n        title: \"Starter Plan\",\n        price: \"$9.99\",\n        description: \"Most popular for regular users.\",\n        features: [\n            {\n                text: \"Voiceover generations\",\n                included: true,\n                value: \"Unlimited\"\n            },\n            {\n                text: \"All voice options\",\n                included: true\n            },\n            {\n                text: \"PDF & script upload\",\n                included: true\n            },\n            {\n                text: \"MP4 video generation\",\n                included: true\n            },\n            {\n                text: \"Voice cloning\",\n                included: true,\n                value: \"1 clone\"\n            },\n            {\n                text: \"Email support\",\n                included: true\n            }\n        ],\n        buttonText: \"Choose Starter\",\n        buttonLink: \"/\",\n        highlighted: true\n    },\n    {\n        title: \"Premium Plan\",\n        price: \"$19.99\",\n        description: \"Best for power users and teams.\",\n        features: [\n            {\n                text: \"Voiceover generations\",\n                included: true,\n                value: \"Unlimited\"\n            },\n            {\n                text: \"All voice options\",\n                included: true\n            },\n            {\n                text: \"PDF & script upload\",\n                included: true\n            },\n            {\n                text: \"MP4 video generation\",\n                included: true\n            },\n            {\n                text: \"Voice cloning\",\n                included: true,\n                value: \"5 clones\"\n            },\n            {\n                text: \"Priority support\",\n                included: true\n            }\n        ],\n        buttonText: \"Choose Premium\",\n        buttonLink: \"/\",\n        highlighted: false\n    }\n];\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/pricing-comparison-table.js\n");

/***/ }),

/***/ "./components/pricing-section.js":
/*!***************************************!*\
  !*** ./components/pricing-section.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PricingCard: () => (/* binding */ PricingCard),\n/* harmony export */   PricingFAQ: () => (/* binding */ PricingFAQ),\n/* harmony export */   PricingSection: () => (/* binding */ PricingSection),\n/* harmony export */   defaultFAQs: () => (/* binding */ defaultFAQs),\n/* harmony export */   defaultPricingPlans: () => (/* binding */ defaultPricingPlans)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _animations_animate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./animations/animate */ \"./components/animations/animate.js\");\n/* harmony import */ var _ui_enhanced_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/enhanced-button */ \"./components/ui/enhanced-button.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,X!=!lucide-react */ \"__barrel_optimize__?names=Check,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _ui_pixel_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/pixel-card */ \"./components/ui/pixel-card.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_animations_animate__WEBPACK_IMPORTED_MODULE_1__, _ui_enhanced_button__WEBPACK_IMPORTED_MODULE_2__, framer_motion__WEBPACK_IMPORTED_MODULE_3__]);\n([_animations_animate__WEBPACK_IMPORTED_MODULE_1__, _ui_enhanced_button__WEBPACK_IMPORTED_MODULE_2__, framer_motion__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ PricingCard,defaultPricingPlans,PricingFAQ,defaultFAQs,PricingSection auto */ \n\n\n\n\n\n\n// Pricing card component\nconst PricingCard = ({ title, price, description, features, buttonText, buttonLink, highlighted = false, delay = 0 })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animations_animate__WEBPACK_IMPORTED_MODULE_1__.Animate, {\n        type: \"slide\",\n        delay: delay,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            className: `relative ${highlighted ? \"transform scale-105 z-10\" : \"\"}`,\n            whileHover: {\n                y: highlighted ? -8 : -5,\n                scale: highlighted ? 1.08 : 1.02,\n                transition: {\n                    duration: 0.2\n                }\n            },\n            children: [\n                highlighted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute -top-3 left-1/2 transform -translate-x-1/2 z-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-theme-purple to-theme-blue text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                        children: \"MOST POPULAR\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 37,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_pixel_card__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    variant: highlighted ? \"highlighted\" : \"default\",\n                    className: `h-full w-full ${highlighted ? \"border-2 border-theme-purple/50 shadow-2xl shadow-theme-purple/20 bg-gradient-to-br from-theme-purple/5 to-theme-blue/5\" : \"border border-white/10 subtle-glass\"}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 p-5 md:p-6 flex flex-col h-full z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg md:text-xl font-bold mb-1\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mb-3\",\n                                        children: description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold mb-2\",\n                                        children: price\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-grow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 my-4 text-sm\",\n                                    children: features.slice(0, 6).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                feature.included ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Check, {\n                                                    className: \"h-4 w-4 text-theme-blue shrink-0 mr-2 mt-0.5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__.X, {\n                                                    className: \"h-4 w-4 text-muted-foreground shrink-0 mr-2 mt-0.5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: feature.included ? \"\" : \"text-muted-foreground\",\n                                                    children: feature.text\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                            lineNumber: 63,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: buttonLink,\n                                className: \"mt-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_enhanced_button__WEBPACK_IMPORTED_MODULE_2__.EnhancedButton, {\n                                    size: \"md\",\n                                    color: highlighted ? \"purple\" : \"blue\",\n                                    className: `w-full ${highlighted ? \"bg-theme-purple text-white hover:bg-theme-purple/90\" : \"bg-transparent border text-foreground dark:border-white/20 dark:hover:border-white/40 border-black/50 hover:border-black/70\"}`,\n                                    children: buttonText\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n// Pricing plans data\nconst defaultPricingPlans = [\n    {\n        title: \"Free Plan\",\n        price: \"Free\",\n        description: \"Perfect for trying out our service.\",\n        features: [\n            {\n                text: \"3 free voiceovers\",\n                included: true\n            },\n            {\n                text: \"Basic voice options\",\n                included: true\n            },\n            {\n                text: \"PDF & script upload\",\n                included: true\n            },\n            {\n                text: \"MP4 video generation\",\n                included: true\n            },\n            {\n                text: \"Voice cloning\",\n                included: false\n            },\n            {\n                text: \"Priority support\",\n                included: false\n            }\n        ],\n        buttonText: \"Get Started\",\n        buttonLink: \"/dashboard\",\n        highlighted: false,\n        delay: 0.1\n    },\n    {\n        title: \"Starter Plan\",\n        price: \"$9.99\",\n        description: \"Most popular for regular users.\",\n        features: [\n            {\n                text: \"Unlimited voiceovers\",\n                included: true\n            },\n            {\n                text: \"All voice options\",\n                included: true\n            },\n            {\n                text: \"PDF & script upload\",\n                included: true\n            },\n            {\n                text: \"MP4 video generation\",\n                included: true\n            },\n            {\n                text: \"1 voice clone\",\n                included: true\n            },\n            {\n                text: \"Email support\",\n                included: true\n            }\n        ],\n        buttonText: \"Choose Starter\",\n        buttonLink: \"/\",\n        highlighted: true,\n        delay: 0.2\n    },\n    {\n        title: \"Premium Plan\",\n        price: \"$19.99\",\n        description: \"Best for power users and teams.\",\n        features: [\n            {\n                text: \"Unlimited voiceovers\",\n                included: true\n            },\n            {\n                text: \"All voice options\",\n                included: true\n            },\n            {\n                text: \"PDF & script upload\",\n                included: true\n            },\n            {\n                text: \"MP4 video generation\",\n                included: true\n            },\n            {\n                text: \"5 voice clones\",\n                included: true\n            },\n            {\n                text: \"Priority support\",\n                included: true\n            }\n        ],\n        buttonText: \"Choose Premium\",\n        buttonLink: \"/\",\n        highlighted: false,\n        delay: 0.3\n    }\n];\n// FAQ component\nconst PricingFAQ = ({ faqs })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-24 max-w-3xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animations_animate__WEBPACK_IMPORTED_MODULE_1__.Animate, {\n                type: \"fade\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl md:text-3xl font-bold mb-8 text-center\",\n                    children: \"Frequently Asked Questions\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: faqs.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animations_animate__WEBPACK_IMPORTED_MODULE_1__.Animate, {\n                        type: \"slide\",\n                        delay: 0.1 * (index + 1),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"subtle-glass rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-2\",\n                                    children: faq.question\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: faq.answer\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\n// Default FAQ data\nconst defaultFAQs = [\n    {\n        question: \"How do I choose the right plan?\",\n        answer: \"Choose a plan based on your usage needs. If you're just getting started, the Free plan gives you 3 voiceovers to try our service. For regular use, the Starter plan offers unlimited voiceovers and 1 voice clone. For power users who need multiple voice clones, the Premium plan provides 5 voice clones and priority support.\"\n    },\n    {\n        question: \"Can I change my plan at any time?\",\n        answer: \"Yes, you can upgrade or downgrade your plan at any time. Upgrades take effect immediately, while downgrades will take effect at the end of your current billing cycle.\"\n    },\n    {\n        question: \"What is voice cloning and how does it work?\",\n        answer: \"Voice cloning allows you to create custom AI voices based on audio samples. Free users cannot access this feature. Starter plan users can create 1 custom voice, while Premium users can create up to 5 custom voices for their presentations.\"\n    }\n];\n// Main pricing section component\nfunction PricingSection({ pricingPlans = defaultPricingPlans, faqs = defaultFAQs, showFAQ = true }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-12 md:py-16\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-5xl mx-auto items-center\",\n                children: pricingPlans.map((plan, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${plan.highlighted ? \"md:mt-0\" : \"md:mt-6\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PricingCard, {\n                            ...plan\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this),\n            showFAQ && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PricingFAQ, {\n                faqs: faqs\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 206,\n                columnNumber: 19\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/pricing-section.js\n");

/***/ }),

/***/ "./components/subtle-background.js":
/*!*****************************************!*\
  !*** ./components/subtle-background.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SubtleBackground: () => (/* binding */ SubtleBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ SubtleBackground auto */ \n\nfunction SubtleBackground() {\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return;\n        const ctx = canvas.getContext(\"2d\");\n        const isDarkMode = document.documentElement.classList.contains(\"dark\");\n        // Set canvas dimensions\n        const resizeCanvas = ()=>{\n            canvas.width = window.innerWidth;\n            canvas.height = window.innerHeight;\n        };\n        resizeCanvas();\n        window.addEventListener(\"resize\", resizeCanvas);\n        // Create particles\n        const particlesArray = [];\n        const numberOfParticles = Math.min(50, Math.floor(window.innerWidth / 30));\n        class Particle {\n            constructor(){\n                this.x = Math.random() * canvas.width;\n                this.y = Math.random() * canvas.height;\n                this.size = Math.random() * 2 + 0.5;\n                this.speedX = Math.random() * 0.5 - 0.25;\n                this.speedY = Math.random() * 0.5 - 0.25;\n                this.color = isDarkMode ? \"rgba(255, 255, 255, 0.05)\" : \"rgba(0, 0, 0, 0.05)\";\n            }\n            update() {\n                this.x += this.speedX;\n                this.y += this.speedY;\n                if (this.x > canvas.width) this.x = 0;\n                else if (this.x < 0) this.x = canvas.width;\n                if (this.y > canvas.height) this.y = 0;\n                else if (this.y < 0) this.y = canvas.height;\n            }\n            draw() {\n                ctx.fillStyle = this.color;\n                ctx.beginPath();\n                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);\n                ctx.fill();\n            }\n        }\n        function init() {\n            for(let i = 0; i < numberOfParticles; i++){\n                particlesArray.push(new Particle());\n            }\n        }\n        function animate() {\n            ctx.clearRect(0, 0, canvas.width, canvas.height);\n            for(let i = 0; i < particlesArray.length; i++){\n                particlesArray[i].update();\n                particlesArray[i].draw();\n            }\n            requestAnimationFrame(animate);\n        }\n        init();\n        animate();\n        return ()=>{\n            window.removeEventListener(\"resize\", resizeCanvas);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        className: \"fixed inset-0 -z-10 opacity-50 pointer-events-none\",\n        style: {\n            background: \"transparent\"\n        }\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\subtle-background.js\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/subtle-background.js\n");

/***/ }),

/***/ "./components/theme-provider.js":
/*!**************************************!*\
  !*** ./components/theme-provider.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    // Ensure theme transitions only happen after hydration\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: mounted ? \"animate-theme-transition\" : \"opacity-0\",\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\theme-provider.js\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\theme-provider.js\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRTFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQU87SUFDbEQsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUdOLDJDQUFjLENBQUM7SUFFN0MsdURBQXVEO0lBQ3ZEQSw0Q0FBZSxDQUFDO1FBQ2RNLFdBQVc7SUFDYixHQUFHLEVBQUU7SUFFTCxxQkFDRSw4REFBQ0osc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQzNCLDRFQUFDSztZQUFJQyxXQUFXTCxVQUFVLDZCQUE2QjtzQkFBY0Y7Ozs7Ozs7Ozs7O0FBRzNFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcHJlc2VudGVyLy4vY29tcG9uZW50cy90aGVtZS1wcm92aWRlci5qcz9kZTcxIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlciB9IGZyb20gXCJuZXh0LXRoZW1lc1wiXG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH0pIHtcbiAgY29uc3QgW21vdW50ZWQsIHNldE1vdW50ZWRdID0gUmVhY3QudXNlU3RhdGUoZmFsc2UpXG5cbiAgLy8gRW5zdXJlIHRoZW1lIHRyYW5zaXRpb25zIG9ubHkgaGFwcGVuIGFmdGVyIGh5ZHJhdGlvblxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldE1vdW50ZWQodHJ1ZSlcbiAgfSwgW10pXG5cbiAgcmV0dXJuIChcbiAgICA8TmV4dFRoZW1lc1Byb3ZpZGVyIHsuLi5wcm9wc30+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT17bW91bnRlZCA/IFwiYW5pbWF0ZS10aGVtZS10cmFuc2l0aW9uXCIgOiBcIm9wYWNpdHktMFwifT57Y2hpbGRyZW59PC9kaXY+XG4gICAgPC9OZXh0VGhlbWVzUHJvdmlkZXI+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIiwibW91bnRlZCIsInNldE1vdW50ZWQiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/theme-provider.js\n");

/***/ }),

/***/ "./components/ui/button.js":
/*!*********************************!*\
  !*** ./components/ui/button.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ Button,buttonVariants auto */ \n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"bg-transparent hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\button.js\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ui/button.js\n");

/***/ }),

/***/ "./components/ui/enhanced-button.js":
/*!******************************************!*\
  !*** ./components/ui/enhanced-button.js ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedButton: () => (/* binding */ EnhancedButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./lib/utils.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"./components/ui/button.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__, _components_ui_button__WEBPACK_IMPORTED_MODULE_4__]);\n([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__, _components_ui_button__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ EnhancedButton auto */ \n\n\n\n\nconst enhancedButtonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative overflow-hidden transition-all\", {\n    variants: {\n        variant: {\n            default: \"flow-border glow-effect\",\n            outline: \"flow-border\",\n            ghost: \"\",\n            link: \"\"\n        },\n        color: {\n            default: \"\",\n            blue: \"glow-effect\",\n            purple: \"glow-effect purple\",\n            teal: \"glow-effect teal\"\n        },\n        size: {\n            default: \"\",\n            sm: \"\",\n            lg: \"\",\n            icon: \"\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        color: \"default\",\n        size: \"default\"\n    }\n});\nconst EnhancedButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, color, size, loading, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(enhancedButtonVariants({\n            variant,\n            color,\n            size\n        }), className),\n        ref: ref,\n        disabled: loading || props.disabled,\n        ...props,\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"animate-rotate-loader -ml-1 mr-2 h-4 w-4\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            className: \"opacity-25\",\n                            cx: \"12\",\n                            cy: \"12\",\n                            r: \"10\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\enhanced-button.js\",\n                            lineNumber: 53,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            className: \"opacity-75\",\n                            fill: \"currentColor\",\n                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\enhanced-button.js\",\n                            lineNumber: 54,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\enhanced-button.js\",\n                    lineNumber: 47,\n                    columnNumber: 13\n                }, undefined),\n                \"Loading...\"\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\enhanced-button.js\",\n            lineNumber: 46,\n            columnNumber: 11\n        }, undefined) : children\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\enhanced-button.js\",\n        lineNumber: 39,\n        columnNumber: 7\n    }, undefined);\n});\nEnhancedButton.displayName = \"EnhancedButton\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ui/enhanced-button.js\n");

/***/ }),

/***/ "./components/ui/pixel-card.js":
/*!*************************************!*\
  !*** ./components/ui/pixel-card.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PixelCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nclass Pixel {\n    constructor(canvas, context, x, y, color, speed, delay){\n        this.width = canvas.width;\n        this.height = canvas.height;\n        this.ctx = context;\n        this.x = x;\n        this.y = y;\n        this.color = color;\n        this.speed = this.getRandomValue(0.1, 0.9) * speed;\n        this.size = 0;\n        this.sizeStep = Math.random() * 0.4;\n        this.minSize = 0.5;\n        this.maxSizeInteger = 2;\n        this.maxSize = this.getRandomValue(this.minSize, this.maxSizeInteger);\n        this.delay = delay;\n        this.counter = 0;\n        this.counterStep = Math.random() * 4 + (this.width + this.height) * 0.01;\n        this.isIdle = false;\n        this.isReverse = false;\n        this.isShimmer = false;\n    }\n    getRandomValue(min, max) {\n        return Math.random() * (max - min) + min;\n    }\n    draw() {\n        const centerOffset = this.maxSizeInteger * 0.5 - this.size * 0.5;\n        this.ctx.fillStyle = this.color;\n        this.ctx.fillRect(this.x + centerOffset, this.y + centerOffset, this.size, this.size);\n    }\n    appear() {\n        this.isIdle = false;\n        if (this.counter <= this.delay) {\n            this.counter += this.counterStep;\n            return;\n        }\n        if (this.size >= this.maxSize) {\n            this.isShimmer = true;\n        }\n        if (this.isShimmer) {\n            this.shimmer();\n        } else {\n            this.size += this.sizeStep;\n        }\n        this.draw();\n    }\n    disappear() {\n        this.isShimmer = false;\n        this.counter = 0;\n        if (this.size <= 0) {\n            this.isIdle = true;\n            return;\n        } else {\n            this.size -= 0.1;\n        }\n        this.draw();\n    }\n    shimmer() {\n        if (this.size >= this.maxSize) {\n            this.isReverse = true;\n        } else if (this.size <= this.minSize) {\n            this.isReverse = false;\n        }\n        if (this.isReverse) {\n            this.size -= this.speed;\n        } else {\n            this.size += this.speed;\n        }\n    }\n}\nfunction getEffectiveSpeed(value, reducedMotion) {\n    const min = 0;\n    const max = 100;\n    const throttle = 0.001;\n    const parsed = parseInt(value, 10);\n    if (parsed <= min || reducedMotion) {\n        return min;\n    } else if (parsed >= max) {\n        return max * throttle;\n    } else {\n        return parsed * throttle;\n    }\n}\n/**\n * Variants adapted for the pricing theme\n */ const VARIANTS = {\n    default: {\n        activeColor: null,\n        gap: 5,\n        speed: 35,\n        colors: \"#f8fafc,#f1f5f9,#cbd5e1\",\n        noFocus: false\n    },\n    purple: {\n        activeColor: \"#e9d5ff\",\n        gap: 6,\n        speed: 40,\n        colors: \"#e9d5ff,#c4b5fd,#8b5cf6\",\n        noFocus: false\n    },\n    blue: {\n        activeColor: \"#dbeafe\",\n        gap: 5,\n        speed: 30,\n        colors: \"#dbeafe,#93c5fd,#3b82f6\",\n        noFocus: false\n    },\n    highlighted: {\n        activeColor: \"#e9d5ff\",\n        gap: 4,\n        speed: 60,\n        colors: \"#e9d5ff,#c4b5fd,#8b5cf6,#dbeafe,#93c5fd\",\n        noFocus: false\n    }\n};\nfunction PixelCard({ variant = \"default\", gap, speed, colors, noFocus, className = \"\", children }) {\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pixelsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const timePreviousRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(performance.now());\n    const reducedMotion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)( false ? 0 : false).current;\n    const variantCfg = VARIANTS[variant] || VARIANTS.default;\n    const finalGap = gap ?? variantCfg.gap;\n    const finalSpeed = speed ?? variantCfg.speed;\n    const finalColors = colors ?? variantCfg.colors;\n    const finalNoFocus = noFocus ?? variantCfg.noFocus;\n    const initPixels = ()=>{\n        if (!containerRef.current || !canvasRef.current) return;\n        const rect = containerRef.current.getBoundingClientRect();\n        const width = Math.floor(rect.width);\n        const height = Math.floor(rect.height);\n        const ctx = canvasRef.current.getContext(\"2d\");\n        canvasRef.current.width = width;\n        canvasRef.current.height = height;\n        canvasRef.current.style.width = `${width}px`;\n        canvasRef.current.style.height = `${height}px`;\n        const colorsArray = finalColors.split(\",\");\n        const pxs = [];\n        for(let x = 0; x < width; x += parseInt(finalGap, 10)){\n            for(let y = 0; y < height; y += parseInt(finalGap, 10)){\n                const color = colorsArray[Math.floor(Math.random() * colorsArray.length)];\n                const dx = x - width / 2;\n                const dy = y - height / 2;\n                const distance = Math.sqrt(dx * dx + dy * dy);\n                const delay = reducedMotion ? 0 : distance;\n                pxs.push(new Pixel(canvasRef.current, ctx, x, y, color, getEffectiveSpeed(finalSpeed, reducedMotion), delay));\n            }\n        }\n        pixelsRef.current = pxs;\n    };\n    const doAnimate = (fnName)=>{\n        animationRef.current = requestAnimationFrame(()=>doAnimate(fnName));\n        const timeNow = performance.now();\n        const timePassed = timeNow - timePreviousRef.current;\n        const timeInterval = 1000 / 60; // ~60 FPS\n        if (timePassed < timeInterval) return;\n        timePreviousRef.current = timeNow - timePassed % timeInterval;\n        const ctx = canvasRef.current?.getContext(\"2d\");\n        if (!ctx || !canvasRef.current) return;\n        ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);\n        let allIdle = true;\n        for(let i = 0; i < pixelsRef.current.length; i++){\n            const pixel = pixelsRef.current[i];\n            pixel[fnName]();\n            if (!pixel.isIdle) {\n                allIdle = false;\n            }\n        }\n        if (allIdle) {\n            cancelAnimationFrame(animationRef.current);\n        }\n    };\n    const handleAnimation = (name)=>{\n        cancelAnimationFrame(animationRef.current);\n        animationRef.current = requestAnimationFrame(()=>doAnimate(name));\n    };\n    const onMouseEnter = ()=>handleAnimation(\"appear\");\n    const onMouseLeave = ()=>handleAnimation(\"disappear\");\n    const onFocus = (e)=>{\n        if (e.currentTarget.contains(e.relatedTarget)) return;\n        handleAnimation(\"appear\");\n    };\n    const onBlur = (e)=>{\n        if (e.currentTarget.contains(e.relatedTarget)) return;\n        handleAnimation(\"disappear\");\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initPixels();\n        const observer = new ResizeObserver(()=>{\n            initPixels();\n        });\n        if (containerRef.current) {\n            observer.observe(containerRef.current);\n        }\n        return ()=>{\n            observer.disconnect();\n            cancelAnimationFrame(animationRef.current);\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        finalGap,\n        finalSpeed,\n        finalColors,\n        finalNoFocus\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: `relative overflow-hidden rounded-xl border border-white/10 isolate transition-colors duration-200 ease-[cubic-bezier(0.5,1,0.89,1)] select-none ${className}`,\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onFocus: finalNoFocus ? undefined : onFocus,\n        onBlur: finalNoFocus ? undefined : onBlur,\n        tabIndex: finalNoFocus ? -1 : 0,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                className: \"absolute inset-0 w-full h-full block pointer-events-none\",\n                ref: canvasRef\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\pixel-card.js\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\pixel-card.js\",\n        lineNumber: 257,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ui/pixel-card.js\n");

/***/ }),

/***/ "./lib/utils.js":
/*!**********************!*\
  !*** ./lib/utils.js ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWIvdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTRCO0FBQ2E7QUFFbEMsU0FBU0UsR0FBRyxHQUFHQyxNQUFNO0lBQzFCLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wcmVzZW50ZXIvLi9saWIvdXRpbHMuanM/OGExZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4IH0gZnJvbSAnY2xzeCc7XG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSAndGFpbHdpbmQtbWVyZ2UnO1xuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSk7XG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./lib/utils.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_pages_app_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"pages\\\\_app.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"pages\\\\\\\\_app.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_pages_app_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_pages_app_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/theme-provider */ \"./components/theme-provider.js\");\n/* harmony import */ var _components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/auth/auth-provider */ \"./components/auth/auth-provider.js\");\n/* harmony import */ var _vercel_speed_insights_next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @vercel/speed-insights/next */ \"@vercel/speed-insights/next\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_vercel_speed_insights_next__WEBPACK_IMPORTED_MODULE_4__]);\n_vercel_speed_insights_next__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"7d86941af6840549\",\n                dynamic: [\n                    (next_font_google_target_css_path_pages_app_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily\n                ],\n                children: `body{font-family:${(next_font_google_target_css_path_pages_app_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily}}`\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"dark\",\n                enableSystem: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_provider__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                            ...pageProps,\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"7d86941af6840549\",\n                                    [\n                                        (next_font_google_target_css_path_pages_app_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().style).fontFamily\n                                    ]\n                                ]\n                            ]) + \" \" + (pageProps && pageProps.className != null && pageProps.className || \"\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\_app.js\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vercel_speed_insights_next__WEBPACK_IMPORTED_MODULE_4__.SpeedInsights, {}, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\_app.js\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\_app.js\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\_app.js\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/pricing.js":
/*!**************************!*\
  !*** ./pages/pricing.js ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PricingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/header */ \"./components/header.js\");\n/* harmony import */ var _components_footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/footer */ \"./components/footer.js\");\n/* harmony import */ var _components_animations_animation_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/animations/animation-provider */ \"./components/animations/animation-provider.js\");\n/* harmony import */ var _components_subtle_background__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/subtle-background */ \"./components/subtle-background.js\");\n/* harmony import */ var _components_animations_animate__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/animations/animate */ \"./components/animations/animate.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _components_page_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/page-header */ \"./components/page-header.js\");\n/* harmony import */ var _components_pricing_section__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../components/pricing-section */ \"./components/pricing-section.js\");\n/* harmony import */ var _components_pricing_comparison_table__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/pricing-comparison-table */ \"./components/pricing-comparison-table.js\");\n/* harmony import */ var _components_animations_PricingAnimations__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/animations/PricingAnimations */ \"./components/animations/PricingAnimations.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_header__WEBPACK_IMPORTED_MODULE_2__, _components_footer__WEBPACK_IMPORTED_MODULE_3__, _components_animations_animation_provider__WEBPACK_IMPORTED_MODULE_4__, _components_animations_animate__WEBPACK_IMPORTED_MODULE_6__, framer_motion__WEBPACK_IMPORTED_MODULE_7__, _components_page_header__WEBPACK_IMPORTED_MODULE_8__, _components_pricing_section__WEBPACK_IMPORTED_MODULE_9__, _components_pricing_comparison_table__WEBPACK_IMPORTED_MODULE_10__, _components_animations_PricingAnimations__WEBPACK_IMPORTED_MODULE_11__]);\n([_components_header__WEBPACK_IMPORTED_MODULE_2__, _components_footer__WEBPACK_IMPORTED_MODULE_3__, _components_animations_animation_provider__WEBPACK_IMPORTED_MODULE_4__, _components_animations_animate__WEBPACK_IMPORTED_MODULE_6__, framer_motion__WEBPACK_IMPORTED_MODULE_7__, _components_page_header__WEBPACK_IMPORTED_MODULE_8__, _components_pricing_section__WEBPACK_IMPORTED_MODULE_9__, _components_pricing_comparison_table__WEBPACK_IMPORTED_MODULE_10__, _components_animations_PricingAnimations__WEBPACK_IMPORTED_MODULE_11__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\n// Pricing page component\nfunction PricingPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Pricing | AI Presenter\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"AI Presenter pricing plans - Choose the best option for your needs\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_animation_provider__WEBPACK_IMPORTED_MODULE_4__.AnimationProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_subtle_background__WEBPACK_IMPORTED_MODULE_5__.SubtleBackground, {}, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header__WEBPACK_IMPORTED_MODULE_2__.Header, {}, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_header__WEBPACK_IMPORTED_MODULE_8__.PageHeader, {\n                                    title: \"Choose the Perfect Plan for You\",\n                                    titleClassName: \"text-2xl mb-16 text-4xl md:text-5xl font-bold\",\n                                    height: \"550px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-[-150px] md:mt-[-180px]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pricing_section__WEBPACK_IMPORTED_MODULE_9__.PricingSection, {\n                                        pricingPlans: _components_pricing_section__WEBPACK_IMPORTED_MODULE_9__.defaultPricingPlans,\n                                        faqs: _components_pricing_section__WEBPACK_IMPORTED_MODULE_9__.defaultFAQs,\n                                        showFAQ: false\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"container mx-auto px-4 py-8 md:py-12 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 z-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_PricingAnimations__WEBPACK_IMPORTED_MODULE_11__.FloatingElements, {\n                                                count: 15,\n                                                colors: [\n                                                    \"#3A29FF\",\n                                                    \"#9C27B0\",\n                                                    \"#FF3232\"\n                                                ],\n                                                minOpacity: 0.02,\n                                                maxOpacity: 0.05\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                                lineNumber: 50,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10 max-w-5xl mx-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_animate__WEBPACK_IMPORTED_MODULE_6__.Animate, {\n                                                    type: \"fade\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl md:text-2xl font-bold mb-8 text-center\",\n                                                        children: \"Reporting and analytics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                                        lineNumber: 59,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pricing_comparison_table__WEBPACK_IMPORTED_MODULE_10__.PricingComparisonTable, {\n                                                    plans: _components_pricing_section__WEBPACK_IMPORTED_MODULE_9__.defaultPricingPlans\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"container mx-auto px-4 pb-24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-12 max-w-3xl mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_animate__WEBPACK_IMPORTED_MODULE_6__.Animate, {\n                                                type: \"fade\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl md:text-3xl font-bold mb-8 text-center\",\n                                                    children: \"Frequently Asked Questions\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: _components_pricing_section__WEBPACK_IMPORTED_MODULE_9__.defaultFAQs.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_animations_animate__WEBPACK_IMPORTED_MODULE_6__.Animate, {\n                                                        type: \"slide\",\n                                                        delay: 0.1 * (index + 1),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"subtle-glass rounded-lg p-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-2\",\n                                                                    children: faq.question\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                                                    lineNumber: 76,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-muted-foreground\",\n                                                                    children: faq.answer\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                                                    lineNumber: 77,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                                            lineNumber: 75,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                                        lineNumber: 74,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer__WEBPACK_IMPORTED_MODULE_3__.Footer, {}, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\pages\\\\pricing.js\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/pricing.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next-themes":
/*!******************************!*\
  !*** external "next-themes" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-themes");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("styled-jsx/style");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "@vercel/speed-insights/next":
/*!**********************************************!*\
  !*** external "@vercel/speed-insights/next" ***!
  \**********************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@vercel/speed-insights/next");;

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "framer-motion":
/*!********************************!*\
  !*** external "framer-motion" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("framer-motion");;

/***/ }),

/***/ "ogl":
/*!**********************!*\
  !*** external "ogl" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = import("ogl");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fpricing&preferredRegion=&absolutePagePath=.%2Fpages%5Cpricing.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();