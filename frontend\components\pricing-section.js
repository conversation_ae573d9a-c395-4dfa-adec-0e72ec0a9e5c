"use client"

import { Animate } from "./animations/animate";
import { EnhancedButton } from "./ui/enhanced-button";
import { motion } from "framer-motion";
import { Check, X } from "lucide-react";
import Link from 'next/link';
import PixelCard from "./ui/pixel-card";

// Pricing card component
export const PricingCard = ({
  title,
  price,
  description,
  features,
  buttonText,
  buttonLink,
  highlighted = false,
  delay = 0
}) => {
  return (
    <Animate type="slide" delay={delay}>
      <motion.div
        className={`relative ${
          highlighted
            ? "transform scale-105 z-10"
            : ""
        }`}
        whileHover={{
          y: highlighted ? -8 : -5,
          scale: highlighted ? 1.08 : 1.02,
          transition: { duration: 0.2 }
        }}
      >
        {highlighted && (
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-20">
            <div className="bg-gradient-to-r from-theme-purple to-theme-blue text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
              MOST POPULAR
            </div>
          </div>
        )}

        <PixelCard
          variant={highlighted ? "highlighted" : "default"}
          className={`${
            highlighted
              ? "border-2 border-theme-purple/50 shadow-2xl shadow-theme-purple/20 bg-gradient-to-br from-theme-purple/5 to-theme-blue/5"
              : "border border-white/10 subtle-glass"
          }`}
        >
          <div className="absolute inset-0 p-5 md:p-6 flex flex-col h-full z-10">
            <div className="mb-3">
              <h3 className="text-lg md:text-xl font-bold mb-1">{title}</h3>
              <p className="text-sm text-muted-foreground mb-3">{description}</p>
              <div className="text-3xl md:text-4xl font-bold mb-2">
                {price}
              </div>
            </div>

            <div className="flex-grow">
              <ul className="space-y-2 my-4 text-sm">
                {features.slice(0, 6).map((feature, index) => (
                  <li key={index} className="flex items-start">
                    {feature.included ? (
                      <Check className="h-4 w-4 text-theme-blue shrink-0 mr-2 mt-0.5" />
                    ) : (
                      <X className="h-4 w-4 text-muted-foreground shrink-0 mr-2 mt-0.5" />
                    )}
                    <span className={feature.included ? "" : "text-muted-foreground"}>
                      {feature.text}
                    </span>
                  </li>
                ))}
              </ul>
            </div>

            <Link href={buttonLink} className="mt-auto">
              <EnhancedButton
                size="md"
                color={highlighted ? "purple" : "blue"}
                className={`w-full ${
                  highlighted
                    ? "bg-theme-purple text-white hover:bg-theme-purple/90"
                    : "bg-transparent border text-foreground dark:border-white/20 dark:hover:border-white/40 border-black/50 hover:border-black/70"
                }`}
              >
                {buttonText}
              </EnhancedButton>
            </Link>
          </div>
        </PixelCard>
      </motion.div>
    </Animate>
  );
};

// Pricing plans data
export const defaultPricingPlans = [
  {
    title: "Free Plan",
    price: "Free",
    description: "Perfect for trying out our service.",
    features: [
      { text: "3 free voiceovers", included: true },
      { text: "Basic voice options", included: true },
      { text: "PDF & script upload", included: true },
      { text: "MP4 video generation", included: true },
      { text: "Voice cloning", included: false },
      { text: "Priority support", included: false },
    ],
    buttonText: "Get Started",
    buttonLink: "/dashboard",
    highlighted: false,
    delay: 0.1
  },
  {
    title: "Starter Plan",
    price: "$9.99",
    description: "Most popular for regular users.",
    features: [
      { text: "Unlimited voiceovers", included: true },
      { text: "All voice options", included: true },
      { text: "PDF & script upload", included: true },
      { text: "MP4 video generation", included: true },
      { text: "1 voice clone", included: true },
      { text: "Email support", included: true },
    ],
    buttonText: "Choose Starter",
    buttonLink: "/",
    highlighted: true,
    delay: 0.2
  },
  {
    title: "Premium Plan",
    price: "$19.99",
    description: "Best for power users and teams.",
    features: [
      { text: "Unlimited voiceovers", included: true },
      { text: "All voice options", included: true },
      { text: "PDF & script upload", included: true },
      { text: "MP4 video generation", included: true },
      { text: "5 voice clones", included: true },
      { text: "Priority support", included: true },
    ],
    buttonText: "Choose Premium",
    buttonLink: "/",
    highlighted: false,
    delay: 0.3
  }
];

// FAQ component
export const PricingFAQ = ({ faqs }) => {
  return (
    <div className="mt-24 max-w-3xl mx-auto">
      <Animate type="fade">
        <h2 className="text-2xl md:text-3xl font-bold mb-8 text-center">Frequently Asked Questions</h2>
      </Animate>

      <div className="space-y-6">
        {faqs.map((faq, index) => (
          <Animate key={index} type="slide" delay={0.1 * (index + 1)}>
            <div className="subtle-glass rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
              <p className="text-muted-foreground">{faq.answer}</p>
            </div>
          </Animate>
        ))}
      </div>
    </div>
  );
};

// Default FAQ data
export const defaultFAQs = [
  {
    question: "How do I choose the right plan?",
    answer: "Choose a plan based on your usage needs. If you're just getting started, the Free plan gives you 3 voiceovers to try our service. For regular use, the Starter plan offers unlimited voiceovers and 1 voice clone. For power users who need multiple voice clones, the Premium plan provides 5 voice clones and priority support."
  },
  {
    question: "Can I change my plan at any time?",
    answer: "Yes, you can upgrade or downgrade your plan at any time. Upgrades take effect immediately, while downgrades will take effect at the end of your current billing cycle."
  },
  {
    question: "What is voice cloning and how does it work?",
    answer: "Voice cloning allows you to create custom AI voices based on audio samples. Free users cannot access this feature. Starter plan users can create 1 custom voice, while Premium users can create up to 5 custom voices for their presentations."
  }
];

// Main pricing section component
export function PricingSection({
  pricingPlans = defaultPricingPlans,
  faqs = defaultFAQs,
  showFAQ = true
}) {
  return (
    <div className="container mx-auto px-4 py-12 md:py-16">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-5xl mx-auto items-center">
        {pricingPlans.map((plan, index) => (
          <div key={index} className={`${plan.highlighted ? 'md:mt-0' : 'md:mt-6'}`}>
            <PricingCard {...plan} />
          </div>
        ))}
      </div>

      {showFAQ && <PricingFAQ faqs={faqs} />}
    </div>
  );
}
