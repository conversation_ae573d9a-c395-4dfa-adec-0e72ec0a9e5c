"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\r\\n  :root {\\r\\n    --background: 0 0% 100%;\\r\\n    --foreground: 240 10% 3.9%;\\r\\n    --card: 0 0% 100%;\\r\\n    --card-foreground: 240 10% 3.9%;\\r\\n    --popover: 0 0% 100%;\\r\\n    --popover-foreground: 240 10% 3.9%;\\r\\n    --primary: 240 5.9% 10%;\\r\\n    --primary-foreground: 0 0% 98%;\\r\\n    --secondary: 240 4.8% 95.9%;\\r\\n    --secondary-foreground: 240 5.9% 10%;\\r\\n    --muted: 240 4.8% 95.9%;\\r\\n    --muted-foreground: 240 3.8% 46.1%;\\r\\n    --accent: 240 4.8% 95.9%;\\r\\n    --accent-foreground: 240 5.9% 10%;\\r\\n    --destructive: 0 84.2% 60.2%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 5.9% 90%;\\r\\n    --input: 240 5.9% 90%;\\r\\n    --ring: 240 5.9% 10%;\\r\\n    --radius: 0.5rem;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 50%;\\r\\n    --theme-purple: 260 100% 60%;\\r\\n    --theme-teal: 180 100% 40%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 50%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.5);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.5);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.5);\\r\\n\\r\\n    /* Theme transition */\\r\\n    --theme-transition: 0.3s ease;\\r\\n  }\\r\\n\\r\\n  .dark {\\r\\n    --background: 240 10% 3.9%;\\r\\n    --foreground: 0 0% 98%;\\r\\n    --card: 240 10% 3.9%;\\r\\n    --card-foreground: 0 0% 98%;\\r\\n    --popover: 240 10% 3.9%;\\r\\n    --popover-foreground: 0 0% 98%;\\r\\n    --primary: 0 0% 98%;\\r\\n    --primary-foreground: 240 5.9% 10%;\\r\\n    --secondary: 240 3.7% 15.9%;\\r\\n    --secondary-foreground: 0 0% 98%;\\r\\n    --muted: 240 3.7% 15.9%;\\r\\n    --muted-foreground: 240 5% 64.9%;\\r\\n    --accent: 240 3.7% 15.9%;\\r\\n    --accent-foreground: 0 0% 98%;\\r\\n    --destructive: 0 62.8% 30.6%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 3.7% 15.9%;\\r\\n    --input: 240 3.7% 15.9%;\\r\\n    --ring: 240 4.9% 83.9%;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 60%;\\r\\n    --theme-purple: 260 100% 70%;\\r\\n    --theme-teal: 180 100% 50%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 60%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.7);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.7);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.7);\\r\\n  }\\r\\n  * {\\n  border-color: hsl(var(--border));\\n}\\r\\n  body {\\n  background-color: hsl(var(--background));\\n  color: hsl(var(--foreground));\\r\\n    font-feature-settings: \\\"rlig\\\" 1, \\\"calt\\\" 1;\\r\\n    transition: background-color var(--theme-transition), color var(--theme-transition);\\n}\\r\\n.container {\\n  width: 100%;\\n  margin-right: auto;\\n  margin-left: auto;\\n  padding-right: 2rem;\\n  padding-left: 2rem;\\n}\\r\\n@media (min-width: 1400px) {\\n\\n  .container {\\n    max-width: 1400px;\\n  }\\n}\\r\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\r\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\r\\n.visible {\\n  visibility: visible;\\n}\\r\\n.fixed {\\n  position: fixed;\\n}\\r\\n.absolute {\\n  position: absolute;\\n}\\r\\n.relative {\\n  position: relative;\\n}\\r\\n.inset-0 {\\n  inset: 0px;\\n}\\r\\n.-top-1 {\\n  top: -0.25rem;\\n}\\r\\n.bottom-2 {\\n  bottom: 0.5rem;\\n}\\r\\n.bottom-20 {\\n  bottom: 5rem;\\n}\\r\\n.bottom-40 {\\n  bottom: 10rem;\\n}\\r\\n.bottom-full {\\n  bottom: 100%;\\n}\\r\\n.left-0 {\\n  left: 0px;\\n}\\r\\n.left-1\\\\/2 {\\n  left: 50%;\\n}\\r\\n.left-1\\\\/4 {\\n  left: 25%;\\n}\\r\\n.left-10 {\\n  left: 2.5rem;\\n}\\r\\n.left-2 {\\n  left: 0.5rem;\\n}\\r\\n.left-\\\\[50\\\\%\\\\] {\\n  left: 50%;\\n}\\r\\n.right-0 {\\n  right: 0px;\\n}\\r\\n.right-10 {\\n  right: 2.5rem;\\n}\\r\\n.right-2 {\\n  right: 0.5rem;\\n}\\r\\n.right-20 {\\n  right: 5rem;\\n}\\r\\n.right-4 {\\n  right: 1rem;\\n}\\r\\n.top-0 {\\n  top: 0px;\\n}\\r\\n.top-20 {\\n  top: 5rem;\\n}\\r\\n.top-4 {\\n  top: 1rem;\\n}\\r\\n.top-40 {\\n  top: 10rem;\\n}\\r\\n.top-8 {\\n  top: 2rem;\\n}\\r\\n.top-\\\\[50\\\\%\\\\] {\\n  top: 50%;\\n}\\r\\n.-top-3 {\\n  top: -0.75rem;\\n}\\r\\n.isolate {\\n  isolation: isolate;\\n}\\r\\n.-z-10 {\\n  z-index: -10;\\n}\\r\\n.z-0 {\\n  z-index: 0;\\n}\\r\\n.z-10 {\\n  z-index: 10;\\n}\\r\\n.z-40 {\\n  z-index: 40;\\n}\\r\\n.z-50 {\\n  z-index: 50;\\n}\\r\\n.z-20 {\\n  z-index: 20;\\n}\\r\\n.col-span-1 {\\n  grid-column: span 1 / span 1;\\n}\\r\\n.-mx-1 {\\n  margin-left: -0.25rem;\\n  margin-right: -0.25rem;\\n}\\r\\n.mx-1 {\\n  margin-left: 0.25rem;\\n  margin-right: 0.25rem;\\n}\\r\\n.mx-2 {\\n  margin-left: 0.5rem;\\n  margin-right: 0.5rem;\\n}\\r\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\r\\n.my-1 {\\n  margin-top: 0.25rem;\\n  margin-bottom: 0.25rem;\\n}\\r\\n.my-4 {\\n  margin-top: 1rem;\\n  margin-bottom: 1rem;\\n}\\r\\n.my-6 {\\n  margin-top: 1.5rem;\\n  margin-bottom: 1.5rem;\\n}\\r\\n.-ml-1 {\\n  margin-left: -0.25rem;\\n}\\r\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\r\\n.mb-12 {\\n  margin-bottom: 3rem;\\n}\\r\\n.mb-16 {\\n  margin-bottom: 4rem;\\n}\\r\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\r\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\r\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\r\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\r\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\r\\n.ml-1 {\\n  margin-left: 0.25rem;\\n}\\r\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\r\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\r\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\r\\n.mt-0 {\\n  margin-top: 0px;\\n}\\r\\n.mt-0\\\\.5 {\\n  margin-top: 0.125rem;\\n}\\r\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\r\\n.mt-12 {\\n  margin-top: 3rem;\\n}\\r\\n.mt-16 {\\n  margin-top: 4rem;\\n}\\r\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\r\\n.mt-24 {\\n  margin-top: 6rem;\\n}\\r\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\r\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\r\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\r\\n.mt-\\\\[-150px\\\\] {\\n  margin-top: -150px;\\n}\\r\\n.mt-auto {\\n  margin-top: auto;\\n}\\r\\n.block {\\n  display: block;\\n}\\r\\n.inline-block {\\n  display: inline-block;\\n}\\r\\n.flex {\\n  display: flex;\\n}\\r\\n.inline-flex {\\n  display: inline-flex;\\n}\\r\\n.table {\\n  display: table;\\n}\\r\\n.grid {\\n  display: grid;\\n}\\r\\n.hidden {\\n  display: none;\\n}\\r\\n.aspect-square {\\n  aspect-ratio: 1 / 1;\\n}\\r\\n.aspect-video {\\n  aspect-ratio: 16 / 9;\\n}\\r\\n.h-0 {\\n  height: 0px;\\n}\\r\\n.h-1 {\\n  height: 0.25rem;\\n}\\r\\n.h-10 {\\n  height: 2.5rem;\\n}\\r\\n.h-11 {\\n  height: 2.75rem;\\n}\\r\\n.h-12 {\\n  height: 3rem;\\n}\\r\\n.h-16 {\\n  height: 4rem;\\n}\\r\\n.h-2 {\\n  height: 0.5rem;\\n}\\r\\n.h-2\\\\.5 {\\n  height: 0.625rem;\\n}\\r\\n.h-20 {\\n  height: 5rem;\\n}\\r\\n.h-24 {\\n  height: 6rem;\\n}\\r\\n.h-3 {\\n  height: 0.75rem;\\n}\\r\\n.h-3\\\\.5 {\\n  height: 0.875rem;\\n}\\r\\n.h-32 {\\n  height: 8rem;\\n}\\r\\n.h-4 {\\n  height: 1rem;\\n}\\r\\n.h-40 {\\n  height: 10rem;\\n}\\r\\n.h-5 {\\n  height: 1.25rem;\\n}\\r\\n.h-6 {\\n  height: 1.5rem;\\n}\\r\\n.h-7 {\\n  height: 1.75rem;\\n}\\r\\n.h-8 {\\n  height: 2rem;\\n}\\r\\n.h-9 {\\n  height: 2.25rem;\\n}\\r\\n.h-\\\\[1px\\\\] {\\n  height: 1px;\\n}\\r\\n.h-\\\\[var\\\\(--radix-select-trigger-height\\\\)\\\\] {\\n  height: var(--radix-select-trigger-height);\\n}\\r\\n.h-auto {\\n  height: auto;\\n}\\r\\n.h-full {\\n  height: 100%;\\n}\\r\\n.h-px {\\n  height: 1px;\\n}\\r\\n.min-h-\\\\[100px\\\\] {\\n  min-height: 100px;\\n}\\r\\n.min-h-\\\\[150px\\\\] {\\n  min-height: 150px;\\n}\\r\\n.min-h-\\\\[80px\\\\] {\\n  min-height: 80px;\\n}\\r\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\r\\n.w-0 {\\n  width: 0px;\\n}\\r\\n.w-10 {\\n  width: 2.5rem;\\n}\\r\\n.w-12 {\\n  width: 3rem;\\n}\\r\\n.w-16 {\\n  width: 4rem;\\n}\\r\\n.w-2 {\\n  width: 0.5rem;\\n}\\r\\n.w-2\\\\.5 {\\n  width: 0.625rem;\\n}\\r\\n.w-20 {\\n  width: 5rem;\\n}\\r\\n.w-24 {\\n  width: 6rem;\\n}\\r\\n.w-3 {\\n  width: 0.75rem;\\n}\\r\\n.w-3\\\\.5 {\\n  width: 0.875rem;\\n}\\r\\n.w-32 {\\n  width: 8rem;\\n}\\r\\n.w-4 {\\n  width: 1rem;\\n}\\r\\n.w-48 {\\n  width: 12rem;\\n}\\r\\n.w-5 {\\n  width: 1.25rem;\\n}\\r\\n.w-6 {\\n  width: 1.5rem;\\n}\\r\\n.w-8 {\\n  width: 2rem;\\n}\\r\\n.w-\\\\[1px\\\\] {\\n  width: 1px;\\n}\\r\\n.w-fit {\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\r\\n.w-full {\\n  width: 100%;\\n}\\r\\n.min-w-\\\\[8rem\\\\] {\\n  min-width: 8rem;\\n}\\r\\n.min-w-\\\\[var\\\\(--radix-select-trigger-width\\\\)\\\\] {\\n  min-width: var(--radix-select-trigger-width);\\n}\\r\\n.max-w-2xl {\\n  max-width: 42rem;\\n}\\r\\n.max-w-3xl {\\n  max-width: 48rem;\\n}\\r\\n.max-w-4xl {\\n  max-width: 56rem;\\n}\\r\\n.max-w-5xl {\\n  max-width: 64rem;\\n}\\r\\n.max-w-lg {\\n  max-width: 32rem;\\n}\\r\\n.max-w-md {\\n  max-width: 28rem;\\n}\\r\\n.max-w-xs {\\n  max-width: 20rem;\\n}\\r\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\r\\n.shrink-0 {\\n  flex-shrink: 0;\\n}\\r\\n.flex-grow {\\n  flex-grow: 1;\\n}\\r\\n.grow {\\n  flex-grow: 1;\\n}\\r\\n.border-collapse {\\n  border-collapse: collapse;\\n}\\r\\n.-translate-x-1\\\\/2 {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-\\\\[-50\\\\%\\\\] {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-1 {\\n  --tw-translate-y: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-\\\\[-50\\\\%\\\\] {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-0 {\\n  --tw-rotate: 0deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-105 {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n@keyframes gradient-x {\\n\\n  0%, 100% {\\n    background-position: 0% 50%;\\n  }\\n\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n}\\r\\n.animate-gradient-x {\\n  animation: gradient-x 3s ease infinite;\\n}\\r\\n@keyframes pulse {\\n\\n  50% {\\n    opacity: .5;\\n  }\\n}\\r\\n.animate-pulse {\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\r\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\r\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\r\\n.cursor-default {\\n  cursor: default;\\n}\\r\\n.cursor-help {\\n  cursor: help;\\n}\\r\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\r\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\r\\n.touch-none {\\n  touch-action: none;\\n}\\r\\n.select-none {\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n}\\r\\n.resize-none {\\n  resize: none;\\n}\\r\\n.resize {\\n  resize: both;\\n}\\r\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\r\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\r\\n.flex-col {\\n  flex-direction: column;\\n}\\r\\n.flex-col-reverse {\\n  flex-direction: column-reverse;\\n}\\r\\n.flex-wrap {\\n  flex-wrap: wrap;\\n}\\r\\n.items-start {\\n  align-items: flex-start;\\n}\\r\\n.items-end {\\n  align-items: flex-end;\\n}\\r\\n.items-center {\\n  align-items: center;\\n}\\r\\n.justify-center {\\n  justify-content: center;\\n}\\r\\n.justify-between {\\n  justify-content: space-between;\\n}\\r\\n.gap-1 {\\n  gap: 0.25rem;\\n}\\r\\n.gap-2 {\\n  gap: 0.5rem;\\n}\\r\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\r\\n.gap-4 {\\n  gap: 1rem;\\n}\\r\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\r\\n.gap-8 {\\n  gap: 2rem;\\n}\\r\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-1\\\\.5 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\r\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\r\\n.overflow-x-auto {\\n  overflow-x: auto;\\n}\\r\\n.truncate {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\r\\n.whitespace-nowrap {\\n  white-space: nowrap;\\n}\\r\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\r\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\r\\n.rounded-lg {\\n  border-radius: var(--radius);\\n}\\r\\n.rounded-md {\\n  border-radius: calc(var(--radius) - 2px);\\n}\\r\\n.rounded-sm {\\n  border-radius: calc(var(--radius) - 4px);\\n}\\r\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\r\\n.border {\\n  border-width: 1px;\\n}\\r\\n.border-2 {\\n  border-width: 2px;\\n}\\r\\n.border-8 {\\n  border-width: 8px;\\n}\\r\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\r\\n.border-b-2 {\\n  border-bottom-width: 2px;\\n}\\r\\n.border-b-\\\\[86px\\\\] {\\n  border-bottom-width: 86px;\\n}\\r\\n.border-l-\\\\[50px\\\\] {\\n  border-left-width: 50px;\\n}\\r\\n.border-r-\\\\[50px\\\\] {\\n  border-right-width: 50px;\\n}\\r\\n.border-t {\\n  border-top-width: 1px;\\n}\\r\\n.border-t-2 {\\n  border-top-width: 2px;\\n}\\r\\n.border-dashed {\\n  border-style: dashed;\\n}\\r\\n.border-amber-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-amber-900\\\\/50 {\\n  border-color: rgb(120 53 15 / 0.5);\\n}\\r\\n.border-black\\\\/50 {\\n  border-color: rgb(0 0 0 / 0.5);\\n}\\r\\n.border-border {\\n  border-color: hsl(var(--border));\\n}\\r\\n.border-destructive {\\n  border-color: hsl(var(--destructive));\\n}\\r\\n.border-destructive\\\\/50 {\\n  border-color: hsl(var(--destructive) / 0.5);\\n}\\r\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-gray-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-gray-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-green-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-green-900\\\\/50 {\\n  border-color: rgb(20 83 45 / 0.5);\\n}\\r\\n.border-input {\\n  border-color: hsl(var(--input));\\n}\\r\\n.border-muted-foreground {\\n  border-color: hsl(var(--muted-foreground));\\n}\\r\\n.border-muted-foreground\\\\/25 {\\n  border-color: hsl(var(--muted-foreground) / 0.25);\\n}\\r\\n.border-primary {\\n  border-color: hsl(var(--primary));\\n}\\r\\n.border-red-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-red-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-red-900\\\\/50 {\\n  border-color: rgb(127 29 29 / 0.5);\\n}\\r\\n.border-theme-blue {\\n  border-color: hsl(var(--theme-blue));\\n}\\r\\n.border-theme-blue\\\\/20 {\\n  border-color: hsl(var(--theme-blue) / 0.2);\\n}\\r\\n.border-theme-purple\\\\/30 {\\n  border-color: hsl(var(--theme-purple) / 0.3);\\n}\\r\\n.border-theme-teal\\\\/30 {\\n  border-color: hsl(var(--theme-teal) / 0.3);\\n}\\r\\n.border-white\\\\/10 {\\n  border-color: rgb(255 255 255 / 0.1);\\n}\\r\\n.border-white\\\\/20 {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n.border-white\\\\/50 {\\n  border-color: rgb(255 255 255 / 0.5);\\n}\\r\\n.border-theme-purple\\\\/50 {\\n  border-color: hsl(var(--theme-purple) / 0.5);\\n}\\r\\n.border-b-theme-teal\\\\/30 {\\n  border-bottom-color: hsl(var(--theme-teal) / 0.3);\\n}\\r\\n.border-l-transparent {\\n  border-left-color: transparent;\\n}\\r\\n.border-r-transparent {\\n  border-right-color: transparent;\\n}\\r\\n.bg-amber-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-amber-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-amber-500\\\\/10 {\\n  background-color: rgb(245 158 11 / 0.1);\\n}\\r\\n.bg-amber-900\\\\/20 {\\n  background-color: rgb(120 53 15 / 0.2);\\n}\\r\\n.bg-background {\\n  background-color: hsl(var(--background));\\n}\\r\\n.bg-background\\\\/50 {\\n  background-color: hsl(var(--background) / 0.5);\\n}\\r\\n.bg-background\\\\/80 {\\n  background-color: hsl(var(--background) / 0.8);\\n}\\r\\n.bg-background\\\\/95 {\\n  background-color: hsl(var(--background) / 0.95);\\n}\\r\\n.bg-black\\\\/20 {\\n  background-color: rgb(0 0 0 / 0.2);\\n}\\r\\n.bg-black\\\\/80 {\\n  background-color: rgb(0 0 0 / 0.8);\\n}\\r\\n.bg-border {\\n  background-color: hsl(var(--border));\\n}\\r\\n.bg-card {\\n  background-color: hsl(var(--card));\\n}\\r\\n.bg-destructive {\\n  background-color: hsl(var(--destructive));\\n}\\r\\n.bg-destructive\\\\/10 {\\n  background-color: hsl(var(--destructive) / 0.1);\\n}\\r\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-300 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-700 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-800 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-green-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-green-500\\\\/20 {\\n  background-color: rgb(34 197 94 / 0.2);\\n}\\r\\n.bg-green-900\\\\/20 {\\n  background-color: rgb(20 83 45 / 0.2);\\n}\\r\\n.bg-muted {\\n  background-color: hsl(var(--muted));\\n}\\r\\n.bg-popover {\\n  background-color: hsl(var(--popover));\\n}\\r\\n.bg-primary {\\n  background-color: hsl(var(--primary));\\n}\\r\\n.bg-primary\\\\/20 {\\n  background-color: hsl(var(--primary) / 0.2);\\n}\\r\\n.bg-red-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-red-500\\\\/10 {\\n  background-color: rgb(239 68 68 / 0.1);\\n}\\r\\n.bg-red-500\\\\/20 {\\n  background-color: rgb(239 68 68 / 0.2);\\n}\\r\\n.bg-red-900\\\\/20 {\\n  background-color: rgb(127 29 29 / 0.2);\\n}\\r\\n.bg-red-950\\\\/20 {\\n  background-color: rgb(69 10 10 / 0.2);\\n}\\r\\n.bg-red-950\\\\/80 {\\n  background-color: rgb(69 10 10 / 0.8);\\n}\\r\\n.bg-secondary {\\n  background-color: hsl(var(--secondary));\\n}\\r\\n.bg-theme-blue {\\n  background-color: hsl(var(--theme-blue));\\n}\\r\\n.bg-theme-blue\\\\/10 {\\n  background-color: hsl(var(--theme-blue) / 0.1);\\n}\\r\\n.bg-theme-blue\\\\/20 {\\n  background-color: hsl(var(--theme-blue) / 0.2);\\n}\\r\\n.bg-theme-blue\\\\/5 {\\n  background-color: hsl(var(--theme-blue) / 0.05);\\n}\\r\\n.bg-theme-purple {\\n  background-color: hsl(var(--theme-purple));\\n}\\r\\n.bg-theme-purple\\\\/10 {\\n  background-color: hsl(var(--theme-purple) / 0.1);\\n}\\r\\n.bg-theme-purple\\\\/20 {\\n  background-color: hsl(var(--theme-purple) / 0.2);\\n}\\r\\n.bg-theme-teal\\\\/10 {\\n  background-color: hsl(var(--theme-teal) / 0.1);\\n}\\r\\n.bg-transparent {\\n  background-color: transparent;\\n}\\r\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-white\\\\/10 {\\n  background-color: rgb(255 255 255 / 0.1);\\n}\\r\\n.bg-white\\\\/5 {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\r\\n.bg-gradient-to-b {\\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\\n}\\r\\n.bg-gradient-to-br {\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\r\\n.bg-gradient-to-r {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\r\\n.from-amber-500 {\\n  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-gray-50 {\\n  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-gray-900 {\\n  --tw-gradient-from: #111827 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-green-500 {\\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-blue {\\n  --tw-gradient-from: hsl(var(--theme-blue)) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-blue) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-blue\\\\/20 {\\n  --tw-gradient-from: hsl(var(--theme-blue) / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-blue) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-purple {\\n  --tw-gradient-from: hsl(var(--theme-purple)) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-purple) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-purple\\\\/5 {\\n  --tw-gradient-from: hsl(var(--theme-purple) / 0.05) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-purple) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.via-purple-600 {\\n  --tw-gradient-to: rgb(147 51 234 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #9333ea var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.to-black {\\n  --tw-gradient-to: #000 var(--tw-gradient-to-position);\\n}\\r\\n.to-emerald-600 {\\n  --tw-gradient-to: #059669 var(--tw-gradient-to-position);\\n}\\r\\n.to-gray-800 {\\n  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);\\n}\\r\\n.to-orange-600 {\\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-500 {\\n  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-500\\\\/20 {\\n  --tw-gradient-to: rgb(168 85 247 / 0.2) var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-600 {\\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\\n}\\r\\n.to-theme-blue {\\n  --tw-gradient-to: hsl(var(--theme-blue)) var(--tw-gradient-to-position);\\n}\\r\\n.to-theme-purple {\\n  --tw-gradient-to: hsl(var(--theme-purple)) var(--tw-gradient-to-position);\\n}\\r\\n.to-white {\\n  --tw-gradient-to: #fff var(--tw-gradient-to-position);\\n}\\r\\n.to-theme-blue\\\\/5 {\\n  --tw-gradient-to: hsl(var(--theme-blue) / 0.05) var(--tw-gradient-to-position);\\n}\\r\\n.bg-\\\\[length\\\\:200\\\\%_100\\\\%\\\\] {\\n  background-size: 200% 100%;\\n}\\r\\n.bg-clip-text {\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n}\\r\\n.fill-current {\\n  fill: currentColor;\\n}\\r\\n.object-cover {\\n  -o-object-fit: cover;\\n     object-fit: cover;\\n}\\r\\n.p-1 {\\n  padding: 0.25rem;\\n}\\r\\n.p-2 {\\n  padding: 0.5rem;\\n}\\r\\n.p-3 {\\n  padding: 0.75rem;\\n}\\r\\n.p-4 {\\n  padding: 1rem;\\n}\\r\\n.p-5 {\\n  padding: 1.25rem;\\n}\\r\\n.p-6 {\\n  padding: 1.5rem;\\n}\\r\\n.p-8 {\\n  padding: 2rem;\\n}\\r\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\r\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\r\\n.px-8 {\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\r\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\r\\n.py-1\\\\.5 {\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\r\\n.py-10 {\\n  padding-top: 2.5rem;\\n  padding-bottom: 2.5rem;\\n}\\r\\n.py-12 {\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\r\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\r\\n.py-20 {\\n  padding-top: 5rem;\\n  padding-bottom: 5rem;\\n}\\r\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\r\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\r\\n.py-6 {\\n  padding-top: 1.5rem;\\n  padding-bottom: 1.5rem;\\n}\\r\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\r\\n.pb-2 {\\n  padding-bottom: 0.5rem;\\n}\\r\\n.pb-24 {\\n  padding-bottom: 6rem;\\n}\\r\\n.pl-8 {\\n  padding-left: 2rem;\\n}\\r\\n.pr-16 {\\n  padding-right: 4rem;\\n}\\r\\n.pr-2 {\\n  padding-right: 0.5rem;\\n}\\r\\n.pt-0 {\\n  padding-top: 0px;\\n}\\r\\n.pt-2 {\\n  padding-top: 0.5rem;\\n}\\r\\n.pt-6 {\\n  padding-top: 1.5rem;\\n}\\r\\n.text-left {\\n  text-align: left;\\n}\\r\\n.text-center {\\n  text-align: center;\\n}\\r\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\r\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\r\\n.text-4xl {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\r\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\r\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\r\\n.font-bold {\\n  font-weight: 700;\\n}\\r\\n.font-medium {\\n  font-weight: 500;\\n}\\r\\n.font-semibold {\\n  font-weight: 600;\\n}\\r\\n.leading-none {\\n  line-height: 1;\\n}\\r\\n.tracking-tight {\\n  letter-spacing: -0.025em;\\n}\\r\\n.text-amber-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 191 36 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-amber-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(245 158 11 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-amber-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(217 119 6 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-black {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-card-foreground {\\n  color: hsl(var(--card-foreground));\\n}\\r\\n.text-current {\\n  color: currentColor;\\n}\\r\\n.text-destructive {\\n  color: hsl(var(--destructive));\\n}\\r\\n.text-destructive-foreground {\\n  color: hsl(var(--destructive-foreground));\\n}\\r\\n.text-foreground {\\n  color: hsl(var(--foreground));\\n}\\r\\n.text-gray-200 {\\n  --tw-text-opacity: 1;\\n  color: rgb(229 231 235 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-green-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-green-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-muted-foreground {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n.text-popover-foreground {\\n  color: hsl(var(--popover-foreground));\\n}\\r\\n.text-primary {\\n  color: hsl(var(--primary));\\n}\\r\\n.text-primary-foreground {\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n.text-red-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-red-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-secondary-foreground {\\n  color: hsl(var(--secondary-foreground));\\n}\\r\\n.text-theme-blue {\\n  color: hsl(var(--theme-blue));\\n}\\r\\n.text-theme-purple {\\n  color: hsl(var(--theme-purple));\\n}\\r\\n.text-theme-teal {\\n  color: hsl(var(--theme-teal));\\n}\\r\\n.text-transparent {\\n  color: transparent;\\n}\\r\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n.underline-offset-4 {\\n  text-underline-offset: 4px;\\n}\\r\\n.opacity-0 {\\n  opacity: 0;\\n}\\r\\n.opacity-25 {\\n  opacity: 0.25;\\n}\\r\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\r\\n.opacity-70 {\\n  opacity: 0.7;\\n}\\r\\n.opacity-75 {\\n  opacity: 0.75;\\n}\\r\\n.opacity-80 {\\n  opacity: 0.8;\\n}\\r\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-md {\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-xl {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-2xl {\\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-theme-purple\\\\/10 {\\n  --tw-shadow-color: hsl(var(--theme-purple) / 0.1);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\r\\n.shadow-theme-purple\\\\/20 {\\n  --tw-shadow-color: hsl(var(--theme-purple) / 0.2);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\r\\n.outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n.outline {\\n  outline-style: solid;\\n}\\r\\n.ring-offset-background {\\n  --tw-ring-offset-color: hsl(var(--background));\\n}\\r\\n.blur {\\n  --tw-blur: blur(8px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.backdrop-blur-md {\\n  --tw-backdrop-blur: blur(12px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-opacity {\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-transform {\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\r\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\r\\n.duration-500 {\\n  transition-duration: 500ms;\\n}\\r\\n.will-change-\\\\[transform\\\\2c filter\\\\2c opacity\\\\] {\\n  will-change: transform,filter,opacity;\\n}\\r\\n@keyframes enter {\\n\\n  from {\\n    opacity: var(--tw-enter-opacity, 1);\\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\\n  }\\n}\\r\\n@keyframes exit {\\n\\n  to {\\n    opacity: var(--tw-exit-opacity, 1);\\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\\n  }\\n}\\r\\n.animate-in {\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n.fade-in-80 {\\n  --tw-enter-opacity: 0.8;\\n}\\r\\n.duration-200 {\\n  animation-duration: 200ms;\\n}\\r\\n.duration-300 {\\n  animation-duration: 300ms;\\n}\\r\\n.duration-500 {\\n  animation-duration: 500ms;\\n}\\r\\n\\r\\n/* Subtle glass effect */\\r\\n.subtle-glass {\\r\\n  background: rgba(255, 255, 255, 0.03);\\r\\n  backdrop-filter: blur(8px);\\r\\n  -webkit-backdrop-filter: blur(8px);\\r\\n  border: 1px solid rgba(255, 255, 255, 0.05);\\r\\n  transition: background-color var(--theme-transition), border-color var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-glass {\\r\\n  background: rgba(0, 0, 0, 0.03);\\r\\n  border: 1px solid rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle border glow */\\r\\n.subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05);\\r\\n  transition: box-shadow var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle hover effect */\\r\\n.subtle-hover {\\r\\n  transition: all 0.2s ease;\\r\\n}\\r\\n\\r\\n.subtle-hover:hover {\\r\\n  background-color: rgba(255, 255, 255, 0.05);\\r\\n}\\r\\n\\r\\n.light .subtle-hover:hover {\\r\\n  background-color: rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Gradient text */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n  transition: background var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .gradient-text {\\r\\n  background: linear-gradient(to right, #000, rgba(0, 0, 0, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n}\\r\\n\\r\\n/* Subtle grid background */\\r\\n.subtle-grid {\\r\\n  background-size: 30px 30px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-grid {\\r\\n  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),\\r\\n    linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Reveal animation classes */\\r\\n.reveal {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n  transition: opacity 0.5s ease, transform 0.5s ease;\\r\\n}\\r\\n\\r\\n.reveal.active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n/* Button shine effect */\\r\\n.btn-shine {\\r\\n  position: relative;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.btn-shine::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: -100%;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-image: linear-gradient(\\r\\n    90deg,\\r\\n    rgba(255, 255, 255, 0) 0%,\\r\\n    rgba(255, 255, 255, 0.05) 50%,\\r\\n    rgba(255, 255, 255, 0) 100%\\r\\n  );\\r\\n  transition: left 0.5s ease;\\r\\n}\\r\\n\\r\\n.btn-shine:hover::after {\\r\\n  left: 100%;\\r\\n}\\r\\n\\r\\n/* Dot pattern */\\r\\n.dot-pattern {\\r\\n  background-image: radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .dot-pattern {\\r\\n  background-image: radial-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Flow border effect */\\r\\n.flow-border {\\r\\n  position: relative;\\r\\n  z-index: 0;\\r\\n}\\r\\n\\r\\n.flow-border::before {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: -2px;\\r\\n  left: -2px;\\r\\n  right: -2px;\\r\\n  bottom: -2px;\\r\\n  background: var(\\r\\n    --bg-flow-border,\\r\\n    linear-gradient(\\r\\n      90deg,\\r\\n      var(--border-start-color),\\r\\n      var(--border-mid-color),\\r\\n      var(--border-end-color),\\r\\n      var(--border-start-color)\\r\\n    )\\r\\n  );\\r\\n  border-radius: inherit;\\r\\n  z-index: -1;\\r\\n  background-size: 300% 100%;\\r\\n  animation: border-flow 3s ease infinite;\\r\\n  opacity: 0;\\r\\n  transition: opacity 0.3s ease;\\r\\n}\\r\\n\\r\\n.flow-border:hover::before {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n/* Glow effect */\\r\\n.glow-effect {\\r\\n  transition: box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\n.glow-effect:hover {\\r\\n  --glow-color: var(--theme-blue-rgb, 33, 150, 243);\\r\\n  box-shadow: 0 0 10px 2px rgba(var(--glow-color), 0.3);\\r\\n}\\r\\n\\r\\n.glow-effect.purple:hover {\\r\\n  --glow-color: var(--theme-purple-rgb, 156, 39, 176);\\r\\n}\\r\\n\\r\\n.glow-effect.teal:hover {\\r\\n  --glow-color: var(--theme-teal-rgb, 0, 188, 212);\\r\\n}\\r\\n\\r\\n/* Theme transition */\\r\\n.theme-transition {\\r\\n  transition: all var(--theme-transition);\\r\\n}\\r\\n\\r\\n/* Page transition */\\r\\n.page-transition-enter {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n}\\r\\n\\r\\n.page-transition-enter-active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n.page-transition-exit {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n.page-transition-exit-active {\\r\\n  opacity: 0;\\r\\n  transform: translateY(-10px);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n/* Nav link with indicator */\\r\\n.nav-link {\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.nav-link::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  bottom: -4px;\\r\\n  left: 0;\\r\\n  width: 0;\\r\\n  height: 2px;\\r\\n  background-color: hsl(var(--theme-blue));\\r\\n  transition: width 0.3s ease;\\r\\n}\\r\\n\\r\\n.nav-link:hover::after,\\r\\n.nav-link.active::after {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n  height: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: transparent;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: rgba(255, 255, 255, 0.1);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(255, 255, 255, 0.2);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb {\\r\\n  background: rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(0, 0, 0, 0.2);\\r\\n}\\r\\n\\r\\n/* Animation for loading spinner */\\r\\n@keyframes border-flow {\\r\\n  0% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n  100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes subtle-pulse {\\r\\n  0% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 1;\\r\\n  }\\r\\n  100% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-subtle-pulse {\\r\\n  animation: subtle-pulse 2s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-rotate-loader {\\r\\n  animation: spin 1s linear infinite;\\r\\n}\\r\\n\\r\\n@keyframes spin {\\r\\n  from {\\r\\n    transform: rotate(0deg);\\r\\n  }\\r\\n  to {\\r\\n    transform: rotate(360deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n.placeholder\\\\:text-muted-foreground::-moz-placeholder {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.placeholder\\\\:text-muted-foreground::placeholder {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.hover\\\\:scale-105:hover {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.hover\\\\:border-black\\\\/20:hover {\\n  border-color: rgb(0 0 0 / 0.2);\\n}\\r\\n\\r\\n.hover\\\\:border-black\\\\/70:hover {\\n  border-color: rgb(0 0 0 / 0.7);\\n}\\r\\n\\r\\n.hover\\\\:border-gray-400:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:border-theme-blue\\\\/50:hover {\\n  border-color: hsl(var(--theme-blue) / 0.5);\\n}\\r\\n\\r\\n.hover\\\\:border-white\\\\/20:hover {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n\\r\\n.hover\\\\:border-white\\\\/30:hover {\\n  border-color: rgb(255 255 255 / 0.3);\\n}\\r\\n\\r\\n.hover\\\\:bg-accent:hover {\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.hover\\\\:bg-black\\\\/5:hover {\\n  background-color: rgb(0 0 0 / 0.05);\\n}\\r\\n\\r\\n.hover\\\\:bg-destructive\\\\/10:hover {\\n  background-color: hsl(var(--destructive) / 0.1);\\n}\\r\\n\\r\\n.hover\\\\:bg-destructive\\\\/90:hover {\\n  background-color: hsl(var(--destructive) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-primary\\\\/10:hover {\\n  background-color: hsl(var(--primary) / 0.1);\\n}\\r\\n\\r\\n.hover\\\\:bg-primary\\\\/90:hover {\\n  background-color: hsl(var(--primary) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-secondary\\\\/80:hover {\\n  background-color: hsl(var(--secondary) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:bg-theme-blue\\\\/10:hover {\\n  background-color: hsl(var(--theme-blue) / 0.1);\\n}\\r\\n\\r\\n.hover\\\\:bg-theme-purple\\\\/90:hover {\\n  background-color: hsl(var(--theme-purple) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-white\\\\/5:hover {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\r\\n\\r\\n.hover\\\\:bg-white\\\\/90:hover {\\n  background-color: rgb(255 255 255 / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:text-accent-foreground:hover {\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-black:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:text-foreground:hover {\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-primary\\\\/80:hover {\\n  color: hsl(var(--primary) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:text-theme-blue:hover {\\n  color: hsl(var(--theme-blue));\\n}\\r\\n\\r\\n.hover\\\\:text-white:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:underline:hover {\\n  text-decoration-line: underline;\\n}\\r\\n\\r\\n.hover\\\\:opacity-100:hover {\\n  opacity: 1;\\n}\\r\\n\\r\\n.hover\\\\:opacity-90:hover {\\n  opacity: 0.9;\\n}\\r\\n\\r\\n.hover\\\\:shadow-lg:hover {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.focus\\\\:border-primary:focus {\\n  border-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.focus\\\\:border-theme-blue:focus {\\n  border-color: hsl(var(--theme-blue));\\n}\\r\\n\\r\\n.focus\\\\:bg-accent:focus {\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.focus\\\\:text-accent-foreground:focus {\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus\\\\:ring-primary:focus {\\n  --tw-ring-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.focus\\\\:ring-ring:focus {\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n\\r\\n.focus\\\\:ring-theme-blue\\\\/50:focus {\\n  --tw-ring-color: hsl(var(--theme-blue) / 0.5);\\n}\\r\\n\\r\\n.focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:outline-none:focus-visible {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-2:focus-visible {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-ring:focus-visible {\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-offset-2:focus-visible {\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n\\r\\n.disabled\\\\:pointer-events-none:disabled {\\n  pointer-events: none;\\n}\\r\\n\\r\\n.disabled\\\\:cursor-not-allowed:disabled {\\n  cursor: not-allowed;\\n}\\r\\n\\r\\n.disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:opacity-100 {\\n  opacity: 1;\\n}\\r\\n\\r\\n.peer:disabled ~ .peer-disabled\\\\:cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\r\\n\\r\\n.peer:disabled ~ .peer-disabled\\\\:opacity-70 {\\n  opacity: 0.7;\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\]\\\\:pointer-events-none[data-disabled] {\\n  pointer-events: none;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:bg-background[data-state=\\\"active\\\"] {\\n  background-color: hsl(var(--background));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:bg-theme-blue[data-state=\\\"active\\\"] {\\n  background-color: hsl(var(--theme-blue));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:bg-accent[data-state=\\\"open\\\"] {\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:text-foreground[data-state=\\\"active\\\"] {\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:text-white[data-state=\\\"active\\\"] {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:text-muted-foreground[data-state=\\\"open\\\"] {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\]\\\\:opacity-50[data-disabled] {\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:shadow-sm[data-state=\\\"active\\\"] {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:animate-in[data-state=\\\"open\\\"] {\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:animate-out[data-state=\\\"closed\\\"] {\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-0[data-state=\\\"closed\\\"] {\\n  --tw-exit-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:fade-in-0[data-state=\\\"open\\\"] {\\n  --tw-enter-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:zoom-out-95[data-state=\\\"closed\\\"] {\\n  --tw-exit-scale: .95;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-95[data-state=\\\"open\\\"] {\\n  --tw-enter-scale: .95;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left-1\\\\/2[data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-x: -50%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top-\\\\[48\\\\%\\\\][data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-y: -48%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left-1\\\\/2[data-state=\\\"open\\\"] {\\n  --tw-enter-translate-x: -50%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top-\\\\[48\\\\%\\\\][data-state=\\\"open\\\"] {\\n  --tw-enter-translate-y: -48%;\\n}\\r\\n\\r\\n.dark\\\\:border-destructive:is(.dark *) {\\n  border-color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.dark\\\\:border-white\\\\/20:is(.dark *) {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n\\r\\n.dark\\\\:text-white:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:border-white\\\\/20:hover:is(.dark *) {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:border-white\\\\/40:hover:is(.dark *) {\\n  border-color: rgb(255 255 255 / 0.4);\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:bg-white\\\\/5:hover:is(.dark *) {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:text-white:hover:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .sm\\\\:max-w-\\\\[600px\\\\] {\\n    max-width: 600px;\\n  }\\n\\n  .sm\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:justify-end {\\n    justify-content: flex-end;\\n  }\\n\\n  .sm\\\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\n\\n  .sm\\\\:rounded-lg {\\n    border-radius: var(--radius);\\n  }\\n\\n  .sm\\\\:text-left {\\n    text-align: left;\\n  }\\n}\\r\\n\\r\\n@media (min-width: 768px) {\\n\\n  .md\\\\:mb-0 {\\n    margin-bottom: 0px;\\n  }\\n\\n  .md\\\\:mt-\\\\[-180px\\\\] {\\n    margin-top: -180px;\\n  }\\n\\n  .md\\\\:mt-0 {\\n    margin-top: 0px;\\n  }\\n\\n  .md\\\\:mt-6 {\\n    margin-top: 1.5rem;\\n  }\\n\\n  .md\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .md\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .md\\\\:gap-8 {\\n    gap: 2rem;\\n  }\\n\\n  .md\\\\:p-6 {\\n    padding: 1.5rem;\\n  }\\n\\n  .md\\\\:py-12 {\\n    padding-top: 3rem;\\n    padding-bottom: 3rem;\\n  }\\n\\n  .md\\\\:py-16 {\\n    padding-top: 4rem;\\n    padding-bottom: 4rem;\\n  }\\n\\n  .md\\\\:py-24 {\\n    padding-top: 6rem;\\n    padding-bottom: 6rem;\\n  }\\n\\n  .md\\\\:py-32 {\\n    padding-top: 8rem;\\n    padding-bottom: 8rem;\\n  }\\n\\n  .md\\\\:text-2xl {\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n\\n  .md\\\\:text-3xl {\\n    font-size: 1.875rem;\\n    line-height: 2.25rem;\\n  }\\n\\n  .md\\\\:text-4xl {\\n    font-size: 2.25rem;\\n    line-height: 2.5rem;\\n  }\\n\\n  .md\\\\:text-5xl {\\n    font-size: 3rem;\\n    line-height: 1;\\n  }\\n\\n  .md\\\\:text-6xl {\\n    font-size: 3.75rem;\\n    line-height: 1;\\n  }\\n\\n  .md\\\\:text-xl {\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n}\\r\\n\\r\\n@media (min-width: 1024px) {\\n\\n  .lg\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-5 {\\n    grid-template-columns: repeat(5, minmax(0, 1fr));\\n  }\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\+div\\\\]\\\\:translate-y-\\\\[-3px\\\\]>svg+div {\\n  --tw-translate-y: -3px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:absolute>svg {\\n  position: absolute;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:left-4>svg {\\n  left: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:top-4>svg {\\n  top: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-destructive>svg {\\n  color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-foreground>svg {\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\~\\\\*\\\\]\\\\:pl-7>svg~* {\\n  padding-left: 1.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_p\\\\]\\\\:leading-relaxed p {\\n  line-height: 1.625;\\n}\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;EAAd;IAAA,uBAAc;IAAd,0BAAc;IAAd,iBAAc;IAAd,+BAAc;IAAd,oBAAc;IAAd,kCAAc;IAAd,uBAAc;IAAd,8BAAc;IAAd,2BAAc;IAAd,oCAAc;IAAd,uBAAc;IAAd,kCAAc;IAAd,wBAAc;IAAd,iCAAc;IAAd,4BAAc;IAAd,kCAAc;IAAd,sBAAc;IAAd,qBAAc;IAAd,oBAAc;IAAd,gBAAc;;IAAd,iBAAc;IAAd,0BAAc;IAAd,4BAAc;IAAd,0BAAc;;IAAd,gBAAc;IAAd,4BAAc;;IAAd,uBAAc;IAAd,6CAAc;IAAd,2CAAc;IAAd,0CAAc;;IAAd,qBAAc;IAAd,6BAAc;EAAA;;EAAd;IAAA,0BAAc;IAAd,sBAAc;IAAd,oBAAc;IAAd,2BAAc;IAAd,uBAAc;IAAd,8BAAc;IAAd,mBAAc;IAAd,kCAAc;IAAd,2BAAc;IAAd,gCAAc;IAAd,uBAAc;IAAd,gCAAc;IAAd,wBAAc;IAAd,6BAAc;IAAd,4BAAc;IAAd,kCAAc;IAAd,wBAAc;IAAd,uBAAc;IAAd,sBAAc;;IAAd,iBAAc;IAAd,0BAAc;IAAd,4BAAc;IAAd,0BAAc;;IAAd,gBAAc;IAAd,4BAAc;;IAAd,uBAAc;IAAd,6CAAc;IAAd,2CAAc;IAAd,0CAAc;EAAA;EAAd;EAAA;AAAc;EAAd;EAAA,wCAAc;EAAd,6BAAc;IAAd,yCAAc;IAAd;AAAc;AACd;EAAA,WAAoB;EAApB,kBAAoB;EAApB,iBAAoB;EAApB,mBAAoB;EAApB;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AACpB;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,yBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;;EAAnB;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yBAAmB;KAAnB,sBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,gEAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,2EAAmB;EAAnB;AAAmB;AAAnB;EAAA,iFAAmB;EAAnB,2EAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,6EAAmB;EAAnB;AAAmB;AAAnB;EAAA,oFAAmB;EAAnB,6EAAmB;EAAnB;AAAmB;AAAnB;EAAA,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;KAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,iDAAmB;EAAnB;AAAmB;AAAnB;EAAA,iDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA,mCAAmB;IAAnB;EAAmB;AAAA;AAAnB;;EAAA;IAAA,kCAAmB;IAAnB;EAAmB;AAAA;AAAnB;EAAA,qBAAmB;EAAnB,yBAAmB;EAAnB,2BAAmB;EAAnB,yBAAmB;EAAnB,0BAAmB;EAAnB,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAyFnB,wBAAwB;AACxB;EACE,qCAAqC;EACrC,0BAA0B;EAC1B,kCAAkC;EAClC,2CAA2C;EAC3C,0FAA0F;AAC5F;;AAEA;EACE,+BAA+B;EAC/B,qCAAqC;AACvC;;AAEA,uBAAuB;AACvB;EACE,+CAA+C;EAC/C,8CAA8C;AAChD;;AAEA;EACE,yCAAyC;AAC3C;;AAEA,wBAAwB;AACxB;EACE,yBAAyB;AAC3B;;AAEA;EACE,2CAA2C;AAC7C;;AAEA;EACE,qCAAqC;AACvC;;AAEA,kBAAkB;AAClB;EACE,sEAAsE;EACtE,6BAA6B;EAC7B,oCAAoC;EACpC,qBAAqB;EACrB,8CAA8C;AAChD;;AAEA;EACE,gEAAgE;EAChE,6BAA6B;EAC7B,oCAAoC;EACpC,qBAAqB;AACvB;;AAEA,2BAA2B;AAC3B;EACE,0BAA0B;EAC1B,oDAAoD;AACtD;;AAEA;EACE;wEACsE;AACxE;;AAEA,6BAA6B;AAC7B;EACE,UAAU;EACV,2BAA2B;EAC3B,kDAAkD;AACpD;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA,wBAAwB;AACxB;EACE,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,WAAW;EACX,WAAW;EACX,YAAY;EACZ;;;;;GAKC;EACD,0BAA0B;AAC5B;;AAEA;EACE,UAAU;AACZ;;AAEA,gBAAgB;AAChB;EACE,gFAAgF;EAChF,0BAA0B;EAC1B,oDAAoD;AACtD;;AAEA;EACE,0EAA0E;AAC5E;;AAEA,uBAAuB;AACvB;EACE,kBAAkB;EAClB,UAAU;AACZ;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,SAAS;EACT,UAAU;EACV,WAAW;EACX,YAAY;EACZ;;;;;;;;;GASC;EACD,sBAAsB;EACtB,WAAW;EACX,0BAA0B;EAC1B,uCAAuC;EACvC,UAAU;EACV,6BAA6B;AAC/B;;AAEA;EACE,UAAU;AACZ;;AAEA,gBAAgB;AAChB;EACE,gCAAgC;AAClC;;AAEA;EACE,iDAAiD;EACjD,qDAAqD;AACvD;;AAEA;EACE,mDAAmD;AACrD;;AAEA;EACE,gDAAgD;AAClD;;AAEA,qBAAqB;AACrB;EACE,uCAAuC;AACzC;;AAEA,oBAAoB;AACpB;EACE,UAAU;EACV,2BAA2B;AAC7B;;AAEA;EACE,UAAU;EACV,wBAAwB;EACxB,wCAAwC;AAC1C;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA;EACE,UAAU;EACV,4BAA4B;EAC5B,wCAAwC;AAC1C;;AAEA,4BAA4B;AAC5B;EACE,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,YAAY;EACZ,OAAO;EACP,QAAQ;EACR,WAAW;EACX,wCAAwC;EACxC,2BAA2B;AAC7B;;AAEA;;EAEE,WAAW;AACb;;AAEA,qBAAqB;AACrB;EACE,UAAU;EACV,WAAW;AACb;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,8BAA8B;AAChC;;AAEA,kCAAkC;AAClC;EACE;IACE,2BAA2B;EAC7B;EACA;IACE,6BAA6B;EAC/B;EACA;IACE,2BAA2B;EAC7B;AACF;;AAEA;EACE;IACE,YAAY;EACd;EACA;IACE,UAAU;EACZ;EACA;IACE,YAAY;EACd;AACF;;AAEA;EACE,+CAA+C;AACjD;;AAEA;EACE,kCAAkC;AACpC;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,yBAAyB;EAC3B;AACF;;AArXA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,kBA6XA;EA7XA,kBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,sBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,kBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,+EA6XA;EA7XA,mGA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,8BA6XA;EA7XA;AA6XA;;AA7XA;EAAA,2GA6XA;EA7XA,yGA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,8BA6XA;EA7XA;AA6XA;;AA7XA;EAAA,2GA6XA;EA7XA,yGA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,0CA6XA;EA7XA,uDA6XA;EA7XA;AA6XA;;AA7XA;EAAA,qBA6XA;EA7XA,yBA6XA;EA7XA,2BA6XA;EA7XA,yBA6XA;EA7XA,0BA6XA;EA7XA,+BA6XA;EA7XA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA,yBA6XA;EA7XA,0BA6XA;EA7XA,wBA6XA;EA7XA,yBA6XA;EA7XA,8BA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;;EAAA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA,uBA6XA;IA7XA,sDA6XA;IA7XA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;AAAA;;AA7XA;;EAAA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,mBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,kBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,eA6XA;IA7XA;EA6XA;;EA7XA;IAAA,kBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,kBA6XA;IA7XA;EA6XA;AAAA;;AA7XA;;EAAA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;AAAA;;AA7XA;EAAA,sBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA\",\"sourcesContent\":[\"@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n\\r\\n@layer base {\\r\\n  :root {\\r\\n    --background: 0 0% 100%;\\r\\n    --foreground: 240 10% 3.9%;\\r\\n    --card: 0 0% 100%;\\r\\n    --card-foreground: 240 10% 3.9%;\\r\\n    --popover: 0 0% 100%;\\r\\n    --popover-foreground: 240 10% 3.9%;\\r\\n    --primary: 240 5.9% 10%;\\r\\n    --primary-foreground: 0 0% 98%;\\r\\n    --secondary: 240 4.8% 95.9%;\\r\\n    --secondary-foreground: 240 5.9% 10%;\\r\\n    --muted: 240 4.8% 95.9%;\\r\\n    --muted-foreground: 240 3.8% 46.1%;\\r\\n    --accent: 240 4.8% 95.9%;\\r\\n    --accent-foreground: 240 5.9% 10%;\\r\\n    --destructive: 0 84.2% 60.2%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 5.9% 90%;\\r\\n    --input: 240 5.9% 90%;\\r\\n    --ring: 240 5.9% 10%;\\r\\n    --radius: 0.5rem;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 50%;\\r\\n    --theme-purple: 260 100% 60%;\\r\\n    --theme-teal: 180 100% 40%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 50%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.5);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.5);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.5);\\r\\n\\r\\n    /* Theme transition */\\r\\n    --theme-transition: 0.3s ease;\\r\\n  }\\r\\n\\r\\n  .dark {\\r\\n    --background: 240 10% 3.9%;\\r\\n    --foreground: 0 0% 98%;\\r\\n    --card: 240 10% 3.9%;\\r\\n    --card-foreground: 0 0% 98%;\\r\\n    --popover: 240 10% 3.9%;\\r\\n    --popover-foreground: 0 0% 98%;\\r\\n    --primary: 0 0% 98%;\\r\\n    --primary-foreground: 240 5.9% 10%;\\r\\n    --secondary: 240 3.7% 15.9%;\\r\\n    --secondary-foreground: 0 0% 98%;\\r\\n    --muted: 240 3.7% 15.9%;\\r\\n    --muted-foreground: 240 5% 64.9%;\\r\\n    --accent: 240 3.7% 15.9%;\\r\\n    --accent-foreground: 0 0% 98%;\\r\\n    --destructive: 0 62.8% 30.6%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 3.7% 15.9%;\\r\\n    --input: 240 3.7% 15.9%;\\r\\n    --ring: 240 4.9% 83.9%;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 60%;\\r\\n    --theme-purple: 260 100% 70%;\\r\\n    --theme-teal: 180 100% 50%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 60%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.7);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.7);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.7);\\r\\n  }\\r\\n}\\r\\n\\r\\n@layer base {\\r\\n  * {\\r\\n    @apply border-border;\\r\\n  }\\r\\n  body {\\r\\n    @apply bg-background text-foreground;\\r\\n    font-feature-settings: \\\"rlig\\\" 1, \\\"calt\\\" 1;\\r\\n    transition: background-color var(--theme-transition), color var(--theme-transition);\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Subtle glass effect */\\r\\n.subtle-glass {\\r\\n  background: rgba(255, 255, 255, 0.03);\\r\\n  backdrop-filter: blur(8px);\\r\\n  -webkit-backdrop-filter: blur(8px);\\r\\n  border: 1px solid rgba(255, 255, 255, 0.05);\\r\\n  transition: background-color var(--theme-transition), border-color var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-glass {\\r\\n  background: rgba(0, 0, 0, 0.03);\\r\\n  border: 1px solid rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle border glow */\\r\\n.subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05);\\r\\n  transition: box-shadow var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle hover effect */\\r\\n.subtle-hover {\\r\\n  transition: all 0.2s ease;\\r\\n}\\r\\n\\r\\n.subtle-hover:hover {\\r\\n  background-color: rgba(255, 255, 255, 0.05);\\r\\n}\\r\\n\\r\\n.light .subtle-hover:hover {\\r\\n  background-color: rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Gradient text */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n  transition: background var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .gradient-text {\\r\\n  background: linear-gradient(to right, #000, rgba(0, 0, 0, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n}\\r\\n\\r\\n/* Subtle grid background */\\r\\n.subtle-grid {\\r\\n  background-size: 30px 30px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-grid {\\r\\n  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),\\r\\n    linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Reveal animation classes */\\r\\n.reveal {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n  transition: opacity 0.5s ease, transform 0.5s ease;\\r\\n}\\r\\n\\r\\n.reveal.active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n/* Button shine effect */\\r\\n.btn-shine {\\r\\n  position: relative;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.btn-shine::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: -100%;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-image: linear-gradient(\\r\\n    90deg,\\r\\n    rgba(255, 255, 255, 0) 0%,\\r\\n    rgba(255, 255, 255, 0.05) 50%,\\r\\n    rgba(255, 255, 255, 0) 100%\\r\\n  );\\r\\n  transition: left 0.5s ease;\\r\\n}\\r\\n\\r\\n.btn-shine:hover::after {\\r\\n  left: 100%;\\r\\n}\\r\\n\\r\\n/* Dot pattern */\\r\\n.dot-pattern {\\r\\n  background-image: radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .dot-pattern {\\r\\n  background-image: radial-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Flow border effect */\\r\\n.flow-border {\\r\\n  position: relative;\\r\\n  z-index: 0;\\r\\n}\\r\\n\\r\\n.flow-border::before {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: -2px;\\r\\n  left: -2px;\\r\\n  right: -2px;\\r\\n  bottom: -2px;\\r\\n  background: var(\\r\\n    --bg-flow-border,\\r\\n    linear-gradient(\\r\\n      90deg,\\r\\n      var(--border-start-color),\\r\\n      var(--border-mid-color),\\r\\n      var(--border-end-color),\\r\\n      var(--border-start-color)\\r\\n    )\\r\\n  );\\r\\n  border-radius: inherit;\\r\\n  z-index: -1;\\r\\n  background-size: 300% 100%;\\r\\n  animation: border-flow 3s ease infinite;\\r\\n  opacity: 0;\\r\\n  transition: opacity 0.3s ease;\\r\\n}\\r\\n\\r\\n.flow-border:hover::before {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n/* Glow effect */\\r\\n.glow-effect {\\r\\n  transition: box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\n.glow-effect:hover {\\r\\n  --glow-color: var(--theme-blue-rgb, 33, 150, 243);\\r\\n  box-shadow: 0 0 10px 2px rgba(var(--glow-color), 0.3);\\r\\n}\\r\\n\\r\\n.glow-effect.purple:hover {\\r\\n  --glow-color: var(--theme-purple-rgb, 156, 39, 176);\\r\\n}\\r\\n\\r\\n.glow-effect.teal:hover {\\r\\n  --glow-color: var(--theme-teal-rgb, 0, 188, 212);\\r\\n}\\r\\n\\r\\n/* Theme transition */\\r\\n.theme-transition {\\r\\n  transition: all var(--theme-transition);\\r\\n}\\r\\n\\r\\n/* Page transition */\\r\\n.page-transition-enter {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n}\\r\\n\\r\\n.page-transition-enter-active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n.page-transition-exit {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n.page-transition-exit-active {\\r\\n  opacity: 0;\\r\\n  transform: translateY(-10px);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n/* Nav link with indicator */\\r\\n.nav-link {\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.nav-link::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  bottom: -4px;\\r\\n  left: 0;\\r\\n  width: 0;\\r\\n  height: 2px;\\r\\n  background-color: hsl(var(--theme-blue));\\r\\n  transition: width 0.3s ease;\\r\\n}\\r\\n\\r\\n.nav-link:hover::after,\\r\\n.nav-link.active::after {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n  height: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: transparent;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: rgba(255, 255, 255, 0.1);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(255, 255, 255, 0.2);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb {\\r\\n  background: rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(0, 0, 0, 0.2);\\r\\n}\\r\\n\\r\\n/* Animation for loading spinner */\\r\\n@keyframes border-flow {\\r\\n  0% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n  100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes subtle-pulse {\\r\\n  0% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 1;\\r\\n  }\\r\\n  100% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-subtle-pulse {\\r\\n  animation: subtle-pulse 2s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-rotate-loader {\\r\\n  animation: spin 1s linear infinite;\\r\\n}\\r\\n\\r\\n@keyframes spin {\\r\\n  from {\\r\\n    transform: rotate(0deg);\\r\\n  }\\r\\n  to {\\r\\n    transform: rotate(360deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9jc3MtbG9hZGVyL3NyYy9pbmRleC5qcz8/cnVsZVNldFsxXS5ydWxlc1s3XS5vbmVPZlsxNF0udXNlWzFdIS4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvcG9zdGNzcy1sb2FkZXIvc3JjL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzddLm9uZU9mWzE0XS51c2VbMl0hLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7O0FBQUE7QUFDd0g7QUFDeEgsOEJBQThCLGtIQUEyQjtBQUN6RDtBQUNBLGdFQUFnRSw2QkFBNkIsNkJBQTZCLHdCQUF3Qix3QkFBd0IsbUJBQW1CLG1CQUFtQixtQkFBbUIsb0JBQW9CLG9CQUFvQixrQkFBa0Isa0JBQWtCLHVCQUF1QiwyQ0FBMkMsbUNBQW1DLGtDQUFrQyxpQ0FBaUMsb0JBQW9CLHlCQUF5QiwyQkFBMkIsNEJBQTRCLDZCQUE2Qix1QkFBdUIsZ0NBQWdDLGlDQUFpQywyQ0FBMkMsdUNBQXVDLGdDQUFnQywyQkFBMkIsbUNBQW1DLGlCQUFpQix1QkFBdUIscUJBQXFCLHNCQUFzQix1QkFBdUIsbUJBQW1CLHFCQUFxQixrQkFBa0Isd0JBQXdCLDBCQUEwQixnQ0FBZ0MsOEJBQThCLCtCQUErQixnQ0FBZ0MsNEJBQTRCLDZCQUE2Qiw4QkFBOEIsMkJBQTJCLHlCQUF5QiwyQkFBMkIsMEJBQTBCLDBCQUEwQixHQUFHLGdCQUFnQiw2QkFBNkIsNkJBQTZCLHdCQUF3Qix3QkFBd0IsbUJBQW1CLG1CQUFtQixtQkFBbUIsb0JBQW9CLG9CQUFvQixrQkFBa0Isa0JBQWtCLHVCQUF1QiwyQ0FBMkMsbUNBQW1DLGtDQUFrQyxpQ0FBaUMsb0JBQW9CLHlCQUF5QiwyQkFBMkIsNEJBQTRCLDZCQUE2Qix1QkFBdUIsZ0NBQWdDLGlDQUFpQywyQ0FBMkMsdUNBQXVDLGdDQUFnQywyQkFBMkIsbUNBQW1DLGlCQUFpQix1QkFBdUIscUJBQXFCLHNCQUFzQix1QkFBdUIsbUJBQW1CLHFCQUFxQixrQkFBa0Isd0JBQXdCLDBCQUEwQixnQ0FBZ0MsOEJBQThCLCtCQUErQixnQ0FBZ0MsNEJBQTRCLDZCQUE2Qiw4QkFBOEIsMkJBQTJCLHlCQUF5QiwyQkFBMkIsMEJBQTBCLDBCQUEwQixHQUFHLGlWQUFpViw0QkFBNEIsNEJBQTRCLGdDQUFnQyxrQ0FBa0MsVUFBVSx3QkFBd0IscUJBQXFCLEdBQUcsK2FBQSthLHNCQUFzQiwyQ0FBMkMsNkJBQTZCLDBCQUEwQixvQkFBb0Isb0pBQW9KLDBDQUEwQyw0Q0FBNEMscURBQXFELFVBQVUsZ0tBQWdLLGVBQWUsaUNBQWlDLFVBQVUsMk5BQTJOLGVBQWUsMkJBQTJCLGtDQUFrQyxVQUFVLGlHQUFpRyw4Q0FBOEMsOENBQThDLEdBQUcsa0dBQWtHLHVCQUF1Qix5QkFBeUIsR0FBRyxpRkFBaUYsbUJBQW1CLDZCQUE2QixHQUFHLDJFQUEyRSx3QkFBd0IsR0FBRywwU0FBMFMseUhBQXlILDBDQUEwQyw0Q0FBNEMsMkJBQTJCLFVBQVUsaUVBQWlFLG1CQUFtQixHQUFHLDJHQUEyRyxtQkFBbUIsbUJBQW1CLHVCQUF1Qiw2QkFBNkIsR0FBRyxTQUFTLG9CQUFvQixHQUFHLFNBQVMsZ0JBQWdCLEdBQUcsZ2JBQWdiLG9CQUFvQixrQ0FBa0Msc0NBQXNDLFVBQVUsa01BQWtNLDBCQUEwQiwyQ0FBMkMsNkNBQTZDLDRCQUE0QixpQ0FBaUMsaUNBQWlDLG9DQUFvQywyQkFBMkIsc0JBQXNCLHVCQUF1QixVQUFVLDhGQUE4Rix5QkFBeUIsR0FBRywwTkFBME4sZ0NBQWdDLDBDQUEwQyxtQ0FBbUMsVUFBVSwrRkFBK0Ysa0JBQWtCLEdBQUcsK01BQStNLHFCQUFxQixHQUFHLG1GQUFtRiw2QkFBNkIsR0FBRyxpSkFBaUosaUJBQWlCLEdBQUcsNkhBQTZILG1DQUFtQyxpQ0FBaUMsVUFBVSxvR0FBb0csNkJBQTZCLEdBQUcscUtBQXFLLGdDQUFnQywwQkFBMEIsVUFBVSxzRUFBc0UsdUJBQXVCLEdBQUcsNEpBQTRKLGNBQWMsR0FBRyxjQUFjLGNBQWMsZUFBZSxHQUFHLFlBQVksZUFBZSxHQUFHLG9CQUFvQixxQkFBcUIsY0FBYyxlQUFlLEdBQUcsd0RBQXdELGVBQWUsR0FBRyw2RUFBNkUscUJBQXFCLEdBQUcsa1FBQWtRLGdCQUFnQiwyQkFBMkIsVUFBVSxnREFBZ0QsZ0JBQWdCLDJCQUEyQixVQUFVLCtFQUErRSxvQkFBb0IsR0FBRyxpRkFBaUYsb0JBQW9CLEdBQUcsbWJBQW1iLG9CQUFvQixtQ0FBbUMsVUFBVSx3S0FBd0ssb0JBQW9CLGlCQUFpQixHQUFHLCtIQUErSCxrQkFBa0IsR0FBRyxhQUFhLGdDQUFnQyxtQ0FBbUMsMEJBQTBCLHdDQUF3Qyw2QkFBNkIsMkNBQTJDLGdDQUFnQyx1Q0FBdUMsb0NBQW9DLDZDQUE2QyxnQ0FBZ0MsMkNBQTJDLGlDQUFpQywwQ0FBMEMscUNBQXFDLDJDQUEyQywrQkFBK0IsOEJBQThCLDZCQUE2Qix5QkFBeUIsaUVBQWlFLHFDQUFxQyxtQ0FBbUMsa0VBQWtFLDBGQUEwRixvREFBb0QsbURBQW1ELHdFQUF3RSxPQUFPLGlCQUFpQixtQ0FBbUMsK0JBQStCLDZCQUE2QixvQ0FBb0MsZ0NBQWdDLHVDQUF1Qyw0QkFBNEIsMkNBQTJDLG9DQUFvQyx5Q0FBeUMsZ0NBQWdDLHlDQUF5QyxpQ0FBaUMsc0NBQXNDLHFDQUFxQywyQ0FBMkMsaUNBQWlDLGdDQUFnQywrQkFBK0IsaUVBQWlFLHFDQUFxQyxtQ0FBbUMsa0VBQWtFLDBGQUEwRixvREFBb0QsbURBQW1ELE9BQU8sU0FBUyxxQ0FBcUMsR0FBRyxZQUFZLDZDQUE2QyxrQ0FBa0Msc0RBQXNELDRGQUE0RixHQUFHLGdCQUFnQixnQkFBZ0IsdUJBQXVCLHNCQUFzQix3QkFBd0IsdUJBQXVCLEdBQUcsZ0NBQWdDLGtCQUFrQix3QkFBd0IsS0FBSyxHQUFHLGNBQWMsdUJBQXVCLGVBQWUsZ0JBQWdCLGVBQWUsaUJBQWlCLHFCQUFxQiwyQkFBMkIsd0JBQXdCLG9CQUFvQixHQUFHLDBCQUEwQix5QkFBeUIsR0FBRyxjQUFjLHdCQUF3QixHQUFHLFlBQVksb0JBQW9CLEdBQUcsZUFBZSx1QkFBdUIsR0FBRyxlQUFlLHVCQUF1QixHQUFHLGNBQWMsZUFBZSxHQUFHLGFBQWEsa0JBQWtCLEdBQUcsZUFBZSxtQkFBbUIsR0FBRyxnQkFBZ0IsaUJBQWlCLEdBQUcsZ0JBQWdCLGtCQUFrQixHQUFHLGtCQUFrQixpQkFBaUIsR0FBRyxhQUFhLGNBQWMsR0FBRyxpQkFBaUIsY0FBYyxHQUFHLGlCQUFpQixjQUFjLEdBQUcsY0FBYyxpQkFBaUIsR0FBRyxhQUFhLGlCQUFpQixHQUFHLHVCQUF1QixjQUFjLEdBQUcsY0FBYyxlQUFlLEdBQUcsZUFBZSxrQkFBa0IsR0FBRyxjQUFjLGtCQUFrQixHQUFHLGVBQWUsZ0JBQWdCLEdBQUcsY0FBYyxnQkFBZ0IsR0FBRyxZQUFZLGFBQWEsR0FBRyxhQUFhLGNBQWMsR0FBRyxZQUFZLGNBQWMsR0FBRyxhQUFhLGVBQWUsR0FBRyxZQUFZLGNBQWMsR0FBRyxzQkFBc0IsYUFBYSxHQUFHLGFBQWEsa0JBQWtCLEdBQUcsY0FBYyx1QkFBdUIsR0FBRyxZQUFZLGlCQUFpQixHQUFHLFVBQVUsZUFBZSxHQUFHLFdBQVcsZ0JBQWdCLEdBQUcsV0FBVyxnQkFBZ0IsR0FBRyxXQUFXLGdCQUFnQixHQUFHLFdBQVcsZ0JBQWdCLEdBQUcsaUJBQWlCLGlDQUFpQyxHQUFHLFlBQVksMEJBQTBCLDJCQUEyQixHQUFHLFdBQVcseUJBQXlCLDBCQUEwQixHQUFHLFdBQVcsd0JBQXdCLHlCQUF5QixHQUFHLGNBQWMsc0JBQXNCLHVCQUF1QixHQUFHLFdBQVcsd0JBQXdCLDJCQUEyQixHQUFHLFdBQVcscUJBQXFCLHdCQUF3QixHQUFHLFdBQVcsdUJBQXVCLDBCQUEwQixHQUFHLFlBQVksMEJBQTBCLEdBQUcsV0FBVywyQkFBMkIsR0FBRyxZQUFZLHdCQUF3QixHQUFHLFlBQVksd0JBQXdCLEdBQUcsV0FBVywwQkFBMEIsR0FBRyxXQUFXLDJCQUEyQixHQUFHLFdBQVcsd0JBQXdCLEdBQUcsV0FBVywwQkFBMEIsR0FBRyxXQUFXLHdCQUF3QixHQUFHLFdBQVcseUJBQXlCLEdBQUcsV0FBVyx3QkFBd0IsR0FBRyxXQUFXLDBCQUEwQixHQUFHLFdBQVcseUJBQXlCLEdBQUcsV0FBVyxvQkFBb0IsR0FBRyxlQUFlLHlCQUF5QixHQUFHLFdBQVcsd0JBQXdCLEdBQUcsWUFBWSxxQkFBcUIsR0FBRyxZQUFZLHFCQUFxQixHQUFHLFdBQVcsdUJBQXVCLEdBQUcsWUFBWSxxQkFBcUIsR0FBRyxXQUFXLHFCQUFxQixHQUFHLFdBQVcsdUJBQXVCLEdBQUcsV0FBVyxxQkFBcUIsR0FBRyxzQkFBc0IsdUJBQXVCLEdBQUcsY0FBYyxxQkFBcUIsR0FBRyxZQUFZLG1CQUFtQixHQUFHLG1CQUFtQiwwQkFBMEIsR0FBRyxXQUFXLGtCQUFrQixHQUFHLGtCQUFrQix5QkFBeUIsR0FBRyxZQUFZLG1CQUFtQixHQUFHLFdBQVcsa0JBQWtCLEdBQUcsYUFBYSxrQkFBa0IsR0FBRyxvQkFBb0Isd0JBQXdCLEdBQUcsbUJBQW1CLHlCQUF5QixHQUFHLFVBQVUsZ0JBQWdCLEdBQUcsVUFBVSxvQkFBb0IsR0FBRyxXQUFXLG1CQUFtQixHQUFHLFdBQVcsb0JBQW9CLEdBQUcsV0FBVyxpQkFBaUIsR0FBRyxXQUFXLGlCQUFpQixHQUFHLFVBQVUsbUJBQW1CLEdBQUcsY0FBYyxxQkFBcUIsR0FBRyxXQUFXLGlCQUFpQixHQUFHLFdBQVcsaUJBQWlCLEdBQUcsVUFBVSxvQkFBb0IsR0FBRyxjQUFjLHFCQUFxQixHQUFHLFdBQVcsaUJBQWlCLEdBQUcsVUFBVSxpQkFBaUIsR0FBRyxXQUFXLGtCQUFrQixHQUFHLFVBQVUsb0JBQW9CLEdBQUcsVUFBVSxtQkFBbUIsR0FBRyxVQUFVLG9CQUFvQixHQUFHLFVBQVUsaUJBQWlCLEdBQUcsVUFBVSxvQkFBb0IsR0FBRyxrQkFBa0IsZ0JBQWdCLEdBQUcscURBQXFELCtDQUErQyxHQUFHLGFBQWEsaUJBQWlCLEdBQUcsYUFBYSxpQkFBaUIsR0FBRyxXQUFXLGdCQUFnQixHQUFHLHdCQUF3QixzQkFBc0IsR0FBRyx3QkFBd0Isc0JBQXNCLEdBQUcsdUJBQXVCLHFCQUFxQixHQUFHLG1CQUFtQixzQkFBc0IsR0FBRyxVQUFVLGVBQWUsR0FBRyxXQUFXLGtCQUFrQixHQUFHLFdBQVcsZ0JBQWdCLEdBQUcsV0FBVyxnQkFBZ0IsR0FBRyxVQUFVLGtCQUFrQixHQUFHLGNBQWMsb0JBQW9CLEdBQUcsV0FBVyxnQkFBZ0IsR0FBRyxXQUFXLGdCQUFnQixHQUFHLFVBQVUsbUJBQW1CLEdBQUcsY0FBYyxvQkFBb0IsR0FBRyxXQUFXLGdCQUFnQixHQUFHLFVBQVUsZ0JBQWdCLEdBQUcsV0FBVyxpQkFBaUIsR0FBRyxVQUFVLG1CQUFtQixHQUFHLFVBQVUsa0JBQWtCLEdBQUcsVUFBVSxnQkFBZ0IsR0FBRyxrQkFBa0IsZUFBZSxHQUFHLFlBQVksNEJBQTRCLHVCQUF1QixHQUFHLGFBQWEsZ0JBQWdCLEdBQUcsdUJBQXVCLG9CQUFvQixHQUFHLHdEQUF3RCxpREFBaUQsR0FBRyxnQkFBZ0IscUJBQXFCLEdBQUcsZ0JBQWdCLHFCQUFxQixHQUFHLGdCQUFnQixxQkFBcUIsR0FBRyxnQkFBZ0IscUJBQXFCLEdBQUcsZUFBZSxxQkFBcUIsR0FBRyxlQUFlLHFCQUFxQixHQUFHLGVBQWUscUJBQXFCLEdBQUcsYUFBYSxpQkFBaUIsR0FBRyxlQUFlLG1CQUFtQixHQUFHLGdCQUFnQixpQkFBaUIsR0FBRyxXQUFXLGlCQUFpQixHQUFHLHNCQUFzQiw4QkFBOEIsR0FBRyx5QkFBeUIsMkJBQTJCLG9NQUFvTSxHQUFHLCtCQUErQiwyQkFBMkIsb01BQW9NLEdBQUcsb0JBQW9CLDhCQUE4QixvTUFBb00sR0FBRywrQkFBK0IsMkJBQTJCLG9NQUFvTSxHQUFHLGVBQWUsc0JBQXNCLG9NQUFvTSxHQUFHLGdCQUFnQix1QkFBdUIsdUJBQXVCLG9NQUFvTSxHQUFHLGdCQUFnQixvTUFBb00sR0FBRywyQkFBMkIsZ0JBQWdCLGtDQUFrQyxLQUFLLFdBQVcsb0NBQW9DLEtBQUssR0FBRyx5QkFBeUIsMkNBQTJDLEdBQUcsc0JBQXNCLFdBQVcsa0JBQWtCLEtBQUssR0FBRyxvQkFBb0IsOERBQThELEdBQUcscUJBQXFCLFVBQVUsZ0NBQWdDLEtBQUssR0FBRyxtQkFBbUIsdUNBQXVDLEdBQUcscUJBQXFCLG9CQUFvQixHQUFHLGtCQUFrQixpQkFBaUIsR0FBRyx5QkFBeUIsd0JBQXdCLEdBQUcscUJBQXFCLG9CQUFvQixHQUFHLGlCQUFpQix1QkFBdUIsR0FBRyxrQkFBa0IsOEJBQThCLDhCQUE4Qiw4QkFBOEIsR0FBRyxrQkFBa0IsaUJBQWlCLEdBQUcsYUFBYSxpQkFBaUIsR0FBRyxrQkFBa0IscURBQXFELEdBQUcsa0JBQWtCLHFEQUFxRCxHQUFHLGVBQWUsMkJBQTJCLEdBQUcsdUJBQXVCLG1DQUFtQyxHQUFHLGdCQUFnQixvQkFBb0IsR0FBRyxrQkFBa0IsNEJBQTRCLEdBQUcsZ0JBQWdCLDBCQUEwQixHQUFHLG1CQUFtQix3QkFBd0IsR0FBRyxxQkFBcUIsNEJBQTRCLEdBQUcsc0JBQXNCLG1DQUFtQyxHQUFHLFlBQVksaUJBQWlCLEdBQUcsWUFBWSxnQkFBZ0IsR0FBRyxZQUFZLGlCQUFpQixHQUFHLFlBQVksY0FBYyxHQUFHLFlBQVksZ0JBQWdCLEdBQUcsWUFBWSxjQUFjLEdBQUcsa0RBQWtELDRCQUE0QiwyREFBMkQsb0VBQW9FLEdBQUcsa0RBQWtELDRCQUE0Qix5REFBeUQsa0VBQWtFLEdBQUcsa0RBQWtELDRCQUE0QiwyREFBMkQsb0VBQW9FLEdBQUcsa0RBQWtELDRCQUE0Qix5REFBeUQsa0VBQWtFLEdBQUcsa0RBQWtELDRCQUE0QixvRUFBb0UsNkRBQTZELEdBQUcsc0RBQXNELDRCQUE0QixxRUFBcUUsOERBQThELEdBQUcsa0RBQWtELDRCQUE0QixtRUFBbUUsNERBQTRELEdBQUcsa0RBQWtELDRCQUE0QixvRUFBb0UsNkRBQTZELEdBQUcsa0RBQWtELDRCQUE0QixpRUFBaUUsMERBQTBELEdBQUcsa0RBQWtELDRCQUE0QixtRUFBbUUsNERBQTRELEdBQUcsa0RBQWtELDRCQUE0QixpRUFBaUUsMERBQTBELEdBQUcsc0JBQXNCLHFCQUFxQixHQUFHLHNCQUFzQixxQkFBcUIsR0FBRyxlQUFlLHFCQUFxQiw0QkFBNEIsd0JBQXdCLEdBQUcsd0JBQXdCLHdCQUF3QixHQUFHLGNBQWMsMkJBQTJCLEdBQUcsbUJBQW1CLDBCQUEwQixHQUFHLGlCQUFpQixpQ0FBaUMsR0FBRyxpQkFBaUIsNkNBQTZDLEdBQUcsaUJBQWlCLDZDQUE2QyxHQUFHLGlCQUFpQiwyQkFBMkIsR0FBRyxhQUFhLHNCQUFzQixHQUFHLGVBQWUsc0JBQXNCLEdBQUcsZUFBZSxzQkFBc0IsR0FBRyxlQUFlLDZCQUE2QixHQUFHLGlCQUFpQiw2QkFBNkIsR0FBRywwQkFBMEIsOEJBQThCLEdBQUcsMEJBQTBCLDRCQUE0QixHQUFHLDBCQUEwQiw2QkFBNkIsR0FBRyxlQUFlLDBCQUEwQixHQUFHLGlCQUFpQiwwQkFBMEIsR0FBRyxvQkFBb0IseUJBQXlCLEdBQUcsdUJBQXVCLDJCQUEyQixpRUFBaUUsR0FBRyw0QkFBNEIsdUNBQXVDLEdBQUcsd0JBQXdCLG1DQUFtQyxHQUFHLG9CQUFvQixxQ0FBcUMsR0FBRyx5QkFBeUIsMENBQTBDLEdBQUcsOEJBQThCLGdEQUFnRCxHQUFHLHNCQUFzQiwyQkFBMkIsaUVBQWlFLEdBQUcsc0JBQXNCLDJCQUEyQixpRUFBaUUsR0FBRyxzQkFBc0IsMkJBQTJCLGlFQUFpRSxHQUFHLHVCQUF1QiwyQkFBMkIsaUVBQWlFLEdBQUcsNEJBQTRCLHNDQUFzQyxHQUFHLG1CQUFtQixvQ0FBb0MsR0FBRyw4QkFBOEIsK0NBQStDLEdBQUcsbUNBQW1DLHNEQUFzRCxHQUFHLHFCQUFxQixzQ0FBc0MsR0FBRyxxQkFBcUIsMkJBQTJCLGlFQUFpRSxHQUFHLHFCQUFxQiwyQkFBMkIsK0RBQStELEdBQUcsMEJBQTBCLHVDQUF1QyxHQUFHLHdCQUF3Qix5Q0FBeUMsR0FBRyw2QkFBNkIsK0NBQStDLEdBQUcsK0JBQStCLGlEQUFpRCxHQUFHLDZCQUE2QiwrQ0FBK0MsR0FBRyx3QkFBd0IseUNBQXlDLEdBQUcsd0JBQXdCLHlDQUF5QyxHQUFHLHdCQUF3Qix5Q0FBeUMsR0FBRywrQkFBK0IsaURBQWlELEdBQUcsK0JBQStCLHNEQUFzRCxHQUFHLDJCQUEyQixtQ0FBbUMsR0FBRywyQkFBMkIsb0NBQW9DLEdBQUcsa0JBQWtCLHVCQUF1QixpRUFBaUUsR0FBRyxtQkFBbUIsdUJBQXVCLGdFQUFnRSxHQUFHLHdCQUF3Qiw0Q0FBNEMsR0FBRyx3QkFBd0IsMkNBQTJDLEdBQUcsb0JBQW9CLDZDQUE2QyxHQUFHLHlCQUF5QixtREFBbUQsR0FBRyx5QkFBeUIsbURBQW1ELEdBQUcseUJBQXlCLG9EQUFvRCxHQUFHLG9CQUFvQix1Q0FBdUMsR0FBRyxvQkFBb0IsdUNBQXVDLEdBQUcsZ0JBQWdCLHlDQUF5QyxHQUFHLGNBQWMsdUNBQXVDLEdBQUcscUJBQXFCLDhDQUE4QyxHQUFHLDBCQUEwQixvREFBb0QsR0FBRyxrQkFBa0IsdUJBQXVCLGlFQUFpRSxHQUFHLGtCQUFrQix1QkFBdUIsaUVBQWlFLEdBQUcsa0JBQWtCLHVCQUF1Qiw4REFBOEQsR0FBRyxrQkFBa0IsdUJBQXVCLDhEQUE4RCxHQUFHLGtCQUFrQix1QkFBdUIsaUVBQWlFLEdBQUcsd0JBQXdCLDJDQUEyQyxHQUFHLHdCQUF3QiwwQ0FBMEMsR0FBRyxlQUFlLHdDQUF3QyxHQUFHLGlCQUFpQiwwQ0FBMEMsR0FBRyxpQkFBaUIsMENBQTBDLEdBQUcsc0JBQXNCLGdEQUFnRCxHQUFHLGdCQUFnQix1QkFBdUIsaUVBQWlFLEdBQUcsc0JBQXNCLDJDQUEyQyxHQUFHLHNCQUFzQiwyQ0FBMkMsR0FBRyxzQkFBc0IsMkNBQTJDLEdBQUcsc0JBQXNCLDBDQUEwQyxHQUFHLHNCQUFzQiwwQ0FBMEMsR0FBRyxtQkFBbUIsNENBQTRDLEdBQUcsb0JBQW9CLDZDQUE2QyxHQUFHLHlCQUF5QixtREFBbUQsR0FBRyx5QkFBeUIsbURBQW1ELEdBQUcsd0JBQXdCLG9EQUFvRCxHQUFHLHNCQUFzQiwrQ0FBK0MsR0FBRywyQkFBMkIscURBQXFELEdBQUcsMkJBQTJCLHFEQUFxRCxHQUFHLHlCQUF5QixtREFBbUQsR0FBRyxxQkFBcUIsa0NBQWtDLEdBQUcsZUFBZSx1QkFBdUIsaUVBQWlFLEdBQUcsb0JBQW9CLDZDQUE2QyxHQUFHLG1CQUFtQiw4Q0FBOEMsR0FBRyx1QkFBdUIsMkVBQTJFLEdBQUcsd0JBQXdCLGlGQUFpRixHQUFHLHVCQUF1QiwwRUFBMEUsR0FBRyxxQkFBcUIsaUVBQWlFLHlFQUF5RSx3RUFBd0UsR0FBRyxtQkFBbUIsaUVBQWlFLDBFQUEwRSx3RUFBd0UsR0FBRyxvQkFBb0IsaUVBQWlFLHVFQUF1RSx3RUFBd0UsR0FBRyxxQkFBcUIsaUVBQWlFLHdFQUF3RSx3RUFBd0UsR0FBRyxzQkFBc0IsZ0ZBQWdGLGdGQUFnRix3RUFBd0UsR0FBRywyQkFBMkIsc0ZBQXNGLGdGQUFnRix3RUFBd0UsR0FBRyx3QkFBd0Isa0ZBQWtGLGtGQUFrRix3RUFBd0UsR0FBRyw0QkFBNEIseUZBQXlGLGtGQUFrRix3RUFBd0UsR0FBRyxxQkFBcUIsMEVBQTBFLGlIQUFpSCxHQUFHLGVBQWUsMERBQTBELEdBQUcscUJBQXFCLDZEQUE2RCxHQUFHLGtCQUFrQiw2REFBNkQsR0FBRyxvQkFBb0IsNkRBQTZELEdBQUcsb0JBQW9CLDZEQUE2RCxHQUFHLHlCQUF5QiwyRUFBMkUsR0FBRyxvQkFBb0IsNkRBQTZELEdBQUcsb0JBQW9CLDRFQUE0RSxHQUFHLHNCQUFzQiw4RUFBOEUsR0FBRyxlQUFlLDBEQUEwRCxHQUFHLHdCQUF3QixtRkFBbUYsR0FBRyxzQ0FBc0MsK0JBQStCLEdBQUcsbUJBQW1CLGtDQUFrQyxrQ0FBa0MsR0FBRyxtQkFBbUIsdUJBQXVCLEdBQUcsbUJBQW1CLHlCQUF5Qix5QkFBeUIsR0FBRyxVQUFVLHFCQUFxQixHQUFHLFVBQVUsb0JBQW9CLEdBQUcsVUFBVSxxQkFBcUIsR0FBRyxVQUFVLGtCQUFrQixHQUFHLFVBQVUscUJBQXFCLEdBQUcsVUFBVSxvQkFBb0IsR0FBRyxVQUFVLGtCQUFrQixHQUFHLFdBQVcseUJBQXlCLDBCQUEwQixHQUFHLFdBQVcsMEJBQTBCLDJCQUEyQixHQUFHLFdBQVcsdUJBQXVCLHdCQUF3QixHQUFHLFdBQVcsdUJBQXVCLHdCQUF3QixHQUFHLFdBQVcseUJBQXlCLDRCQUE0QixHQUFHLGVBQWUsMEJBQTBCLDZCQUE2QixHQUFHLFlBQVksd0JBQXdCLDJCQUEyQixHQUFHLFlBQVksc0JBQXNCLHlCQUF5QixHQUFHLFdBQVcsd0JBQXdCLDJCQUEyQixHQUFHLFlBQVksc0JBQXNCLHlCQUF5QixHQUFHLFdBQVcseUJBQXlCLDRCQUE0QixHQUFHLFdBQVcsc0JBQXNCLHlCQUF5QixHQUFHLFdBQVcsd0JBQXdCLDJCQUEyQixHQUFHLFdBQVcsc0JBQXNCLHlCQUF5QixHQUFHLFdBQVcsMkJBQTJCLEdBQUcsWUFBWSx5QkFBeUIsR0FBRyxXQUFXLHVCQUF1QixHQUFHLFlBQVksd0JBQXdCLEdBQUcsV0FBVywwQkFBMEIsR0FBRyxXQUFXLHFCQUFxQixHQUFHLFdBQVcsd0JBQXdCLEdBQUcsV0FBVyx3QkFBd0IsR0FBRyxnQkFBZ0IscUJBQXFCLEdBQUcsa0JBQWtCLHVCQUF1QixHQUFHLGVBQWUsc0JBQXNCLHNCQUFzQixHQUFHLGVBQWUsd0JBQXdCLHlCQUF5QixHQUFHLGVBQWUsdUJBQXVCLHdCQUF3QixHQUFHLGNBQWMsd0JBQXdCLHlCQUF5QixHQUFHLGNBQWMsd0JBQXdCLHlCQUF5QixHQUFHLGNBQWMsdUJBQXVCLHlCQUF5QixHQUFHLGNBQWMsdUJBQXVCLHNCQUFzQixHQUFHLGdCQUFnQixxQkFBcUIsR0FBRyxrQkFBa0IscUJBQXFCLEdBQUcsb0JBQW9CLHFCQUFxQixHQUFHLG1CQUFtQixtQkFBbUIsR0FBRyxxQkFBcUIsNkJBQTZCLEdBQUcscUJBQXFCLHlCQUF5Qix1REFBdUQsR0FBRyxxQkFBcUIseUJBQXlCLHVEQUF1RCxHQUFHLHFCQUFxQix5QkFBeUIsc0RBQXNELEdBQUcsaUJBQWlCLHlCQUF5QixrREFBa0QsR0FBRywyQkFBMkIsdUNBQXVDLEdBQUcsbUJBQW1CLHdCQUF3QixHQUFHLHVCQUF1QixtQ0FBbUMsR0FBRyxrQ0FBa0MsOENBQThDLEdBQUcsc0JBQXNCLGtDQUFrQyxHQUFHLG9CQUFvQix5QkFBeUIsd0RBQXdELEdBQUcsb0JBQW9CLHlCQUF5Qix3REFBd0QsR0FBRyxvQkFBb0IseUJBQXlCLHdEQUF3RCxHQUFHLG9CQUFvQix5QkFBeUIscURBQXFELEdBQUcsb0JBQW9CLHlCQUF5QixxREFBcUQsR0FBRyxvQkFBb0IseUJBQXlCLHFEQUFxRCxHQUFHLHFCQUFxQix5QkFBeUIsdURBQXVELEdBQUcscUJBQXFCLHlCQUF5QixzREFBc0QsR0FBRyxxQkFBcUIseUJBQXlCLHNEQUFzRCxHQUFHLDRCQUE0Qix3Q0FBd0MsR0FBRyw4QkFBOEIsMENBQTBDLEdBQUcsbUJBQW1CLCtCQUErQixHQUFHLDhCQUE4QiwwQ0FBMEMsR0FBRyxtQkFBbUIseUJBQXlCLHdEQUF3RCxHQUFHLG1CQUFtQix5QkFBeUIsc0RBQXNELEdBQUcsbUJBQW1CLHlCQUF5QixzREFBc0QsR0FBRyxnQ0FBZ0MsNENBQTRDLEdBQUcsc0JBQXNCLGtDQUFrQyxHQUFHLHdCQUF3QixvQ0FBb0MsR0FBRyxzQkFBc0Isa0NBQWtDLEdBQUcsdUJBQXVCLHVCQUF1QixHQUFHLGlCQUFpQix5QkFBeUIsd0RBQXdELEdBQUcseUJBQXlCLCtCQUErQixHQUFHLGdCQUFnQixlQUFlLEdBQUcsaUJBQWlCLGtCQUFrQixHQUFHLGlCQUFpQixpQkFBaUIsR0FBRyxpQkFBaUIsaUJBQWlCLEdBQUcsaUJBQWlCLGtCQUFrQixHQUFHLGlCQUFpQixpQkFBaUIsR0FBRyxnQkFBZ0Isb0ZBQW9GLHdHQUF3Ryw0R0FBNEcsR0FBRyxnQkFBZ0Isa0ZBQWtGLHNHQUFzRyw0R0FBNEcsR0FBRyxnQkFBZ0IsK0NBQStDLDREQUE0RCw0R0FBNEcsR0FBRyxnQkFBZ0IscUZBQXFGLHlHQUF5Ryw0R0FBNEcsR0FBRyxpQkFBaUIscURBQXFELGtFQUFrRSw0R0FBNEcsR0FBRywrQkFBK0Isc0RBQXNELDBDQUEwQyxHQUFHLCtCQUErQixzREFBc0QsMENBQTBDLEdBQUcsbUJBQW1CLG1DQUFtQyx3QkFBd0IsR0FBRyxjQUFjLHlCQUF5QixHQUFHLDZCQUE2QixtREFBbUQsR0FBRyxXQUFXLHlCQUF5QixzTEFBc0wsR0FBRyxhQUFhLHNMQUFzTCxHQUFHLHVCQUF1QixtQ0FBbUMsb1JBQW9SLDRRQUE0USxHQUFHLHVCQUF1QixrQ0FBa0Msb1JBQW9SLDRRQUE0USxHQUFHLGlCQUFpQixxS0FBcUssNkpBQTZKLHNMQUFzTCw2REFBNkQsK0JBQStCLEdBQUcscUJBQXFCLDZCQUE2Qiw2REFBNkQsK0JBQStCLEdBQUcsd0JBQXdCLG9HQUFvRyw2REFBNkQsK0JBQStCLEdBQUcseUJBQXlCLGlDQUFpQyw2REFBNkQsK0JBQStCLEdBQUcsMkJBQTJCLG1DQUFtQyw2REFBNkQsK0JBQStCLEdBQUcsbUJBQW1CLCtCQUErQixHQUFHLG1CQUFtQiwrQkFBK0IsR0FBRyxtQkFBbUIsK0JBQStCLEdBQUcseURBQXlELDBDQUEwQyxHQUFHLHNCQUFzQixZQUFZLDBDQUEwQyx3TkFBd04sS0FBSyxHQUFHLHFCQUFxQixVQUFVLHlDQUF5QyxrTkFBa04sS0FBSyxHQUFHLGlCQUFpQiwwQkFBMEIsOEJBQThCLGdDQUFnQyw4QkFBOEIsK0JBQStCLG9DQUFvQyxvQ0FBb0MsR0FBRyxpQkFBaUIsNEJBQTRCLEdBQUcsbUJBQW1CLDhCQUE4QixHQUFHLG1CQUFtQiw4QkFBOEIsR0FBRyxtQkFBbUIsOEJBQThCLEdBQUcsb0RBQW9ELDRDQUE0QyxpQ0FBaUMseUNBQXlDLGtEQUFrRCxpR0FBaUcsS0FBSyw4QkFBOEIsc0NBQXNDLDRDQUE0QyxLQUFLLHlEQUF5RCxzREFBc0QscURBQXFELEtBQUssb0NBQW9DLGdEQUFnRCxLQUFLLG9EQUFvRCxnQ0FBZ0MsS0FBSyw2QkFBNkIsa0RBQWtELEtBQUssb0NBQW9DLDRDQUE0QyxLQUFLLCtDQUErQyw2RUFBNkUsb0NBQW9DLDJDQUEyQyw0QkFBNEIscURBQXFELEtBQUssK0JBQStCLHVFQUF1RSxvQ0FBb0MsMkNBQTJDLDRCQUE0QixLQUFLLHNEQUFzRCxpQ0FBaUMsMkRBQTJELEtBQUssNkJBQTZCLHlLQUF5SyxLQUFLLG1EQUFtRCxpQkFBaUIsa0NBQWtDLHlEQUF5RCxLQUFLLHdCQUF3QixpQkFBaUIsK0JBQStCLEtBQUssaURBQWlELHlCQUF5Qix1QkFBdUIsS0FBSywyQkFBMkIsb0JBQW9CLHlCQUF5QixhQUFhLGtCQUFrQixrQkFBa0IsbUJBQW1CLHlLQUF5SyxpQ0FBaUMsS0FBSyxpQ0FBaUMsaUJBQWlCLEtBQUssMkNBQTJDLHVGQUF1RixpQ0FBaUMsMkRBQTJELEtBQUssNkJBQTZCLGlGQUFpRixLQUFLLGtEQUFrRCx5QkFBeUIsaUJBQWlCLEtBQUssOEJBQThCLG9CQUFvQix5QkFBeUIsZ0JBQWdCLGlCQUFpQixrQkFBa0IsbUJBQW1CLG1QQUFtUCw2QkFBNkIsa0JBQWtCLGlDQUFpQyw4Q0FBOEMsaUJBQWlCLG9DQUFvQyxLQUFLLG9DQUFvQyxpQkFBaUIsS0FBSywyQ0FBMkMsdUNBQXVDLEtBQUssNEJBQTRCLHdEQUF3RCw0REFBNEQsS0FBSyxtQ0FBbUMsMERBQTBELEtBQUssaUNBQWlDLHVEQUF1RCxLQUFLLHFEQUFxRCw4Q0FBOEMsS0FBSyx5REFBeUQsaUJBQWlCLGtDQUFrQyxLQUFLLHVDQUF1QyxpQkFBaUIsK0JBQStCLCtDQUErQyxLQUFLLCtCQUErQixpQkFBaUIsK0JBQStCLEtBQUssc0NBQXNDLGlCQUFpQixtQ0FBbUMsK0NBQStDLEtBQUssb0RBQW9ELHlCQUF5QixLQUFLLDBCQUEwQixvQkFBb0IseUJBQXlCLG1CQUFtQixjQUFjLGVBQWUsa0JBQWtCLCtDQUErQyxrQ0FBa0MsS0FBSyw0REFBNEQsa0JBQWtCLEtBQUssdURBQXVELGlCQUFpQixrQkFBa0IsS0FBSyxtQ0FBbUMsOEJBQThCLEtBQUssbUNBQW1DLDJDQUEyQyx5QkFBeUIsS0FBSyx5Q0FBeUMsMkNBQTJDLEtBQUssMENBQTBDLHFDQUFxQyxLQUFLLGdEQUFnRCxxQ0FBcUMsS0FBSyx1RUFBdUUsVUFBVSxvQ0FBb0MsT0FBTyxXQUFXLHNDQUFzQyxPQUFPLFlBQVksb0NBQW9DLE9BQU8sS0FBSyxpQ0FBaUMsVUFBVSxxQkFBcUIsT0FBTyxXQUFXLG1CQUFtQixPQUFPLFlBQVkscUJBQXFCLE9BQU8sS0FBSywrQkFBK0Isc0RBQXNELEtBQUssZ0NBQWdDLHlDQUF5QyxLQUFLLHlCQUF5QixZQUFZLGdDQUFnQyxPQUFPLFVBQVUsa0NBQWtDLE9BQU8sS0FBSyxnRUFBZ0Usd0NBQXdDLEdBQUcsMkRBQTJELHdDQUF3QyxHQUFHLGtDQUFrQyx1QkFBdUIsdUJBQXVCLG9NQUFvTSxHQUFHLDBDQUEwQyxtQ0FBbUMsR0FBRywwQ0FBMEMsbUNBQW1DLEdBQUcsd0NBQXdDLDJCQUEyQixpRUFBaUUsR0FBRywrQ0FBK0MsK0NBQStDLEdBQUcsMENBQTBDLHlDQUF5QyxHQUFHLDBDQUEwQyx5Q0FBeUMsR0FBRyxrQ0FBa0MseUNBQXlDLEdBQUcscUNBQXFDLHdDQUF3QyxHQUFHLDRDQUE0QyxvREFBb0QsR0FBRyw0Q0FBNEMsb0RBQW9ELEdBQUcsbUNBQW1DLHVCQUF1QixpRUFBaUUsR0FBRyx3Q0FBd0MsZ0RBQWdELEdBQUcsd0NBQXdDLGdEQUFnRCxHQUFHLDBDQUEwQyxrREFBa0QsR0FBRywyQ0FBMkMsbURBQW1ELEdBQUcsNkNBQTZDLHFEQUFxRCxHQUFHLHFDQUFxQyw4Q0FBOEMsR0FBRyxzQ0FBc0MsNkNBQTZDLEdBQUcsK0NBQStDLHlDQUF5QyxHQUFHLG1DQUFtQyx5QkFBeUIsa0RBQWtELEdBQUcsd0NBQXdDLGtDQUFrQyxHQUFHLDBDQUEwQyxxQ0FBcUMsR0FBRyx3Q0FBd0Msa0NBQWtDLEdBQUcsbUNBQW1DLHlCQUF5Qix3REFBd0QsR0FBRyxrQ0FBa0Msb0NBQW9DLEdBQUcsb0NBQW9DLGVBQWUsR0FBRyxtQ0FBbUMsaUJBQWlCLEdBQUcsa0NBQWtDLG9GQUFvRix3R0FBd0csNEdBQTRHLEdBQUcsdUNBQXVDLHNDQUFzQyxHQUFHLDBDQUEwQyx5Q0FBeUMsR0FBRyxrQ0FBa0MseUNBQXlDLEdBQUcsK0NBQStDLHlDQUF5QyxHQUFHLHFDQUFxQyxtQ0FBbUMsd0JBQXdCLEdBQUcsK0JBQStCLGdIQUFnSCw4R0FBOEcsaUdBQWlHLEdBQUcscUNBQXFDLHlDQUF5QyxHQUFHLGtDQUFrQyxzQ0FBc0MsR0FBRyw2Q0FBNkMsa0RBQWtELEdBQUcsc0NBQXNDLGdDQUFnQyxHQUFHLHFEQUFxRCxtQ0FBbUMsd0JBQXdCLEdBQUcsK0NBQStDLGdIQUFnSCw4R0FBOEcsaUdBQWlHLEdBQUcsa0RBQWtELHNDQUFzQyxHQUFHLHNEQUFzRCxnQ0FBZ0MsR0FBRyxrREFBa0QseUJBQXlCLEdBQUcsaURBQWlELHdCQUF3QixHQUFHLHlDQUF5QyxpQkFBaUIsR0FBRyxpREFBaUQsZUFBZSxHQUFHLDhEQUE4RCx3QkFBd0IsR0FBRyxzREFBc0QsaUJBQWlCLEdBQUcsbUVBQW1FLHlCQUF5QixHQUFHLDJFQUEyRSw2Q0FBNkMsR0FBRywyRUFBMkUsNkNBQTZDLEdBQUcsbUVBQW1FLHlDQUF5QyxHQUFHLDZFQUE2RSxrQ0FBa0MsR0FBRyx3RUFBd0UseUJBQXlCLHdEQUF3RCxHQUFHLCtFQUErRSx3Q0FBd0MsR0FBRywwREFBMEQsaUJBQWlCLEdBQUcsdUVBQXVFLCtDQUErQyw0REFBNEQsNEdBQTRHLEdBQUcsb0VBQW9FLDBCQUEwQiw4QkFBOEIsZ0NBQWdDLDhCQUE4QiwrQkFBK0Isb0NBQW9DLG9DQUFvQyxHQUFHLHlFQUF5RSx5QkFBeUIsOEJBQThCLCtCQUErQiw2QkFBNkIsOEJBQThCLG1DQUFtQyxtQ0FBbUMsR0FBRyx3RUFBd0UseUJBQXlCLEdBQUcsbUVBQW1FLDBCQUEwQixHQUFHLHlFQUF5RSx5QkFBeUIsR0FBRyxvRUFBb0UsMEJBQTBCLEdBQUcscUZBQXFGLGdDQUFnQyxHQUFHLDBGQUEwRixnQ0FBZ0MsR0FBRyxrRkFBa0YsaUNBQWlDLEdBQUcsdUZBQXVGLGlDQUFpQyxHQUFHLGdEQUFnRCwwQ0FBMEMsR0FBRywrQ0FBK0MseUNBQXlDLEdBQUcsd0NBQXdDLHlCQUF5Qix3REFBd0QsR0FBRyw2REFBNkQseUNBQXlDLEdBQUcsNkRBQTZELHlDQUF5QyxHQUFHLHdEQUF3RCw4Q0FBOEMsR0FBRyxzREFBc0QseUJBQXlCLHdEQUF3RCxHQUFHLG1DQUFtQyx3QkFBd0IsbUNBQW1DLEtBQUssK0JBQStCLHVCQUF1QixLQUFLLHlCQUF5Qix1REFBdUQsS0FBSyxzQkFBc0IsMEJBQTBCLEtBQUsseUJBQXlCLGdDQUFnQyxLQUFLLHlEQUF5RCw4QkFBOEIsNkRBQTZELHNFQUFzRSxLQUFLLHdCQUF3QixtQ0FBbUMsS0FBSyx1QkFBdUIsdUJBQXVCLEtBQUssR0FBRyxtQ0FBbUMsa0JBQWtCLHlCQUF5QixLQUFLLDZCQUE2Qix5QkFBeUIsS0FBSyxrQkFBa0Isc0JBQXNCLEtBQUssa0JBQWtCLHlCQUF5QixLQUFLLGtCQUFrQixvQkFBb0IsS0FBSyxvQkFBb0Isb0JBQW9CLEtBQUsseUJBQXlCLHVEQUF1RCxLQUFLLHlCQUF5Qix1REFBdUQsS0FBSyxzQkFBc0IsMEJBQTBCLEtBQUssbUJBQW1CLGdCQUFnQixLQUFLLGlCQUFpQixzQkFBc0IsS0FBSyxtQkFBbUIsd0JBQXdCLDJCQUEyQixLQUFLLG1CQUFtQix3QkFBd0IsMkJBQTJCLEtBQUssbUJBQW1CLHdCQUF3QiwyQkFBMkIsS0FBSyxtQkFBbUIsd0JBQXdCLDJCQUEyQixLQUFLLHNCQUFzQix3QkFBd0Isd0JBQXdCLEtBQUssc0JBQXNCLDBCQUEwQiwyQkFBMkIsS0FBSyxzQkFBc0IseUJBQXlCLDBCQUEwQixLQUFLLHNCQUFzQixzQkFBc0IscUJBQXFCLEtBQUssc0JBQXNCLHlCQUF5QixxQkFBcUIsS0FBSyxxQkFBcUIseUJBQXlCLDJCQUEyQixLQUFLLEdBQUcsb0NBQW9DLHdCQUF3QixtQ0FBbUMsS0FBSyx5QkFBeUIsdURBQXVELEtBQUsseUJBQXlCLHVEQUF1RCxLQUFLLEdBQUcsaUVBQWlFLDJCQUEyQixvTUFBb00sR0FBRyx5Q0FBeUMsdUJBQXVCLEdBQUcsdUNBQXVDLGVBQWUsR0FBRyxzQ0FBc0MsY0FBYyxHQUFHLGlEQUFpRCxtQ0FBbUMsR0FBRyxnREFBZ0Qsa0NBQWtDLEdBQUcsNkNBQTZDLDBCQUEwQixHQUFHLDBDQUEwQyx1QkFBdUIsR0FBRyx1Q0FBdUMsbUZBQW1GLFdBQVcsV0FBVyxXQUFXLFdBQVcsVUFBVSxVQUFVLFVBQVUsVUFBVSxVQUFVLFVBQVUsVUFBVSxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsVUFBVSxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxVQUFVLFdBQVcsV0FBVyxXQUFXLFdBQVcsVUFBVSxXQUFXLFVBQVUsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxLQUFLLE1BQU0sS0FBSyxXQUFXLFdBQVcsV0FBVyxXQUFXLFVBQVUsVUFBVSxVQUFVLFVBQVUsVUFBVSxVQUFVLFVBQVUsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFVBQVUsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsVUFBVSxXQUFXLFdBQVcsV0FBVyxXQUFXLFVBQVUsV0FBVyxVQUFVLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsS0FBSyxXQUFXLFlBQVksTUFBTSxPQUFPLHFCQUFxQixvQkFBb0IscUJBQXFCLHFCQUFxQixNQUFNLE1BQU0sV0FBVyxNQUFNLFlBQVksTUFBTSxNQUFNLHFCQUFxQixxQkFBcUIscUJBQXFCLFVBQVUsb0JBQW9CLHFCQUFxQixxQkFBcUIscUJBQXFCLHFCQUFxQixNQUFNLE9BQU8sTUFBTSxLQUFLLG9CQUFvQixxQkFBcUIsTUFBTSxRQUFRLE1BQU0sS0FBSyxvQkFBb0Isb0JBQW9CLHFCQUFxQixNQUFNLE1BQU0sTUFBTSxLQUFLLFdBQVcsV0FBVyxNQUFNLE1BQU0sTUFBTSxVQUFVLFdBQVcsV0FBVyxNQUFNLE1BQU0sTUFBTSxLQUFLLFVBQVUsV0FBVyxNQUFNLE1BQU0sTUFBTSxNQUFNLFdBQVcsTUFBTSxTQUFTLE1BQU0sUUFBUSxxQkFBcUIscUJBQXFCLHFCQUFxQixvQkFBb0IsTUFBTSxNQUFNLE1BQU0sS0FBSyxVQUFVLE1BQU0sTUFBTSxNQUFNLE1BQU0sVUFBVSxVQUFVLFdBQVcsV0FBVyxNQUFNLEtBQUssVUFBVSxNQUFNLEtBQUssVUFBVSxNQUFNLFFBQVEsTUFBTSxLQUFLLG9CQUFvQixxQkFBcUIscUJBQXFCLE1BQU0sUUFBUSxNQUFNLFNBQVMscUJBQXFCLHFCQUFxQixxQkFBcUIsb0JBQW9CLHFCQUFxQixxQkFBcUIscUJBQXFCLG9CQUFvQixvQkFBb0Isb0JBQW9CLE1BQU0sTUFBTSxNQUFNLE1BQU0sV0FBVyxNQUFNLE9BQU8sTUFBTSxRQUFRLHFCQUFxQixxQkFBcUIscUJBQXFCLE1BQU0sTUFBTSxNQUFNLEtBQUssVUFBVSxNQUFNLE1BQU0sTUFBTSxLQUFLLFdBQVcsTUFBTSxNQUFNLE1BQU0sS0FBSyxXQUFXLE1BQU0sTUFBTSxNQUFNLE1BQU0sVUFBVSxNQUFNLE9BQU8sTUFBTSxLQUFLLHFCQUFxQixxQkFBcUIsTUFBTSxNQUFNLE1BQU0sS0FBSyxXQUFXLE1BQU0sT0FBTyxNQUFNLEtBQUsscUJBQXFCLG9CQUFvQixNQUFNLE1BQU0sTUFBTSxLQUFLLFdBQVcsTUFBTSxNQUFNLE1BQU0saUJBQWlCLFVBQVUsTUFBTSxLQUFLLFVBQVUsVUFBVSxNQUFNLEtBQUssVUFBVSxNQUFNLE9BQU8sV0FBVyxVQUFVLFVBQVUsTUFBTSxNQUFNLEtBQUssS0FBSyxVQUFVLE1BQU0sTUFBTSxNQUFNLEtBQUssV0FBVyxNQUFNLE9BQU8sTUFBTSxLQUFLLG9CQUFvQixvQkFBb0IsTUFBTSxNQUFNLG9CQUFvQixvQkFBb0IsTUFBTSxNQUFNLE1BQU0sTUFBTSxVQUFVLE1BQU0sTUFBTSxLQUFLLEtBQUssVUFBVSxNQUFNLFFBQVEsTUFBTSxZQUFZLG9CQUFvQixxQkFBcUIsTUFBTSxNQUFNLE1BQU0sTUFBTSxVQUFVLFVBQVUsTUFBTSxXQUFXLEtBQUssVUFBVSxLQUFLLEtBQUssV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxZQUFZLFdBQVcsV0FBVyxXQUFXLFlBQVksV0FBVyxZQUFZLFdBQVcsV0FBVyxXQUFXLFlBQVksV0FBVyxXQUFXLE1BQU0sS0FBSyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxXQUFXLFdBQVcsV0FBVyxZQUFZLFdBQVcsV0FBVyxXQUFXLFlBQVksV0FBVyxZQUFZLFdBQVcsV0FBVyxXQUFXLFdBQVcsS0FBSyxLQUFLLEtBQUssS0FBSyxLQUFLLFdBQVcsV0FBVyxXQUFXLEtBQUssS0FBSyxLQUFLLFdBQVcsYUFBYSxhQUFhLGFBQWEsTUFBTSxNQUFNLE9BQU8sS0FBSyxLQUFLLE1BQU0sS0FBSyxNQUFNLFlBQVksWUFBWSxZQUFZLFlBQVksWUFBWSxhQUFhLGFBQWEsYUFBYSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sT0FBTyxLQUFLLEtBQUssT0FBTyxNQUFNLEtBQUssTUFBTSxLQUFLLE1BQU0sS0FBSyxNQUFNLE9BQU8sS0FBSyxLQUFLLE1BQU0sS0FBSyxNQUFNLEtBQUssTUFBTSxPQUFPLEtBQUssS0FBSyxNQUFNLEtBQUssTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLEtBQUssTUFBTSxNQUFNLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxZQUFZLGFBQWEsTUFBTSxNQUFNLE1BQU0sWUFBWSxhQUFhLE1BQU0sTUFBTSxNQUFNLFlBQVksYUFBYSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sT0FBTyxLQUFLLFlBQVksTUFBTSxNQUFNLEtBQUssT0FBTyxLQUFLLFlBQVksTUFBTSxNQUFNLEtBQUssTUFBTSxZQUFZLGFBQWEsYUFBYSxhQUFhLGFBQWEsYUFBYSxNQUFNLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE1BQU0sTUFBTSxLQUFLLE9BQU8sY0FBYyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxPQUFPLEtBQUssWUFBWSxhQUFhLE9BQU8sWUFBWSxNQUFNLFlBQVksYUFBYSxPQUFPLEtBQUssWUFBWSxPQUFPLFlBQVksTUFBTSxZQUFZLE9BQU8sS0FBSyxZQUFZLE9BQU8sS0FBSyxZQUFZLE9BQU8sWUFBWSxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxPQUFPLEtBQUssWUFBWSxhQUFhLGFBQWEsYUFBYSxPQUFPLFlBQVksTUFBTSxZQUFZLGFBQWEsT0FBTyxLQUFLLEtBQUssT0FBTyxPQUFPLFlBQVksTUFBTSxVQUFVLFlBQVksYUFBYSxPQUFPLEtBQUssVUFBVSxZQUFZLE9BQU8sWUFBWSxNQUFNLFlBQVksYUFBYSxPQUFPLEtBQUssVUFBVSxZQUFZLFdBQVcsVUFBVSxVQUFVLFVBQVUsU0FBUyxLQUFLLFlBQVksT0FBTyxLQUFLLFVBQVUsTUFBTSxZQUFZLE1BQU0sWUFBWSxhQUFhLGFBQWEsT0FBTyxLQUFLLFlBQVksT0FBTyxZQUFZLE1BQU0sWUFBWSxXQUFXLE1BQU0sS0FBSyxVQUFVLFlBQVksV0FBVyxVQUFVLFVBQVUsVUFBVSxhQUFhLEtBQUssWUFBWSxXQUFXLFlBQVksYUFBYSxXQUFXLFlBQVksT0FBTyxLQUFLLFVBQVUsTUFBTSxZQUFZLE1BQU0sWUFBWSxPQUFPLEtBQUssWUFBWSxhQUFhLE9BQU8sS0FBSyxZQUFZLE9BQU8sS0FBSyxZQUFZLE9BQU8sWUFBWSxNQUFNLFlBQVksT0FBTyxZQUFZLE1BQU0sVUFBVSxZQUFZLE9BQU8sS0FBSyxVQUFVLFlBQVksYUFBYSxPQUFPLEtBQUssVUFBVSxZQUFZLE9BQU8sS0FBSyxVQUFVLFlBQVksYUFBYSxPQUFPLFlBQVksTUFBTSxZQUFZLE9BQU8sS0FBSyxVQUFVLFlBQVksV0FBVyxVQUFVLFVBQVUsVUFBVSxZQUFZLGFBQWEsT0FBTyxNQUFNLFVBQVUsTUFBTSxZQUFZLE1BQU0sVUFBVSxVQUFVLE1BQU0sS0FBSyxZQUFZLE9BQU8sS0FBSyxZQUFZLGFBQWEsT0FBTyxLQUFLLFlBQVksT0FBTyxLQUFLLFlBQVksT0FBTyxLQUFLLFlBQVksT0FBTyxZQUFZLE1BQU0sS0FBSyxZQUFZLE1BQU0sS0FBSyxZQUFZLE1BQU0sS0FBSyxZQUFZLE1BQU0sTUFBTSxLQUFLLEtBQUssVUFBVSxLQUFLLEtBQUssVUFBVSxLQUFLLEtBQUssVUFBVSxLQUFLLE1BQU0sS0FBSyxZQUFZLE9BQU8sS0FBSyxZQUFZLE9BQU8sS0FBSyxLQUFLLFlBQVksTUFBTSxLQUFLLFlBQVksTUFBTSxNQUFNLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sWUFBWSxhQUFhLE1BQU0sT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sWUFBWSxNQUFNLE9BQU8sTUFBTSxLQUFLLE9BQU8sTUFBTSxLQUFLLE9BQU8sTUFBTSxLQUFLLE9BQU8sTUFBTSxLQUFLLE9BQU8sTUFBTSxLQUFLLE9BQU8sTUFBTSxLQUFLLE9BQU8sTUFBTSxLQUFLLE9BQU8sTUFBTSxLQUFLLE9BQU8sTUFBTSxZQUFZLE1BQU0sT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sWUFBWSxhQUFhLE1BQU0sT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLE1BQU0sT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sWUFBWSxhQUFhLE1BQU0sT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sWUFBWSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLFlBQVksYUFBYSxhQUFhLGFBQWEsYUFBYSxhQUFhLE1BQU0sT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sWUFBWSxNQUFNLE9BQU8sT0FBTyxLQUFLLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLFlBQVksYUFBYSxNQUFNLE9BQU8sTUFBTSxLQUFLLE9BQU8sTUFBTSxLQUFLLE1BQU0sTUFBTSxPQUFPLEtBQUssS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sWUFBWSxNQUFNLE9BQU8sTUFBTSxZQUFZLE1BQU0sT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sWUFBWSxNQUFNLE9BQU8sTUFBTSxZQUFZLE1BQU0sT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sWUFBWSxNQUFNLE9BQU8sTUFBTSxXQUFXLE1BQU0sT0FBTyxNQUFNLFlBQVksTUFBTSxPQUFPLE1BQU0sWUFBWSxNQUFNLE1BQU0sTUFBTSxPQUFPLEtBQUssS0FBSyxPQUFPLE1BQU0sS0FBSyxPQUFPLE1BQU0sS0FBSyxNQUFNLE1BQU0sTUFBTSxZQUFZLE1BQU0sT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUssT0FBTyxNQUFNLEtBQUsseUNBQXlDLHlCQUF5Qix3QkFBd0IscUJBQXFCLGFBQWEsZ0NBQWdDLG1DQUFtQywwQkFBMEIsd0NBQXdDLDZCQUE2QiwyQ0FBMkMsZ0NBQWdDLHVDQUF1QyxvQ0FBb0MsNkNBQTZDLGdDQUFnQywyQ0FBMkMsaUNBQWlDLDBDQUEwQyxxQ0FBcUMsMkNBQTJDLCtCQUErQiw4QkFBOEIsNkJBQTZCLHlCQUF5QixpRUFBaUUscUNBQXFDLG1DQUFtQyxrRUFBa0UsMEZBQTBGLG9EQUFvRCxtREFBbUQsd0VBQXdFLE9BQU8saUJBQWlCLG1DQUFtQywrQkFBK0IsNkJBQTZCLG9DQUFvQyxnQ0FBZ0MsdUNBQXVDLDRCQUE0QiwyQ0FBMkMsb0NBQW9DLHlDQUF5QyxnQ0FBZ0MseUNBQXlDLGlDQUFpQyxzQ0FBc0MscUNBQXFDLDJDQUEyQyxpQ0FBaUMsZ0NBQWdDLCtCQUErQixpRUFBaUUscUNBQXFDLG1DQUFtQyxrRUFBa0UsMEZBQTBGLG9EQUFvRCxtREFBbUQsT0FBTyxLQUFLLHFCQUFxQixTQUFTLDZCQUE2QixPQUFPLFlBQVksNkNBQTZDLHNEQUFzRCw0RkFBNEYsT0FBTyxLQUFLLG9EQUFvRCw0Q0FBNEMsaUNBQWlDLHlDQUF5QyxrREFBa0QsaUdBQWlHLEtBQUssOEJBQThCLHNDQUFzQyw0Q0FBNEMsS0FBSyx5REFBeUQsc0RBQXNELHFEQUFxRCxLQUFLLG9DQUFvQyxnREFBZ0QsS0FBSyxvREFBb0QsZ0NBQWdDLEtBQUssNkJBQTZCLGtEQUFrRCxLQUFLLG9DQUFvQyw0Q0FBNEMsS0FBSywrQ0FBK0MsNkVBQTZFLG9DQUFvQywyQ0FBMkMsNEJBQTRCLHFEQUFxRCxLQUFLLCtCQUErQix1RUFBdUUsb0NBQW9DLDJDQUEyQyw0QkFBNEIsS0FBSyxzREFBc0QsaUNBQWlDLDJEQUEyRCxLQUFLLDZCQUE2Qix5S0FBeUssS0FBSyxtREFBbUQsaUJBQWlCLGtDQUFrQyx5REFBeUQsS0FBSyx3QkFBd0IsaUJBQWlCLCtCQUErQixLQUFLLGlEQUFpRCx5QkFBeUIsdUJBQXVCLEtBQUssMkJBQTJCLG9CQUFvQix5QkFBeUIsYUFBYSxrQkFBa0Isa0JBQWtCLG1CQUFtQix5S0FBeUssaUNBQWlDLEtBQUssaUNBQWlDLGlCQUFpQixLQUFLLDJDQUEyQyx1RkFBdUYsaUNBQWlDLDJEQUEyRCxLQUFLLDZCQUE2QixpRkFBaUYsS0FBSyxrREFBa0QseUJBQXlCLGlCQUFpQixLQUFLLDhCQUE4QixvQkFBb0IseUJBQXlCLGdCQUFnQixpQkFBaUIsa0JBQWtCLG1CQUFtQixtUEFBbVAsNkJBQTZCLGtCQUFrQixpQ0FBaUMsOENBQThDLGlCQUFpQixvQ0FBb0MsS0FBSyxvQ0FBb0MsaUJBQWlCLEtBQUssMkNBQTJDLHVDQUF1QyxLQUFLLDRCQUE0Qix3REFBd0QsNERBQTRELEtBQUssbUNBQW1DLDBEQUEwRCxLQUFLLGlDQUFpQyx1REFBdUQsS0FBSyxxREFBcUQsOENBQThDLEtBQUsseURBQXlELGlCQUFpQixrQ0FBa0MsS0FBSyx1Q0FBdUMsaUJBQWlCLCtCQUErQiwrQ0FBK0MsS0FBSywrQkFBK0IsaUJBQWlCLCtCQUErQixLQUFLLHNDQUFzQyxpQkFBaUIsbUNBQW1DLCtDQUErQyxLQUFLLG9EQUFvRCx5QkFBeUIsS0FBSywwQkFBMEIsb0JBQW9CLHlCQUF5QixtQkFBbUIsY0FBYyxlQUFlLGtCQUFrQiwrQ0FBK0Msa0NBQWtDLEtBQUssNERBQTRELGtCQUFrQixLQUFLLHVEQUF1RCxpQkFBaUIsa0JBQWtCLEtBQUssbUNBQW1DLDhCQUE4QixLQUFLLG1DQUFtQywyQ0FBMkMseUJBQXlCLEtBQUsseUNBQXlDLDJDQUEyQyxLQUFLLDBDQUEwQyxxQ0FBcUMsS0FBSyxnREFBZ0QscUNBQXFDLEtBQUssdUVBQXVFLFVBQVUsb0NBQW9DLE9BQU8sV0FBVyxzQ0FBc0MsT0FBTyxZQUFZLG9DQUFvQyxPQUFPLEtBQUssaUNBQWlDLFVBQVUscUJBQXFCLE9BQU8sV0FBVyxtQkFBbUIsT0FBTyxZQUFZLHFCQUFxQixPQUFPLEtBQUssK0JBQStCLHNEQUFzRCxLQUFLLGdDQUFnQyx5Q0FBeUMsS0FBSyx5QkFBeUIsWUFBWSxnQ0FBZ0MsT0FBTyxVQUFVLGtDQUFrQyxPQUFPLEtBQUssbURBQW1EO0FBQ3YxMEY7QUFDQSwrREFBZSx1QkFBdUIsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zdHlsZXMvZ2xvYmFscy5jc3M/OGM1MyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBJbXBvcnRzXG5pbXBvcnQgX19fQ1NTX0xPQURFUl9BUElfSU1QT1JUX19fIGZyb20gXCIuLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9jc3MtbG9hZGVyL3NyYy9ydW50aW1lL2FwaS5qc1wiO1xudmFyIF9fX0NTU19MT0FERVJfRVhQT1JUX19fID0gX19fQ1NTX0xPQURFUl9BUElfSU1QT1JUX19fKHRydWUpO1xuLy8gTW9kdWxlXG5fX19DU1NfTE9BREVSX0VYUE9SVF9fXy5wdXNoKFttb2R1bGUuaWQsIFwiKiwgOjpiZWZvcmUsIDo6YWZ0ZXIge1xcbiAgLS10dy1ib3JkZXItc3BhY2luZy14OiAwO1xcbiAgLS10dy1ib3JkZXItc3BhY2luZy15OiAwO1xcbiAgLS10dy10cmFuc2xhdGUteDogMDtcXG4gIC0tdHctdHJhbnNsYXRlLXk6IDA7XFxuICAtLXR3LXJvdGF0ZTogMDtcXG4gIC0tdHctc2tldy14OiAwO1xcbiAgLS10dy1za2V3LXk6IDA7XFxuICAtLXR3LXNjYWxlLXg6IDE7XFxuICAtLXR3LXNjYWxlLXk6IDE7XFxuICAtLXR3LXBhbi14OiAgO1xcbiAgLS10dy1wYW4teTogIDtcXG4gIC0tdHctcGluY2gtem9vbTogIDtcXG4gIC0tdHctc2Nyb2xsLXNuYXAtc3RyaWN0bmVzczogcHJveGltaXR5O1xcbiAgLS10dy1ncmFkaWVudC1mcm9tLXBvc2l0aW9uOiAgO1xcbiAgLS10dy1ncmFkaWVudC12aWEtcG9zaXRpb246ICA7XFxuICAtLXR3LWdyYWRpZW50LXRvLXBvc2l0aW9uOiAgO1xcbiAgLS10dy1vcmRpbmFsOiAgO1xcbiAgLS10dy1zbGFzaGVkLXplcm86ICA7XFxuICAtLXR3LW51bWVyaWMtZmlndXJlOiAgO1xcbiAgLS10dy1udW1lcmljLXNwYWNpbmc6ICA7XFxuICAtLXR3LW51bWVyaWMtZnJhY3Rpb246ICA7XFxuICAtLXR3LXJpbmctaW5zZXQ6ICA7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXdpZHRoOiAwcHg7XFxuICAtLXR3LXJpbmctb2Zmc2V0LWNvbG9yOiAjZmZmO1xcbiAgLS10dy1yaW5nLWNvbG9yOiByZ2IoNTkgMTMwIDI0NiAvIDAuNSk7XFxuICAtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdzogMCAwICMwMDAwO1xcbiAgLS10dy1yaW5nLXNoYWRvdzogMCAwICMwMDAwO1xcbiAgLS10dy1zaGFkb3c6IDAgMCAjMDAwMDtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMCAjMDAwMDtcXG4gIC0tdHctYmx1cjogIDtcXG4gIC0tdHctYnJpZ2h0bmVzczogIDtcXG4gIC0tdHctY29udHJhc3Q6ICA7XFxuICAtLXR3LWdyYXlzY2FsZTogIDtcXG4gIC0tdHctaHVlLXJvdGF0ZTogIDtcXG4gIC0tdHctaW52ZXJ0OiAgO1xcbiAgLS10dy1zYXR1cmF0ZTogIDtcXG4gIC0tdHctc2VwaWE6ICA7XFxuICAtLXR3LWRyb3Atc2hhZG93OiAgO1xcbiAgLS10dy1iYWNrZHJvcC1ibHVyOiAgO1xcbiAgLS10dy1iYWNrZHJvcC1icmlnaHRuZXNzOiAgO1xcbiAgLS10dy1iYWNrZHJvcC1jb250cmFzdDogIDtcXG4gIC0tdHctYmFja2Ryb3AtZ3JheXNjYWxlOiAgO1xcbiAgLS10dy1iYWNrZHJvcC1odWUtcm90YXRlOiAgO1xcbiAgLS10dy1iYWNrZHJvcC1pbnZlcnQ6ICA7XFxuICAtLXR3LWJhY2tkcm9wLW9wYWNpdHk6ICA7XFxuICAtLXR3LWJhY2tkcm9wLXNhdHVyYXRlOiAgO1xcbiAgLS10dy1iYWNrZHJvcC1zZXBpYTogIDtcXG4gIC0tdHctY29udGFpbi1zaXplOiAgO1xcbiAgLS10dy1jb250YWluLWxheW91dDogIDtcXG4gIC0tdHctY29udGFpbi1wYWludDogIDtcXG4gIC0tdHctY29udGFpbi1zdHlsZTogIDtcXG59XFxuXFxuOjpiYWNrZHJvcCB7XFxuICAtLXR3LWJvcmRlci1zcGFjaW5nLXg6IDA7XFxuICAtLXR3LWJvcmRlci1zcGFjaW5nLXk6IDA7XFxuICAtLXR3LXRyYW5zbGF0ZS14OiAwO1xcbiAgLS10dy10cmFuc2xhdGUteTogMDtcXG4gIC0tdHctcm90YXRlOiAwO1xcbiAgLS10dy1za2V3LXg6IDA7XFxuICAtLXR3LXNrZXcteTogMDtcXG4gIC0tdHctc2NhbGUteDogMTtcXG4gIC0tdHctc2NhbGUteTogMTtcXG4gIC0tdHctcGFuLXg6ICA7XFxuICAtLXR3LXBhbi15OiAgO1xcbiAgLS10dy1waW5jaC16b29tOiAgO1xcbiAgLS10dy1zY3JvbGwtc25hcC1zdHJpY3RuZXNzOiBwcm94aW1pdHk7XFxuICAtLXR3LWdyYWRpZW50LWZyb20tcG9zaXRpb246ICA7XFxuICAtLXR3LWdyYWRpZW50LXZpYS1wb3NpdGlvbjogIDtcXG4gIC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb246ICA7XFxuICAtLXR3LW9yZGluYWw6ICA7XFxuICAtLXR3LXNsYXNoZWQtemVybzogIDtcXG4gIC0tdHctbnVtZXJpYy1maWd1cmU6ICA7XFxuICAtLXR3LW51bWVyaWMtc3BhY2luZzogIDtcXG4gIC0tdHctbnVtZXJpYy1mcmFjdGlvbjogIDtcXG4gIC0tdHctcmluZy1pbnNldDogIDtcXG4gIC0tdHctcmluZy1vZmZzZXQtd2lkdGg6IDBweDtcXG4gIC0tdHctcmluZy1vZmZzZXQtY29sb3I6ICNmZmY7XFxuICAtLXR3LXJpbmctY29sb3I6IHJnYig1OSAxMzAgMjQ2IC8gMC41KTtcXG4gIC0tdHctcmluZy1vZmZzZXQtc2hhZG93OiAwIDAgIzAwMDA7XFxuICAtLXR3LXJpbmctc2hhZG93OiAwIDAgIzAwMDA7XFxuICAtLXR3LXNoYWRvdzogMCAwICMwMDAwO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCAwICMwMDAwO1xcbiAgLS10dy1ibHVyOiAgO1xcbiAgLS10dy1icmlnaHRuZXNzOiAgO1xcbiAgLS10dy1jb250cmFzdDogIDtcXG4gIC0tdHctZ3JheXNjYWxlOiAgO1xcbiAgLS10dy1odWUtcm90YXRlOiAgO1xcbiAgLS10dy1pbnZlcnQ6ICA7XFxuICAtLXR3LXNhdHVyYXRlOiAgO1xcbiAgLS10dy1zZXBpYTogIDtcXG4gIC0tdHctZHJvcC1zaGFkb3c6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWJsdXI6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWJyaWdodG5lc3M6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWNvbnRyYXN0OiAgO1xcbiAgLS10dy1iYWNrZHJvcC1ncmF5c2NhbGU6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWh1ZS1yb3RhdGU6ICA7XFxuICAtLXR3LWJhY2tkcm9wLWludmVydDogIDtcXG4gIC0tdHctYmFja2Ryb3Atb3BhY2l0eTogIDtcXG4gIC0tdHctYmFja2Ryb3Atc2F0dXJhdGU6ICA7XFxuICAtLXR3LWJhY2tkcm9wLXNlcGlhOiAgO1xcbiAgLS10dy1jb250YWluLXNpemU6ICA7XFxuICAtLXR3LWNvbnRhaW4tbGF5b3V0OiAgO1xcbiAgLS10dy1jb250YWluLXBhaW50OiAgO1xcbiAgLS10dy1jb250YWluLXN0eWxlOiAgO1xcbn0vKlxcbiEgdGFpbHdpbmRjc3MgdjMuNC4xNyB8IE1JVCBMaWNlbnNlIHwgaHR0cHM6Ly90YWlsd2luZGNzcy5jb21cXG4qLy8qXFxuMS4gUHJldmVudCBwYWRkaW5nIGFuZCBib3JkZXIgZnJvbSBhZmZlY3RpbmcgZWxlbWVudCB3aWR0aC4gKGh0dHBzOi8vZ2l0aHViLmNvbS9tb3pkZXZzL2Nzc3JlbWVkeS9pc3N1ZXMvNClcXG4yLiBBbGxvdyBhZGRpbmcgYSBib3JkZXIgdG8gYW4gZWxlbWVudCBieSBqdXN0IGFkZGluZyBhIGJvcmRlci13aWR0aC4gKGh0dHBzOi8vZ2l0aHViLmNvbS90YWlsd2luZGNzcy90YWlsd2luZGNzcy9wdWxsLzExNilcXG4qL1xcblxcbiosXFxuOjpiZWZvcmUsXFxuOjphZnRlciB7XFxuICBib3gtc2l6aW5nOiBib3JkZXItYm94OyAvKiAxICovXFxuICBib3JkZXItd2lkdGg6IDA7IC8qIDIgKi9cXG4gIGJvcmRlci1zdHlsZTogc29saWQ7IC8qIDIgKi9cXG4gIGJvcmRlci1jb2xvcjogI2U1ZTdlYjsgLyogMiAqL1xcbn1cXG5cXG46OmJlZm9yZSxcXG46OmFmdGVyIHtcXG4gIC0tdHctY29udGVudDogJyc7XFxufVxcblxcbi8qXFxuMS4gVXNlIGEgY29uc2lzdGVudCBzZW5zaWJsZSBsaW5lLWhlaWdodCBpbiBhbGwgYnJvd3NlcnMuXFxuMi4gUHJldmVudCBhZGp1c3RtZW50cyBvZiBmb250IHNpemUgYWZ0ZXIgb3JpZW50YXRpb24gY2hhbmdlcyBpbiBpT1MuXFxuMy4gVXNlIGEgbW9yZSByZWFkYWJsZSB0YWIgc2l6ZS5cXG40LiBVc2UgdGhlIHVzZXIncyBjb25maWd1cmVkIGBzYW5zYCBmb250LWZhbWlseSBieSBkZWZhdWx0LlxcbjUuIFVzZSB0aGUgdXNlcidzIGNvbmZpZ3VyZWQgYHNhbnNgIGZvbnQtZmVhdHVyZS1zZXR0aW5ncyBieSBkZWZhdWx0LlxcbjYuIFVzZSB0aGUgdXNlcidzIGNvbmZpZ3VyZWQgYHNhbnNgIGZvbnQtdmFyaWF0aW9uLXNldHRpbmdzIGJ5IGRlZmF1bHQuXFxuNy4gRGlzYWJsZSB0YXAgaGlnaGxpZ2h0cyBvbiBpT1NcXG4qL1xcblxcbmh0bWwsXFxuOmhvc3Qge1xcbiAgbGluZS1oZWlnaHQ6IDEuNTsgLyogMSAqL1xcbiAgLXdlYmtpdC10ZXh0LXNpemUtYWRqdXN0OiAxMDAlOyAvKiAyICovXFxuICAtbW96LXRhYi1zaXplOiA0OyAvKiAzICovXFxuICAtby10YWItc2l6ZTogNDtcXG4gICAgIHRhYi1zaXplOiA0OyAvKiAzICovXFxuICBmb250LWZhbWlseTogdWktc2Fucy1zZXJpZiwgc3lzdGVtLXVpLCBzYW5zLXNlcmlmLCBcXFwiQXBwbGUgQ29sb3IgRW1vamlcXFwiLCBcXFwiU2Vnb2UgVUkgRW1vamlcXFwiLCBcXFwiU2Vnb2UgVUkgU3ltYm9sXFxcIiwgXFxcIk5vdG8gQ29sb3IgRW1vamlcXFwiOyAvKiA0ICovXFxuICBmb250LWZlYXR1cmUtc2V0dGluZ3M6IG5vcm1hbDsgLyogNSAqL1xcbiAgZm9udC12YXJpYXRpb24tc2V0dGluZ3M6IG5vcm1hbDsgLyogNiAqL1xcbiAgLXdlYmtpdC10YXAtaGlnaGxpZ2h0LWNvbG9yOiB0cmFuc3BhcmVudDsgLyogNyAqL1xcbn1cXG5cXG4vKlxcbjEuIFJlbW92ZSB0aGUgbWFyZ2luIGluIGFsbCBicm93c2Vycy5cXG4yLiBJbmhlcml0IGxpbmUtaGVpZ2h0IGZyb20gYGh0bWxgIHNvIHVzZXJzIGNhbiBzZXQgdGhlbSBhcyBhIGNsYXNzIGRpcmVjdGx5IG9uIHRoZSBgaHRtbGAgZWxlbWVudC5cXG4qL1xcblxcbmJvZHkge1xcbiAgbWFyZ2luOiAwOyAvKiAxICovXFxuICBsaW5lLWhlaWdodDogaW5oZXJpdDsgLyogMiAqL1xcbn1cXG5cXG4vKlxcbjEuIEFkZCB0aGUgY29ycmVjdCBoZWlnaHQgaW4gRmlyZWZveC5cXG4yLiBDb3JyZWN0IHRoZSBpbmhlcml0YW5jZSBvZiBib3JkZXIgY29sb3IgaW4gRmlyZWZveC4gKGh0dHBzOi8vYnVnemlsbGEubW96aWxsYS5vcmcvc2hvd19idWcuY2dpP2lkPTE5MDY1NSlcXG4zLiBFbnN1cmUgaG9yaXpvbnRhbCBydWxlcyBhcmUgdmlzaWJsZSBieSBkZWZhdWx0LlxcbiovXFxuXFxuaHIge1xcbiAgaGVpZ2h0OiAwOyAvKiAxICovXFxuICBjb2xvcjogaW5oZXJpdDsgLyogMiAqL1xcbiAgYm9yZGVyLXRvcC13aWR0aDogMXB4OyAvKiAzICovXFxufVxcblxcbi8qXFxuQWRkIHRoZSBjb3JyZWN0IHRleHQgZGVjb3JhdGlvbiBpbiBDaHJvbWUsIEVkZ2UsIGFuZCBTYWZhcmkuXFxuKi9cXG5cXG5hYmJyOndoZXJlKFt0aXRsZV0pIHtcXG4gIC13ZWJraXQtdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmUgZG90dGVkO1xcbiAgICAgICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZSBkb3R0ZWQ7XFxufVxcblxcbi8qXFxuUmVtb3ZlIHRoZSBkZWZhdWx0IGZvbnQgc2l6ZSBhbmQgd2VpZ2h0IGZvciBoZWFkaW5ncy5cXG4qL1xcblxcbmgxLFxcbmgyLFxcbmgzLFxcbmg0LFxcbmg1LFxcbmg2IHtcXG4gIGZvbnQtc2l6ZTogaW5oZXJpdDtcXG4gIGZvbnQtd2VpZ2h0OiBpbmhlcml0O1xcbn1cXG5cXG4vKlxcblJlc2V0IGxpbmtzIHRvIG9wdGltaXplIGZvciBvcHQtaW4gc3R5bGluZyBpbnN0ZWFkIG9mIG9wdC1vdXQuXFxuKi9cXG5cXG5hIHtcXG4gIGNvbG9yOiBpbmhlcml0O1xcbiAgdGV4dC1kZWNvcmF0aW9uOiBpbmhlcml0O1xcbn1cXG5cXG4vKlxcbkFkZCB0aGUgY29ycmVjdCBmb250IHdlaWdodCBpbiBFZGdlIGFuZCBTYWZhcmkuXFxuKi9cXG5cXG5iLFxcbnN0cm9uZyB7XFxuICBmb250LXdlaWdodDogYm9sZGVyO1xcbn1cXG5cXG4vKlxcbjEuIFVzZSB0aGUgdXNlcidzIGNvbmZpZ3VyZWQgYG1vbm9gIGZvbnQtZmFtaWx5IGJ5IGRlZmF1bHQuXFxuMi4gVXNlIHRoZSB1c2VyJ3MgY29uZmlndXJlZCBgbW9ub2AgZm9udC1mZWF0dXJlLXNldHRpbmdzIGJ5IGRlZmF1bHQuXFxuMy4gVXNlIHRoZSB1c2VyJ3MgY29uZmlndXJlZCBgbW9ub2AgZm9udC12YXJpYXRpb24tc2V0dGluZ3MgYnkgZGVmYXVsdC5cXG40LiBDb3JyZWN0IHRoZSBvZGQgYGVtYCBmb250IHNpemluZyBpbiBhbGwgYnJvd3NlcnMuXFxuKi9cXG5cXG5jb2RlLFxcbmtiZCxcXG5zYW1wLFxcbnByZSB7XFxuICBmb250LWZhbWlseTogdWktbW9ub3NwYWNlLCBTRk1vbm8tUmVndWxhciwgTWVubG8sIE1vbmFjbywgQ29uc29sYXMsIFxcXCJMaWJlcmF0aW9uIE1vbm9cXFwiLCBcXFwiQ291cmllciBOZXdcXFwiLCBtb25vc3BhY2U7IC8qIDEgKi9cXG4gIGZvbnQtZmVhdHVyZS1zZXR0aW5nczogbm9ybWFsOyAvKiAyICovXFxuICBmb250LXZhcmlhdGlvbi1zZXR0aW5nczogbm9ybWFsOyAvKiAzICovXFxuICBmb250LXNpemU6IDFlbTsgLyogNCAqL1xcbn1cXG5cXG4vKlxcbkFkZCB0aGUgY29ycmVjdCBmb250IHNpemUgaW4gYWxsIGJyb3dzZXJzLlxcbiovXFxuXFxuc21hbGwge1xcbiAgZm9udC1zaXplOiA4MCU7XFxufVxcblxcbi8qXFxuUHJldmVudCBgc3ViYCBhbmQgYHN1cGAgZWxlbWVudHMgZnJvbSBhZmZlY3RpbmcgdGhlIGxpbmUgaGVpZ2h0IGluIGFsbCBicm93c2Vycy5cXG4qL1xcblxcbnN1YixcXG5zdXAge1xcbiAgZm9udC1zaXplOiA3NSU7XFxuICBsaW5lLWhlaWdodDogMDtcXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcXG4gIHZlcnRpY2FsLWFsaWduOiBiYXNlbGluZTtcXG59XFxuXFxuc3ViIHtcXG4gIGJvdHRvbTogLTAuMjVlbTtcXG59XFxuXFxuc3VwIHtcXG4gIHRvcDogLTAuNWVtO1xcbn1cXG5cXG4vKlxcbjEuIFJlbW92ZSB0ZXh0IGluZGVudGF0aW9uIGZyb20gdGFibGUgY29udGVudHMgaW4gQ2hyb21lIGFuZCBTYWZhcmkuIChodHRwczovL2J1Z3MuY2hyb21pdW0ub3JnL3AvY2hyb21pdW0vaXNzdWVzL2RldGFpbD9pZD05OTkwODgsIGh0dHBzOi8vYnVncy53ZWJraXQub3JnL3Nob3dfYnVnLmNnaT9pZD0yMDEyOTcpXFxuMi4gQ29ycmVjdCB0YWJsZSBib3JkZXIgY29sb3IgaW5oZXJpdGFuY2UgaW4gYWxsIENocm9tZSBhbmQgU2FmYXJpLiAoaHR0cHM6Ly9idWdzLmNocm9taXVtLm9yZy9wL2Nocm9taXVtL2lzc3Vlcy9kZXRhaWw/aWQ9OTM1NzI5LCBodHRwczovL2J1Z3Mud2Via2l0Lm9yZy9zaG93X2J1Zy5jZ2k/aWQ9MTk1MDE2KVxcbjMuIFJlbW92ZSBnYXBzIGJldHdlZW4gdGFibGUgYm9yZGVycyBieSBkZWZhdWx0LlxcbiovXFxuXFxudGFibGUge1xcbiAgdGV4dC1pbmRlbnQ6IDA7IC8qIDEgKi9cXG4gIGJvcmRlci1jb2xvcjogaW5oZXJpdDsgLyogMiAqL1xcbiAgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsgLyogMyAqL1xcbn1cXG5cXG4vKlxcbjEuIENoYW5nZSB0aGUgZm9udCBzdHlsZXMgaW4gYWxsIGJyb3dzZXJzLlxcbjIuIFJlbW92ZSB0aGUgbWFyZ2luIGluIEZpcmVmb3ggYW5kIFNhZmFyaS5cXG4zLiBSZW1vdmUgZGVmYXVsdCBwYWRkaW5nIGluIGFsbCBicm93c2Vycy5cXG4qL1xcblxcbmJ1dHRvbixcXG5pbnB1dCxcXG5vcHRncm91cCxcXG5zZWxlY3QsXFxudGV4dGFyZWEge1xcbiAgZm9udC1mYW1pbHk6IGluaGVyaXQ7IC8qIDEgKi9cXG4gIGZvbnQtZmVhdHVyZS1zZXR0aW5nczogaW5oZXJpdDsgLyogMSAqL1xcbiAgZm9udC12YXJpYXRpb24tc2V0dGluZ3M6IGluaGVyaXQ7IC8qIDEgKi9cXG4gIGZvbnQtc2l6ZTogMTAwJTsgLyogMSAqL1xcbiAgZm9udC13ZWlnaHQ6IGluaGVyaXQ7IC8qIDEgKi9cXG4gIGxpbmUtaGVpZ2h0OiBpbmhlcml0OyAvKiAxICovXFxuICBsZXR0ZXItc3BhY2luZzogaW5oZXJpdDsgLyogMSAqL1xcbiAgY29sb3I6IGluaGVyaXQ7IC8qIDEgKi9cXG4gIG1hcmdpbjogMDsgLyogMiAqL1xcbiAgcGFkZGluZzogMDsgLyogMyAqL1xcbn1cXG5cXG4vKlxcblJlbW92ZSB0aGUgaW5oZXJpdGFuY2Ugb2YgdGV4dCB0cmFuc2Zvcm0gaW4gRWRnZSBhbmQgRmlyZWZveC5cXG4qL1xcblxcbmJ1dHRvbixcXG5zZWxlY3Qge1xcbiAgdGV4dC10cmFuc2Zvcm06IG5vbmU7XFxufVxcblxcbi8qXFxuMS4gQ29ycmVjdCB0aGUgaW5hYmlsaXR5IHRvIHN0eWxlIGNsaWNrYWJsZSB0eXBlcyBpbiBpT1MgYW5kIFNhZmFyaS5cXG4yLiBSZW1vdmUgZGVmYXVsdCBidXR0b24gc3R5bGVzLlxcbiovXFxuXFxuYnV0dG9uLFxcbmlucHV0OndoZXJlKFt0eXBlPSdidXR0b24nXSksXFxuaW5wdXQ6d2hlcmUoW3R5cGU9J3Jlc2V0J10pLFxcbmlucHV0OndoZXJlKFt0eXBlPSdzdWJtaXQnXSkge1xcbiAgLXdlYmtpdC1hcHBlYXJhbmNlOiBidXR0b247IC8qIDEgKi9cXG4gIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50OyAvKiAyICovXFxuICBiYWNrZ3JvdW5kLWltYWdlOiBub25lOyAvKiAyICovXFxufVxcblxcbi8qXFxuVXNlIHRoZSBtb2Rlcm4gRmlyZWZveCBmb2N1cyBzdHlsZSBmb3IgYWxsIGZvY3VzYWJsZSBlbGVtZW50cy5cXG4qL1xcblxcbjotbW96LWZvY3VzcmluZyB7XFxuICBvdXRsaW5lOiBhdXRvO1xcbn1cXG5cXG4vKlxcblJlbW92ZSB0aGUgYWRkaXRpb25hbCBgOmludmFsaWRgIHN0eWxlcyBpbiBGaXJlZm94LiAoaHR0cHM6Ly9naXRodWIuY29tL21vemlsbGEvZ2Vja28tZGV2L2Jsb2IvMmY5ZWFjZDlkM2Q5OTVjOTM3YjQyNTFhNTU1N2Q5NWQ0OTRjOWJlMS9sYXlvdXQvc3R5bGUvcmVzL2Zvcm1zLmNzcyNMNzI4LUw3MzcpXFxuKi9cXG5cXG46LW1vei11aS1pbnZhbGlkIHtcXG4gIGJveC1zaGFkb3c6IG5vbmU7XFxufVxcblxcbi8qXFxuQWRkIHRoZSBjb3JyZWN0IHZlcnRpY2FsIGFsaWdubWVudCBpbiBDaHJvbWUgYW5kIEZpcmVmb3guXFxuKi9cXG5cXG5wcm9ncmVzcyB7XFxuICB2ZXJ0aWNhbC1hbGlnbjogYmFzZWxpbmU7XFxufVxcblxcbi8qXFxuQ29ycmVjdCB0aGUgY3Vyc29yIHN0eWxlIG9mIGluY3JlbWVudCBhbmQgZGVjcmVtZW50IGJ1dHRvbnMgaW4gU2FmYXJpLlxcbiovXFxuXFxuOjotd2Via2l0LWlubmVyLXNwaW4tYnV0dG9uLFxcbjo6LXdlYmtpdC1vdXRlci1zcGluLWJ1dHRvbiB7XFxuICBoZWlnaHQ6IGF1dG87XFxufVxcblxcbi8qXFxuMS4gQ29ycmVjdCB0aGUgb2RkIGFwcGVhcmFuY2UgaW4gQ2hyb21lIGFuZCBTYWZhcmkuXFxuMi4gQ29ycmVjdCB0aGUgb3V0bGluZSBzdHlsZSBpbiBTYWZhcmkuXFxuKi9cXG5cXG5bdHlwZT0nc2VhcmNoJ10ge1xcbiAgLXdlYmtpdC1hcHBlYXJhbmNlOiB0ZXh0ZmllbGQ7IC8qIDEgKi9cXG4gIG91dGxpbmUtb2Zmc2V0OiAtMnB4OyAvKiAyICovXFxufVxcblxcbi8qXFxuUmVtb3ZlIHRoZSBpbm5lciBwYWRkaW5nIGluIENocm9tZSBhbmQgU2FmYXJpIG9uIG1hY09TLlxcbiovXFxuXFxuOjotd2Via2l0LXNlYXJjaC1kZWNvcmF0aW9uIHtcXG4gIC13ZWJraXQtYXBwZWFyYW5jZTogbm9uZTtcXG59XFxuXFxuLypcXG4xLiBDb3JyZWN0IHRoZSBpbmFiaWxpdHkgdG8gc3R5bGUgY2xpY2thYmxlIHR5cGVzIGluIGlPUyBhbmQgU2FmYXJpLlxcbjIuIENoYW5nZSBmb250IHByb3BlcnRpZXMgdG8gYGluaGVyaXRgIGluIFNhZmFyaS5cXG4qL1xcblxcbjo6LXdlYmtpdC1maWxlLXVwbG9hZC1idXR0b24ge1xcbiAgLXdlYmtpdC1hcHBlYXJhbmNlOiBidXR0b247IC8qIDEgKi9cXG4gIGZvbnQ6IGluaGVyaXQ7IC8qIDIgKi9cXG59XFxuXFxuLypcXG5BZGQgdGhlIGNvcnJlY3QgZGlzcGxheSBpbiBDaHJvbWUgYW5kIFNhZmFyaS5cXG4qL1xcblxcbnN1bW1hcnkge1xcbiAgZGlzcGxheTogbGlzdC1pdGVtO1xcbn1cXG5cXG4vKlxcblJlbW92ZXMgdGhlIGRlZmF1bHQgc3BhY2luZyBhbmQgYm9yZGVyIGZvciBhcHByb3ByaWF0ZSBlbGVtZW50cy5cXG4qL1xcblxcbmJsb2NrcXVvdGUsXFxuZGwsXFxuZGQsXFxuaDEsXFxuaDIsXFxuaDMsXFxuaDQsXFxuaDUsXFxuaDYsXFxuaHIsXFxuZmlndXJlLFxcbnAsXFxucHJlIHtcXG4gIG1hcmdpbjogMDtcXG59XFxuXFxuZmllbGRzZXQge1xcbiAgbWFyZ2luOiAwO1xcbiAgcGFkZGluZzogMDtcXG59XFxuXFxubGVnZW5kIHtcXG4gIHBhZGRpbmc6IDA7XFxufVxcblxcbm9sLFxcbnVsLFxcbm1lbnUge1xcbiAgbGlzdC1zdHlsZTogbm9uZTtcXG4gIG1hcmdpbjogMDtcXG4gIHBhZGRpbmc6IDA7XFxufVxcblxcbi8qXFxuUmVzZXQgZGVmYXVsdCBzdHlsaW5nIGZvciBkaWFsb2dzLlxcbiovXFxuZGlhbG9nIHtcXG4gIHBhZGRpbmc6IDA7XFxufVxcblxcbi8qXFxuUHJldmVudCByZXNpemluZyB0ZXh0YXJlYXMgaG9yaXpvbnRhbGx5IGJ5IGRlZmF1bHQuXFxuKi9cXG5cXG50ZXh0YXJlYSB7XFxuICByZXNpemU6IHZlcnRpY2FsO1xcbn1cXG5cXG4vKlxcbjEuIFJlc2V0IHRoZSBkZWZhdWx0IHBsYWNlaG9sZGVyIG9wYWNpdHkgaW4gRmlyZWZveC4gKGh0dHBzOi8vZ2l0aHViLmNvbS90YWlsd2luZGxhYnMvdGFpbHdpbmRjc3MvaXNzdWVzLzMzMDApXFxuMi4gU2V0IHRoZSBkZWZhdWx0IHBsYWNlaG9sZGVyIGNvbG9yIHRvIHRoZSB1c2VyJ3MgY29uZmlndXJlZCBncmF5IDQwMCBjb2xvci5cXG4qL1xcblxcbmlucHV0OjotbW96LXBsYWNlaG9sZGVyLCB0ZXh0YXJlYTo6LW1vei1wbGFjZWhvbGRlciB7XFxuICBvcGFjaXR5OiAxOyAvKiAxICovXFxuICBjb2xvcjogIzljYTNhZjsgLyogMiAqL1xcbn1cXG5cXG5pbnB1dDo6cGxhY2Vob2xkZXIsXFxudGV4dGFyZWE6OnBsYWNlaG9sZGVyIHtcXG4gIG9wYWNpdHk6IDE7IC8qIDEgKi9cXG4gIGNvbG9yOiAjOWNhM2FmOyAvKiAyICovXFxufVxcblxcbi8qXFxuU2V0IHRoZSBkZWZhdWx0IGN1cnNvciBmb3IgYnV0dG9ucy5cXG4qL1xcblxcbmJ1dHRvbixcXG5bcm9sZT1cXFwiYnV0dG9uXFxcIl0ge1xcbiAgY3Vyc29yOiBwb2ludGVyO1xcbn1cXG5cXG4vKlxcbk1ha2Ugc3VyZSBkaXNhYmxlZCBidXR0b25zIGRvbid0IGdldCB0aGUgcG9pbnRlciBjdXJzb3IuXFxuKi9cXG46ZGlzYWJsZWQge1xcbiAgY3Vyc29yOiBkZWZhdWx0O1xcbn1cXG5cXG4vKlxcbjEuIE1ha2UgcmVwbGFjZWQgZWxlbWVudHMgYGRpc3BsYXk6IGJsb2NrYCBieSBkZWZhdWx0LiAoaHR0cHM6Ly9naXRodWIuY29tL21vemRldnMvY3NzcmVtZWR5L2lzc3Vlcy8xNClcXG4yLiBBZGQgYHZlcnRpY2FsLWFsaWduOiBtaWRkbGVgIHRvIGFsaWduIHJlcGxhY2VkIGVsZW1lbnRzIG1vcmUgc2Vuc2libHkgYnkgZGVmYXVsdC4gKGh0dHBzOi8vZ2l0aHViLmNvbS9qZW5zaW1tb25zL2Nzc3JlbWVkeS9pc3N1ZXMvMTQjaXNzdWVjb21tZW50LTYzNDkzNDIxMClcXG4gICBUaGlzIGNhbiB0cmlnZ2VyIGEgcG9vcmx5IGNvbnNpZGVyZWQgbGludCBlcnJvciBpbiBzb21lIHRvb2xzIGJ1dCBpcyBpbmNsdWRlZCBieSBkZXNpZ24uXFxuKi9cXG5cXG5pbWcsXFxuc3ZnLFxcbnZpZGVvLFxcbmNhbnZhcyxcXG5hdWRpbyxcXG5pZnJhbWUsXFxuZW1iZWQsXFxub2JqZWN0IHtcXG4gIGRpc3BsYXk6IGJsb2NrOyAvKiAxICovXFxuICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlOyAvKiAyICovXFxufVxcblxcbi8qXFxuQ29uc3RyYWluIGltYWdlcyBhbmQgdmlkZW9zIHRvIHRoZSBwYXJlbnQgd2lkdGggYW5kIHByZXNlcnZlIHRoZWlyIGludHJpbnNpYyBhc3BlY3QgcmF0aW8uIChodHRwczovL2dpdGh1Yi5jb20vbW96ZGV2cy9jc3NyZW1lZHkvaXNzdWVzLzE0KVxcbiovXFxuXFxuaW1nLFxcbnZpZGVvIHtcXG4gIG1heC13aWR0aDogMTAwJTtcXG4gIGhlaWdodDogYXV0bztcXG59XFxuXFxuLyogTWFrZSBlbGVtZW50cyB3aXRoIHRoZSBIVE1MIGhpZGRlbiBhdHRyaWJ1dGUgc3RheSBoaWRkZW4gYnkgZGVmYXVsdCAqL1xcbltoaWRkZW5dOndoZXJlKDpub3QoW2hpZGRlbj1cXFwidW50aWwtZm91bmRcXFwiXSkpIHtcXG4gIGRpc3BsYXk6IG5vbmU7XFxufVxcclxcbiAgOnJvb3Qge1xcclxcbiAgICAtLWJhY2tncm91bmQ6IDAgMCUgMTAwJTtcXHJcXG4gICAgLS1mb3JlZ3JvdW5kOiAyNDAgMTAlIDMuOSU7XFxyXFxuICAgIC0tY2FyZDogMCAwJSAxMDAlO1xcclxcbiAgICAtLWNhcmQtZm9yZWdyb3VuZDogMjQwIDEwJSAzLjklO1xcclxcbiAgICAtLXBvcG92ZXI6IDAgMCUgMTAwJTtcXHJcXG4gICAgLS1wb3BvdmVyLWZvcmVncm91bmQ6IDI0MCAxMCUgMy45JTtcXHJcXG4gICAgLS1wcmltYXJ5OiAyNDAgNS45JSAxMCU7XFxyXFxuICAgIC0tcHJpbWFyeS1mb3JlZ3JvdW5kOiAwIDAlIDk4JTtcXHJcXG4gICAgLS1zZWNvbmRhcnk6IDI0MCA0LjglIDk1LjklO1xcclxcbiAgICAtLXNlY29uZGFyeS1mb3JlZ3JvdW5kOiAyNDAgNS45JSAxMCU7XFxyXFxuICAgIC0tbXV0ZWQ6IDI0MCA0LjglIDk1LjklO1xcclxcbiAgICAtLW11dGVkLWZvcmVncm91bmQ6IDI0MCAzLjglIDQ2LjElO1xcclxcbiAgICAtLWFjY2VudDogMjQwIDQuOCUgOTUuOSU7XFxyXFxuICAgIC0tYWNjZW50LWZvcmVncm91bmQ6IDI0MCA1LjklIDEwJTtcXHJcXG4gICAgLS1kZXN0cnVjdGl2ZTogMCA4NC4yJSA2MC4yJTtcXHJcXG4gICAgLS1kZXN0cnVjdGl2ZS1mb3JlZ3JvdW5kOiAwIDAlIDk4JTtcXHJcXG4gICAgLS1ib3JkZXI6IDI0MCA1LjklIDkwJTtcXHJcXG4gICAgLS1pbnB1dDogMjQwIDUuOSUgOTAlO1xcclxcbiAgICAtLXJpbmc6IDI0MCA1LjklIDEwJTtcXHJcXG4gICAgLS1yYWRpdXM6IDAuNXJlbTtcXHJcXG5cXHJcXG4gICAgLyogVGhlbWUgY29sb3JzICovXFxyXFxuICAgIC0tdGhlbWUtYmx1ZTogMjEwIDEwMCUgNTAlO1xcclxcbiAgICAtLXRoZW1lLXB1cnBsZTogMjYwIDEwMCUgNjAlO1xcclxcbiAgICAtLXRoZW1lLXRlYWw6IDE4MCAxMDAlIDQwJTtcXHJcXG5cXHJcXG4gICAgLyogR2xvdyBjb2xvcnMgKi9cXHJcXG4gICAgLS1nbG93LWNvbG9yOiAyMTAsIDEwMCUsIDUwJTtcXHJcXG5cXHJcXG4gICAgLyogRmxvdyBib3JkZXIgY29sb3JzICovXFxyXFxuICAgIC0tYm9yZGVyLXN0YXJ0LWNvbG9yOiByZ2JhKDMzLCAxNTAsIDI0MywgMC41KTtcXHJcXG4gICAgLS1ib3JkZXItbWlkLWNvbG9yOiByZ2JhKDE1NiwgMzksIDE3NiwgMC41KTtcXHJcXG4gICAgLS1ib3JkZXItZW5kLWNvbG9yOiByZ2JhKDAsIDE4OCwgMjEyLCAwLjUpO1xcclxcblxcclxcbiAgICAvKiBUaGVtZSB0cmFuc2l0aW9uICovXFxyXFxuICAgIC0tdGhlbWUtdHJhbnNpdGlvbjogMC4zcyBlYXNlO1xcclxcbiAgfVxcclxcblxcclxcbiAgLmRhcmsge1xcclxcbiAgICAtLWJhY2tncm91bmQ6IDI0MCAxMCUgMy45JTtcXHJcXG4gICAgLS1mb3JlZ3JvdW5kOiAwIDAlIDk4JTtcXHJcXG4gICAgLS1jYXJkOiAyNDAgMTAlIDMuOSU7XFxyXFxuICAgIC0tY2FyZC1mb3JlZ3JvdW5kOiAwIDAlIDk4JTtcXHJcXG4gICAgLS1wb3BvdmVyOiAyNDAgMTAlIDMuOSU7XFxyXFxuICAgIC0tcG9wb3Zlci1mb3JlZ3JvdW5kOiAwIDAlIDk4JTtcXHJcXG4gICAgLS1wcmltYXJ5OiAwIDAlIDk4JTtcXHJcXG4gICAgLS1wcmltYXJ5LWZvcmVncm91bmQ6IDI0MCA1LjklIDEwJTtcXHJcXG4gICAgLS1zZWNvbmRhcnk6IDI0MCAzLjclIDE1LjklO1xcclxcbiAgICAtLXNlY29uZGFyeS1mb3JlZ3JvdW5kOiAwIDAlIDk4JTtcXHJcXG4gICAgLS1tdXRlZDogMjQwIDMuNyUgMTUuOSU7XFxyXFxuICAgIC0tbXV0ZWQtZm9yZWdyb3VuZDogMjQwIDUlIDY0LjklO1xcclxcbiAgICAtLWFjY2VudDogMjQwIDMuNyUgMTUuOSU7XFxyXFxuICAgIC0tYWNjZW50LWZvcmVncm91bmQ6IDAgMCUgOTglO1xcclxcbiAgICAtLWRlc3RydWN0aXZlOiAwIDYyLjglIDMwLjYlO1xcclxcbiAgICAtLWRlc3RydWN0aXZlLWZvcmVncm91bmQ6IDAgMCUgOTglO1xcclxcbiAgICAtLWJvcmRlcjogMjQwIDMuNyUgMTUuOSU7XFxyXFxuICAgIC0taW5wdXQ6IDI0MCAzLjclIDE1LjklO1xcclxcbiAgICAtLXJpbmc6IDI0MCA0LjklIDgzLjklO1xcclxcblxcclxcbiAgICAvKiBUaGVtZSBjb2xvcnMgKi9cXHJcXG4gICAgLS10aGVtZS1ibHVlOiAyMTAgMTAwJSA2MCU7XFxyXFxuICAgIC0tdGhlbWUtcHVycGxlOiAyNjAgMTAwJSA3MCU7XFxyXFxuICAgIC0tdGhlbWUtdGVhbDogMTgwIDEwMCUgNTAlO1xcclxcblxcclxcbiAgICAvKiBHbG93IGNvbG9ycyAqL1xcclxcbiAgICAtLWdsb3ctY29sb3I6IDIxMCwgMTAwJSwgNjAlO1xcclxcblxcclxcbiAgICAvKiBGbG93IGJvcmRlciBjb2xvcnMgKi9cXHJcXG4gICAgLS1ib3JkZXItc3RhcnQtY29sb3I6IHJnYmEoMzMsIDE1MCwgMjQzLCAwLjcpO1xcclxcbiAgICAtLWJvcmRlci1taWQtY29sb3I6IHJnYmEoMTU2LCAzOSwgMTc2LCAwLjcpO1xcclxcbiAgICAtLWJvcmRlci1lbmQtY29sb3I6IHJnYmEoMCwgMTg4LCAyMTIsIDAuNyk7XFxyXFxuICB9XFxyXFxuICAqIHtcXG4gIGJvcmRlci1jb2xvcjogaHNsKHZhcigtLWJvcmRlcikpO1xcbn1cXHJcXG4gIGJvZHkge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogaHNsKHZhcigtLWJhY2tncm91bmQpKTtcXG4gIGNvbG9yOiBoc2wodmFyKC0tZm9yZWdyb3VuZCkpO1xcclxcbiAgICBmb250LWZlYXR1cmUtc2V0dGluZ3M6IFxcXCJybGlnXFxcIiAxLCBcXFwiY2FsdFxcXCIgMTtcXHJcXG4gICAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciB2YXIoLS10aGVtZS10cmFuc2l0aW9uKSwgY29sb3IgdmFyKC0tdGhlbWUtdHJhbnNpdGlvbik7XFxufVxcclxcbi5jb250YWluZXIge1xcbiAgd2lkdGg6IDEwMCU7XFxuICBtYXJnaW4tcmlnaHQ6IGF1dG87XFxuICBtYXJnaW4tbGVmdDogYXV0bztcXG4gIHBhZGRpbmctcmlnaHQ6IDJyZW07XFxuICBwYWRkaW5nLWxlZnQ6IDJyZW07XFxufVxcclxcbkBtZWRpYSAobWluLXdpZHRoOiAxNDAwcHgpIHtcXG5cXG4gIC5jb250YWluZXIge1xcbiAgICBtYXgtd2lkdGg6IDE0MDBweDtcXG4gIH1cXG59XFxyXFxuLnNyLW9ubHkge1xcbiAgcG9zaXRpb246IGFic29sdXRlO1xcbiAgd2lkdGg6IDFweDtcXG4gIGhlaWdodDogMXB4O1xcbiAgcGFkZGluZzogMDtcXG4gIG1hcmdpbjogLTFweDtcXG4gIG92ZXJmbG93OiBoaWRkZW47XFxuICBjbGlwOiByZWN0KDAsIDAsIDAsIDApO1xcbiAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcXG4gIGJvcmRlci13aWR0aDogMDtcXG59XFxyXFxuLnBvaW50ZXItZXZlbnRzLW5vbmUge1xcbiAgcG9pbnRlci1ldmVudHM6IG5vbmU7XFxufVxcclxcbi52aXNpYmxlIHtcXG4gIHZpc2liaWxpdHk6IHZpc2libGU7XFxufVxcclxcbi5maXhlZCB7XFxuICBwb3NpdGlvbjogZml4ZWQ7XFxufVxcclxcbi5hYnNvbHV0ZSB7XFxuICBwb3NpdGlvbjogYWJzb2x1dGU7XFxufVxcclxcbi5yZWxhdGl2ZSB7XFxuICBwb3NpdGlvbjogcmVsYXRpdmU7XFxufVxcclxcbi5pbnNldC0wIHtcXG4gIGluc2V0OiAwcHg7XFxufVxcclxcbi4tdG9wLTEge1xcbiAgdG9wOiAtMC4yNXJlbTtcXG59XFxyXFxuLmJvdHRvbS0yIHtcXG4gIGJvdHRvbTogMC41cmVtO1xcbn1cXHJcXG4uYm90dG9tLTIwIHtcXG4gIGJvdHRvbTogNXJlbTtcXG59XFxyXFxuLmJvdHRvbS00MCB7XFxuICBib3R0b206IDEwcmVtO1xcbn1cXHJcXG4uYm90dG9tLWZ1bGwge1xcbiAgYm90dG9tOiAxMDAlO1xcbn1cXHJcXG4ubGVmdC0wIHtcXG4gIGxlZnQ6IDBweDtcXG59XFxyXFxuLmxlZnQtMVxcXFwvMiB7XFxuICBsZWZ0OiA1MCU7XFxufVxcclxcbi5sZWZ0LTFcXFxcLzQge1xcbiAgbGVmdDogMjUlO1xcbn1cXHJcXG4ubGVmdC0xMCB7XFxuICBsZWZ0OiAyLjVyZW07XFxufVxcclxcbi5sZWZ0LTIge1xcbiAgbGVmdDogMC41cmVtO1xcbn1cXHJcXG4ubGVmdC1cXFxcWzUwXFxcXCVcXFxcXSB7XFxuICBsZWZ0OiA1MCU7XFxufVxcclxcbi5yaWdodC0wIHtcXG4gIHJpZ2h0OiAwcHg7XFxufVxcclxcbi5yaWdodC0xMCB7XFxuICByaWdodDogMi41cmVtO1xcbn1cXHJcXG4ucmlnaHQtMiB7XFxuICByaWdodDogMC41cmVtO1xcbn1cXHJcXG4ucmlnaHQtMjAge1xcbiAgcmlnaHQ6IDVyZW07XFxufVxcclxcbi5yaWdodC00IHtcXG4gIHJpZ2h0OiAxcmVtO1xcbn1cXHJcXG4udG9wLTAge1xcbiAgdG9wOiAwcHg7XFxufVxcclxcbi50b3AtMjAge1xcbiAgdG9wOiA1cmVtO1xcbn1cXHJcXG4udG9wLTQge1xcbiAgdG9wOiAxcmVtO1xcbn1cXHJcXG4udG9wLTQwIHtcXG4gIHRvcDogMTByZW07XFxufVxcclxcbi50b3AtOCB7XFxuICB0b3A6IDJyZW07XFxufVxcclxcbi50b3AtXFxcXFs1MFxcXFwlXFxcXF0ge1xcbiAgdG9wOiA1MCU7XFxufVxcclxcbi4tdG9wLTMge1xcbiAgdG9wOiAtMC43NXJlbTtcXG59XFxyXFxuLmlzb2xhdGUge1xcbiAgaXNvbGF0aW9uOiBpc29sYXRlO1xcbn1cXHJcXG4uLXotMTAge1xcbiAgei1pbmRleDogLTEwO1xcbn1cXHJcXG4uei0wIHtcXG4gIHotaW5kZXg6IDA7XFxufVxcclxcbi56LTEwIHtcXG4gIHotaW5kZXg6IDEwO1xcbn1cXHJcXG4uei00MCB7XFxuICB6LWluZGV4OiA0MDtcXG59XFxyXFxuLnotNTAge1xcbiAgei1pbmRleDogNTA7XFxufVxcclxcbi56LTIwIHtcXG4gIHotaW5kZXg6IDIwO1xcbn1cXHJcXG4uY29sLXNwYW4tMSB7XFxuICBncmlkLWNvbHVtbjogc3BhbiAxIC8gc3BhbiAxO1xcbn1cXHJcXG4uLW14LTEge1xcbiAgbWFyZ2luLWxlZnQ6IC0wLjI1cmVtO1xcbiAgbWFyZ2luLXJpZ2h0OiAtMC4yNXJlbTtcXG59XFxyXFxuLm14LTEge1xcbiAgbWFyZ2luLWxlZnQ6IDAuMjVyZW07XFxuICBtYXJnaW4tcmlnaHQ6IDAuMjVyZW07XFxufVxcclxcbi5teC0yIHtcXG4gIG1hcmdpbi1sZWZ0OiAwLjVyZW07XFxuICBtYXJnaW4tcmlnaHQ6IDAuNXJlbTtcXG59XFxyXFxuLm14LWF1dG8ge1xcbiAgbWFyZ2luLWxlZnQ6IGF1dG87XFxuICBtYXJnaW4tcmlnaHQ6IGF1dG87XFxufVxcclxcbi5teS0xIHtcXG4gIG1hcmdpbi10b3A6IDAuMjVyZW07XFxuICBtYXJnaW4tYm90dG9tOiAwLjI1cmVtO1xcbn1cXHJcXG4ubXktNCB7XFxuICBtYXJnaW4tdG9wOiAxcmVtO1xcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcXG59XFxyXFxuLm15LTYge1xcbiAgbWFyZ2luLXRvcDogMS41cmVtO1xcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xcbn1cXHJcXG4uLW1sLTEge1xcbiAgbWFyZ2luLWxlZnQ6IC0wLjI1cmVtO1xcbn1cXHJcXG4ubWItMSB7XFxuICBtYXJnaW4tYm90dG9tOiAwLjI1cmVtO1xcbn1cXHJcXG4ubWItMTIge1xcbiAgbWFyZ2luLWJvdHRvbTogM3JlbTtcXG59XFxyXFxuLm1iLTE2IHtcXG4gIG1hcmdpbi1ib3R0b206IDRyZW07XFxufVxcclxcbi5tYi0yIHtcXG4gIG1hcmdpbi1ib3R0b206IDAuNXJlbTtcXG59XFxyXFxuLm1iLTMge1xcbiAgbWFyZ2luLWJvdHRvbTogMC43NXJlbTtcXG59XFxyXFxuLm1iLTQge1xcbiAgbWFyZ2luLWJvdHRvbTogMXJlbTtcXG59XFxyXFxuLm1iLTYge1xcbiAgbWFyZ2luLWJvdHRvbTogMS41cmVtO1xcbn1cXHJcXG4ubWItOCB7XFxuICBtYXJnaW4tYm90dG9tOiAycmVtO1xcbn1cXHJcXG4ubWwtMSB7XFxuICBtYXJnaW4tbGVmdDogMC4yNXJlbTtcXG59XFxyXFxuLm1sLTIge1xcbiAgbWFyZ2luLWxlZnQ6IDAuNXJlbTtcXG59XFxyXFxuLm1yLTEge1xcbiAgbWFyZ2luLXJpZ2h0OiAwLjI1cmVtO1xcbn1cXHJcXG4ubXItMiB7XFxuICBtYXJnaW4tcmlnaHQ6IDAuNXJlbTtcXG59XFxyXFxuLm10LTAge1xcbiAgbWFyZ2luLXRvcDogMHB4O1xcbn1cXHJcXG4ubXQtMFxcXFwuNSB7XFxuICBtYXJnaW4tdG9wOiAwLjEyNXJlbTtcXG59XFxyXFxuLm10LTEge1xcbiAgbWFyZ2luLXRvcDogMC4yNXJlbTtcXG59XFxyXFxuLm10LTEyIHtcXG4gIG1hcmdpbi10b3A6IDNyZW07XFxufVxcclxcbi5tdC0xNiB7XFxuICBtYXJnaW4tdG9wOiA0cmVtO1xcbn1cXHJcXG4ubXQtMiB7XFxuICBtYXJnaW4tdG9wOiAwLjVyZW07XFxufVxcclxcbi5tdC0yNCB7XFxuICBtYXJnaW4tdG9wOiA2cmVtO1xcbn1cXHJcXG4ubXQtNCB7XFxuICBtYXJnaW4tdG9wOiAxcmVtO1xcbn1cXHJcXG4ubXQtNiB7XFxuICBtYXJnaW4tdG9wOiAxLjVyZW07XFxufVxcclxcbi5tdC04IHtcXG4gIG1hcmdpbi10b3A6IDJyZW07XFxufVxcclxcbi5tdC1cXFxcWy0xNTBweFxcXFxdIHtcXG4gIG1hcmdpbi10b3A6IC0xNTBweDtcXG59XFxyXFxuLm10LWF1dG8ge1xcbiAgbWFyZ2luLXRvcDogYXV0bztcXG59XFxyXFxuLmJsb2NrIHtcXG4gIGRpc3BsYXk6IGJsb2NrO1xcbn1cXHJcXG4uaW5saW5lLWJsb2NrIHtcXG4gIGRpc3BsYXk6IGlubGluZS1ibG9jaztcXG59XFxyXFxuLmZsZXgge1xcbiAgZGlzcGxheTogZmxleDtcXG59XFxyXFxuLmlubGluZS1mbGV4IHtcXG4gIGRpc3BsYXk6IGlubGluZS1mbGV4O1xcbn1cXHJcXG4udGFibGUge1xcbiAgZGlzcGxheTogdGFibGU7XFxufVxcclxcbi5ncmlkIHtcXG4gIGRpc3BsYXk6IGdyaWQ7XFxufVxcclxcbi5oaWRkZW4ge1xcbiAgZGlzcGxheTogbm9uZTtcXG59XFxyXFxuLmFzcGVjdC1zcXVhcmUge1xcbiAgYXNwZWN0LXJhdGlvOiAxIC8gMTtcXG59XFxyXFxuLmFzcGVjdC12aWRlbyB7XFxuICBhc3BlY3QtcmF0aW86IDE2IC8gOTtcXG59XFxyXFxuLmgtMCB7XFxuICBoZWlnaHQ6IDBweDtcXG59XFxyXFxuLmgtMSB7XFxuICBoZWlnaHQ6IDAuMjVyZW07XFxufVxcclxcbi5oLTEwIHtcXG4gIGhlaWdodDogMi41cmVtO1xcbn1cXHJcXG4uaC0xMSB7XFxuICBoZWlnaHQ6IDIuNzVyZW07XFxufVxcclxcbi5oLTEyIHtcXG4gIGhlaWdodDogM3JlbTtcXG59XFxyXFxuLmgtMTYge1xcbiAgaGVpZ2h0OiA0cmVtO1xcbn1cXHJcXG4uaC0yIHtcXG4gIGhlaWdodDogMC41cmVtO1xcbn1cXHJcXG4uaC0yXFxcXC41IHtcXG4gIGhlaWdodDogMC42MjVyZW07XFxufVxcclxcbi5oLTIwIHtcXG4gIGhlaWdodDogNXJlbTtcXG59XFxyXFxuLmgtMjQge1xcbiAgaGVpZ2h0OiA2cmVtO1xcbn1cXHJcXG4uaC0zIHtcXG4gIGhlaWdodDogMC43NXJlbTtcXG59XFxyXFxuLmgtM1xcXFwuNSB7XFxuICBoZWlnaHQ6IDAuODc1cmVtO1xcbn1cXHJcXG4uaC0zMiB7XFxuICBoZWlnaHQ6IDhyZW07XFxufVxcclxcbi5oLTQge1xcbiAgaGVpZ2h0OiAxcmVtO1xcbn1cXHJcXG4uaC00MCB7XFxuICBoZWlnaHQ6IDEwcmVtO1xcbn1cXHJcXG4uaC01IHtcXG4gIGhlaWdodDogMS4yNXJlbTtcXG59XFxyXFxuLmgtNiB7XFxuICBoZWlnaHQ6IDEuNXJlbTtcXG59XFxyXFxuLmgtNyB7XFxuICBoZWlnaHQ6IDEuNzVyZW07XFxufVxcclxcbi5oLTgge1xcbiAgaGVpZ2h0OiAycmVtO1xcbn1cXHJcXG4uaC05IHtcXG4gIGhlaWdodDogMi4yNXJlbTtcXG59XFxyXFxuLmgtXFxcXFsxcHhcXFxcXSB7XFxuICBoZWlnaHQ6IDFweDtcXG59XFxyXFxuLmgtXFxcXFt2YXJcXFxcKC0tcmFkaXgtc2VsZWN0LXRyaWdnZXItaGVpZ2h0XFxcXClcXFxcXSB7XFxuICBoZWlnaHQ6IHZhcigtLXJhZGl4LXNlbGVjdC10cmlnZ2VyLWhlaWdodCk7XFxufVxcclxcbi5oLWF1dG8ge1xcbiAgaGVpZ2h0OiBhdXRvO1xcbn1cXHJcXG4uaC1mdWxsIHtcXG4gIGhlaWdodDogMTAwJTtcXG59XFxyXFxuLmgtcHgge1xcbiAgaGVpZ2h0OiAxcHg7XFxufVxcclxcbi5taW4taC1cXFxcWzEwMHB4XFxcXF0ge1xcbiAgbWluLWhlaWdodDogMTAwcHg7XFxufVxcclxcbi5taW4taC1cXFxcWzE1MHB4XFxcXF0ge1xcbiAgbWluLWhlaWdodDogMTUwcHg7XFxufVxcclxcbi5taW4taC1cXFxcWzgwcHhcXFxcXSB7XFxuICBtaW4taGVpZ2h0OiA4MHB4O1xcbn1cXHJcXG4ubWluLWgtc2NyZWVuIHtcXG4gIG1pbi1oZWlnaHQ6IDEwMHZoO1xcbn1cXHJcXG4udy0wIHtcXG4gIHdpZHRoOiAwcHg7XFxufVxcclxcbi53LTEwIHtcXG4gIHdpZHRoOiAyLjVyZW07XFxufVxcclxcbi53LTEyIHtcXG4gIHdpZHRoOiAzcmVtO1xcbn1cXHJcXG4udy0xNiB7XFxuICB3aWR0aDogNHJlbTtcXG59XFxyXFxuLnctMiB7XFxuICB3aWR0aDogMC41cmVtO1xcbn1cXHJcXG4udy0yXFxcXC41IHtcXG4gIHdpZHRoOiAwLjYyNXJlbTtcXG59XFxyXFxuLnctMjAge1xcbiAgd2lkdGg6IDVyZW07XFxufVxcclxcbi53LTI0IHtcXG4gIHdpZHRoOiA2cmVtO1xcbn1cXHJcXG4udy0zIHtcXG4gIHdpZHRoOiAwLjc1cmVtO1xcbn1cXHJcXG4udy0zXFxcXC41IHtcXG4gIHdpZHRoOiAwLjg3NXJlbTtcXG59XFxyXFxuLnctMzIge1xcbiAgd2lkdGg6IDhyZW07XFxufVxcclxcbi53LTQge1xcbiAgd2lkdGg6IDFyZW07XFxufVxcclxcbi53LTQ4IHtcXG4gIHdpZHRoOiAxMnJlbTtcXG59XFxyXFxuLnctNSB7XFxuICB3aWR0aDogMS4yNXJlbTtcXG59XFxyXFxuLnctNiB7XFxuICB3aWR0aDogMS41cmVtO1xcbn1cXHJcXG4udy04IHtcXG4gIHdpZHRoOiAycmVtO1xcbn1cXHJcXG4udy1cXFxcWzFweFxcXFxdIHtcXG4gIHdpZHRoOiAxcHg7XFxufVxcclxcbi53LWZpdCB7XFxuICB3aWR0aDogLW1vei1maXQtY29udGVudDtcXG4gIHdpZHRoOiBmaXQtY29udGVudDtcXG59XFxyXFxuLnctZnVsbCB7XFxuICB3aWR0aDogMTAwJTtcXG59XFxyXFxuLm1pbi13LVxcXFxbOHJlbVxcXFxdIHtcXG4gIG1pbi13aWR0aDogOHJlbTtcXG59XFxyXFxuLm1pbi13LVxcXFxbdmFyXFxcXCgtLXJhZGl4LXNlbGVjdC10cmlnZ2VyLXdpZHRoXFxcXClcXFxcXSB7XFxuICBtaW4td2lkdGg6IHZhcigtLXJhZGl4LXNlbGVjdC10cmlnZ2VyLXdpZHRoKTtcXG59XFxyXFxuLm1heC13LTJ4bCB7XFxuICBtYXgtd2lkdGg6IDQycmVtO1xcbn1cXHJcXG4ubWF4LXctM3hsIHtcXG4gIG1heC13aWR0aDogNDhyZW07XFxufVxcclxcbi5tYXgtdy00eGwge1xcbiAgbWF4LXdpZHRoOiA1NnJlbTtcXG59XFxyXFxuLm1heC13LTV4bCB7XFxuICBtYXgtd2lkdGg6IDY0cmVtO1xcbn1cXHJcXG4ubWF4LXctbGcge1xcbiAgbWF4LXdpZHRoOiAzMnJlbTtcXG59XFxyXFxuLm1heC13LW1kIHtcXG4gIG1heC13aWR0aDogMjhyZW07XFxufVxcclxcbi5tYXgtdy14cyB7XFxuICBtYXgtd2lkdGg6IDIwcmVtO1xcbn1cXHJcXG4uZmxleC0xIHtcXG4gIGZsZXg6IDEgMSAwJTtcXG59XFxyXFxuLnNocmluay0wIHtcXG4gIGZsZXgtc2hyaW5rOiAwO1xcbn1cXHJcXG4uZmxleC1ncm93IHtcXG4gIGZsZXgtZ3JvdzogMTtcXG59XFxyXFxuLmdyb3cge1xcbiAgZmxleC1ncm93OiAxO1xcbn1cXHJcXG4uYm9yZGVyLWNvbGxhcHNlIHtcXG4gIGJvcmRlci1jb2xsYXBzZTogY29sbGFwc2U7XFxufVxcclxcbi4tdHJhbnNsYXRlLXgtMVxcXFwvMiB7XFxuICAtLXR3LXRyYW5zbGF0ZS14OiAtNTAlO1xcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUodmFyKC0tdHctdHJhbnNsYXRlLXgpLCB2YXIoLS10dy10cmFuc2xhdGUteSkpIHJvdGF0ZSh2YXIoLS10dy1yb3RhdGUpKSBza2V3WCh2YXIoLS10dy1za2V3LXgpKSBza2V3WSh2YXIoLS10dy1za2V3LXkpKSBzY2FsZVgodmFyKC0tdHctc2NhbGUteCkpIHNjYWxlWSh2YXIoLS10dy1zY2FsZS15KSk7XFxufVxcclxcbi50cmFuc2xhdGUteC1cXFxcWy01MFxcXFwlXFxcXF0ge1xcbiAgLS10dy10cmFuc2xhdGUteDogLTUwJTtcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlKHZhcigtLXR3LXRyYW5zbGF0ZS14KSwgdmFyKC0tdHctdHJhbnNsYXRlLXkpKSByb3RhdGUodmFyKC0tdHctcm90YXRlKSkgc2tld1godmFyKC0tdHctc2tldy14KSkgc2tld1kodmFyKC0tdHctc2tldy15KSkgc2NhbGVYKHZhcigtLXR3LXNjYWxlLXgpKSBzY2FsZVkodmFyKC0tdHctc2NhbGUteSkpO1xcbn1cXHJcXG4udHJhbnNsYXRlLXktMSB7XFxuICAtLXR3LXRyYW5zbGF0ZS15OiAwLjI1cmVtO1xcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUodmFyKC0tdHctdHJhbnNsYXRlLXgpLCB2YXIoLS10dy10cmFuc2xhdGUteSkpIHJvdGF0ZSh2YXIoLS10dy1yb3RhdGUpKSBza2V3WCh2YXIoLS10dy1za2V3LXgpKSBza2V3WSh2YXIoLS10dy1za2V3LXkpKSBzY2FsZVgodmFyKC0tdHctc2NhbGUteCkpIHNjYWxlWSh2YXIoLS10dy1zY2FsZS15KSk7XFxufVxcclxcbi50cmFuc2xhdGUteS1cXFxcWy01MFxcXFwlXFxcXF0ge1xcbiAgLS10dy10cmFuc2xhdGUteTogLTUwJTtcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlKHZhcigtLXR3LXRyYW5zbGF0ZS14KSwgdmFyKC0tdHctdHJhbnNsYXRlLXkpKSByb3RhdGUodmFyKC0tdHctcm90YXRlKSkgc2tld1godmFyKC0tdHctc2tldy14KSkgc2tld1kodmFyKC0tdHctc2tldy15KSkgc2NhbGVYKHZhcigtLXR3LXNjYWxlLXgpKSBzY2FsZVkodmFyKC0tdHctc2NhbGUteSkpO1xcbn1cXHJcXG4ucm90YXRlLTAge1xcbiAgLS10dy1yb3RhdGU6IDBkZWc7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSh2YXIoLS10dy10cmFuc2xhdGUteCksIHZhcigtLXR3LXRyYW5zbGF0ZS15KSkgcm90YXRlKHZhcigtLXR3LXJvdGF0ZSkpIHNrZXdYKHZhcigtLXR3LXNrZXcteCkpIHNrZXdZKHZhcigtLXR3LXNrZXcteSkpIHNjYWxlWCh2YXIoLS10dy1zY2FsZS14KSkgc2NhbGVZKHZhcigtLXR3LXNjYWxlLXkpKTtcXG59XFxyXFxuLnNjYWxlLTEwNSB7XFxuICAtLXR3LXNjYWxlLXg6IDEuMDU7XFxuICAtLXR3LXNjYWxlLXk6IDEuMDU7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSh2YXIoLS10dy10cmFuc2xhdGUteCksIHZhcigtLXR3LXRyYW5zbGF0ZS15KSkgcm90YXRlKHZhcigtLXR3LXJvdGF0ZSkpIHNrZXdYKHZhcigtLXR3LXNrZXcteCkpIHNrZXdZKHZhcigtLXR3LXNrZXcteSkpIHNjYWxlWCh2YXIoLS10dy1zY2FsZS14KSkgc2NhbGVZKHZhcigtLXR3LXNjYWxlLXkpKTtcXG59XFxyXFxuLnRyYW5zZm9ybSB7XFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSh2YXIoLS10dy10cmFuc2xhdGUteCksIHZhcigtLXR3LXRyYW5zbGF0ZS15KSkgcm90YXRlKHZhcigtLXR3LXJvdGF0ZSkpIHNrZXdYKHZhcigtLXR3LXNrZXcteCkpIHNrZXdZKHZhcigtLXR3LXNrZXcteSkpIHNjYWxlWCh2YXIoLS10dy1zY2FsZS14KSkgc2NhbGVZKHZhcigtLXR3LXNjYWxlLXkpKTtcXG59XFxyXFxuQGtleWZyYW1lcyBncmFkaWVudC14IHtcXG5cXG4gIDAlLCAxMDAlIHtcXG4gICAgYmFja2dyb3VuZC1wb3NpdGlvbjogMCUgNTAlO1xcbiAgfVxcblxcbiAgNTAlIHtcXG4gICAgYmFja2dyb3VuZC1wb3NpdGlvbjogMTAwJSA1MCU7XFxuICB9XFxufVxcclxcbi5hbmltYXRlLWdyYWRpZW50LXgge1xcbiAgYW5pbWF0aW9uOiBncmFkaWVudC14IDNzIGVhc2UgaW5maW5pdGU7XFxufVxcclxcbkBrZXlmcmFtZXMgcHVsc2Uge1xcblxcbiAgNTAlIHtcXG4gICAgb3BhY2l0eTogLjU7XFxuICB9XFxufVxcclxcbi5hbmltYXRlLXB1bHNlIHtcXG4gIGFuaW1hdGlvbjogcHVsc2UgMnMgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC42LCAxKSBpbmZpbml0ZTtcXG59XFxyXFxuQGtleWZyYW1lcyBzcGluIHtcXG5cXG4gIHRvIHtcXG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTtcXG4gIH1cXG59XFxyXFxuLmFuaW1hdGUtc3BpbiB7XFxuICBhbmltYXRpb246IHNwaW4gMXMgbGluZWFyIGluZmluaXRlO1xcbn1cXHJcXG4uY3Vyc29yLWRlZmF1bHQge1xcbiAgY3Vyc29yOiBkZWZhdWx0O1xcbn1cXHJcXG4uY3Vyc29yLWhlbHAge1xcbiAgY3Vyc29yOiBoZWxwO1xcbn1cXHJcXG4uY3Vyc29yLW5vdC1hbGxvd2VkIHtcXG4gIGN1cnNvcjogbm90LWFsbG93ZWQ7XFxufVxcclxcbi5jdXJzb3ItcG9pbnRlciB7XFxuICBjdXJzb3I6IHBvaW50ZXI7XFxufVxcclxcbi50b3VjaC1ub25lIHtcXG4gIHRvdWNoLWFjdGlvbjogbm9uZTtcXG59XFxyXFxuLnNlbGVjdC1ub25lIHtcXG4gIC13ZWJraXQtdXNlci1zZWxlY3Q6IG5vbmU7XFxuICAgICAtbW96LXVzZXItc2VsZWN0OiBub25lO1xcbiAgICAgICAgICB1c2VyLXNlbGVjdDogbm9uZTtcXG59XFxyXFxuLnJlc2l6ZS1ub25lIHtcXG4gIHJlc2l6ZTogbm9uZTtcXG59XFxyXFxuLnJlc2l6ZSB7XFxuICByZXNpemU6IGJvdGg7XFxufVxcclxcbi5ncmlkLWNvbHMtMSB7XFxuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgxLCBtaW5tYXgoMCwgMWZyKSk7XFxufVxcclxcbi5ncmlkLWNvbHMtMiB7XFxuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCBtaW5tYXgoMCwgMWZyKSk7XFxufVxcclxcbi5mbGV4LWNvbCB7XFxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xcbn1cXHJcXG4uZmxleC1jb2wtcmV2ZXJzZSB7XFxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uLXJldmVyc2U7XFxufVxcclxcbi5mbGV4LXdyYXAge1xcbiAgZmxleC13cmFwOiB3cmFwO1xcbn1cXHJcXG4uaXRlbXMtc3RhcnQge1xcbiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XFxufVxcclxcbi5pdGVtcy1lbmQge1xcbiAgYWxpZ24taXRlbXM6IGZsZXgtZW5kO1xcbn1cXHJcXG4uaXRlbXMtY2VudGVyIHtcXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XFxufVxcclxcbi5qdXN0aWZ5LWNlbnRlciB7XFxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcXG59XFxyXFxuLmp1c3RpZnktYmV0d2VlbiB7XFxuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XFxufVxcclxcbi5nYXAtMSB7XFxuICBnYXA6IDAuMjVyZW07XFxufVxcclxcbi5nYXAtMiB7XFxuICBnYXA6IDAuNXJlbTtcXG59XFxyXFxuLmdhcC0zIHtcXG4gIGdhcDogMC43NXJlbTtcXG59XFxyXFxuLmdhcC00IHtcXG4gIGdhcDogMXJlbTtcXG59XFxyXFxuLmdhcC02IHtcXG4gIGdhcDogMS41cmVtO1xcbn1cXHJcXG4uZ2FwLTgge1xcbiAgZ2FwOiAycmVtO1xcbn1cXHJcXG4uc3BhY2UteC0yID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXgtcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi1yaWdodDogY2FsYygwLjVyZW0gKiB2YXIoLS10dy1zcGFjZS14LXJldmVyc2UpKTtcXG4gIG1hcmdpbi1sZWZ0OiBjYWxjKDAuNXJlbSAqIGNhbGMoMSAtIHZhcigtLXR3LXNwYWNlLXgtcmV2ZXJzZSkpKTtcXG59XFxyXFxuLnNwYWNlLXgtNCA+IDpub3QoW2hpZGRlbl0pIH4gOm5vdChbaGlkZGVuXSkge1xcbiAgLS10dy1zcGFjZS14LXJldmVyc2U6IDA7XFxuICBtYXJnaW4tcmlnaHQ6IGNhbGMoMXJlbSAqIHZhcigtLXR3LXNwYWNlLXgtcmV2ZXJzZSkpO1xcbiAgbWFyZ2luLWxlZnQ6IGNhbGMoMXJlbSAqIGNhbGMoMSAtIHZhcigtLXR3LXNwYWNlLXgtcmV2ZXJzZSkpKTtcXG59XFxyXFxuLnNwYWNlLXgtNiA+IDpub3QoW2hpZGRlbl0pIH4gOm5vdChbaGlkZGVuXSkge1xcbiAgLS10dy1zcGFjZS14LXJldmVyc2U6IDA7XFxuICBtYXJnaW4tcmlnaHQ6IGNhbGMoMS41cmVtICogdmFyKC0tdHctc3BhY2UteC1yZXZlcnNlKSk7XFxuICBtYXJnaW4tbGVmdDogY2FsYygxLjVyZW0gKiBjYWxjKDEgLSB2YXIoLS10dy1zcGFjZS14LXJldmVyc2UpKSk7XFxufVxcclxcbi5zcGFjZS14LTggPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteC1yZXZlcnNlOiAwO1xcbiAgbWFyZ2luLXJpZ2h0OiBjYWxjKDJyZW0gKiB2YXIoLS10dy1zcGFjZS14LXJldmVyc2UpKTtcXG4gIG1hcmdpbi1sZWZ0OiBjYWxjKDJyZW0gKiBjYWxjKDEgLSB2YXIoLS10dy1zcGFjZS14LXJldmVyc2UpKSk7XFxufVxcclxcbi5zcGFjZS15LTEgPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteS1yZXZlcnNlOiAwO1xcbiAgbWFyZ2luLXRvcDogY2FsYygwLjI1cmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteS1yZXZlcnNlKSkpO1xcbiAgbWFyZ2luLWJvdHRvbTogY2FsYygwLjI1cmVtICogdmFyKC0tdHctc3BhY2UteS1yZXZlcnNlKSk7XFxufVxcclxcbi5zcGFjZS15LTFcXFxcLjUgPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteS1yZXZlcnNlOiAwO1xcbiAgbWFyZ2luLXRvcDogY2FsYygwLjM3NXJlbSAqIGNhbGMoMSAtIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpKTtcXG4gIG1hcmdpbi1ib3R0b206IGNhbGMoMC4zNzVyZW0gKiB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKTtcXG59XFxyXFxuLnNwYWNlLXktMiA+IDpub3QoW2hpZGRlbl0pIH4gOm5vdChbaGlkZGVuXSkge1xcbiAgLS10dy1zcGFjZS15LXJldmVyc2U6IDA7XFxuICBtYXJnaW4tdG9wOiBjYWxjKDAuNXJlbSAqIGNhbGMoMSAtIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpKTtcXG4gIG1hcmdpbi1ib3R0b206IGNhbGMoMC41cmVtICogdmFyKC0tdHctc3BhY2UteS1yZXZlcnNlKSk7XFxufVxcclxcbi5zcGFjZS15LTMgPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteS1yZXZlcnNlOiAwO1xcbiAgbWFyZ2luLXRvcDogY2FsYygwLjc1cmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteS1yZXZlcnNlKSkpO1xcbiAgbWFyZ2luLWJvdHRvbTogY2FsYygwLjc1cmVtICogdmFyKC0tdHctc3BhY2UteS1yZXZlcnNlKSk7XFxufVxcclxcbi5zcGFjZS15LTQgPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteS1yZXZlcnNlOiAwO1xcbiAgbWFyZ2luLXRvcDogY2FsYygxcmVtICogY2FsYygxIC0gdmFyKC0tdHctc3BhY2UteS1yZXZlcnNlKSkpO1xcbiAgbWFyZ2luLWJvdHRvbTogY2FsYygxcmVtICogdmFyKC0tdHctc3BhY2UteS1yZXZlcnNlKSk7XFxufVxcclxcbi5zcGFjZS15LTYgPiA6bm90KFtoaWRkZW5dKSB+IDpub3QoW2hpZGRlbl0pIHtcXG4gIC0tdHctc3BhY2UteS1yZXZlcnNlOiAwO1xcbiAgbWFyZ2luLXRvcDogY2FsYygxLjVyZW0gKiBjYWxjKDEgLSB2YXIoLS10dy1zcGFjZS15LXJldmVyc2UpKSk7XFxuICBtYXJnaW4tYm90dG9tOiBjYWxjKDEuNXJlbSAqIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpO1xcbn1cXHJcXG4uc3BhY2UteS04ID4gOm5vdChbaGlkZGVuXSkgfiA6bm90KFtoaWRkZW5dKSB7XFxuICAtLXR3LXNwYWNlLXktcmV2ZXJzZTogMDtcXG4gIG1hcmdpbi10b3A6IGNhbGMoMnJlbSAqIGNhbGMoMSAtIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpKTtcXG4gIG1hcmdpbi1ib3R0b206IGNhbGMoMnJlbSAqIHZhcigtLXR3LXNwYWNlLXktcmV2ZXJzZSkpO1xcbn1cXHJcXG4ub3ZlcmZsb3ctaGlkZGVuIHtcXG4gIG92ZXJmbG93OiBoaWRkZW47XFxufVxcclxcbi5vdmVyZmxvdy14LWF1dG8ge1xcbiAgb3ZlcmZsb3cteDogYXV0bztcXG59XFxyXFxuLnRydW5jYXRlIHtcXG4gIG92ZXJmbG93OiBoaWRkZW47XFxuICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpcztcXG4gIHdoaXRlLXNwYWNlOiBub3dyYXA7XFxufVxcclxcbi53aGl0ZXNwYWNlLW5vd3JhcCB7XFxuICB3aGl0ZS1zcGFjZTogbm93cmFwO1xcbn1cXHJcXG4ucm91bmRlZCB7XFxuICBib3JkZXItcmFkaXVzOiAwLjI1cmVtO1xcbn1cXHJcXG4ucm91bmRlZC1mdWxsIHtcXG4gIGJvcmRlci1yYWRpdXM6IDk5OTlweDtcXG59XFxyXFxuLnJvdW5kZWQtbGcge1xcbiAgYm9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzKTtcXG59XFxyXFxuLnJvdW5kZWQtbWQge1xcbiAgYm9yZGVyLXJhZGl1czogY2FsYyh2YXIoLS1yYWRpdXMpIC0gMnB4KTtcXG59XFxyXFxuLnJvdW5kZWQtc20ge1xcbiAgYm9yZGVyLXJhZGl1czogY2FsYyh2YXIoLS1yYWRpdXMpIC0gNHB4KTtcXG59XFxyXFxuLnJvdW5kZWQteGwge1xcbiAgYm9yZGVyLXJhZGl1czogMC43NXJlbTtcXG59XFxyXFxuLmJvcmRlciB7XFxuICBib3JkZXItd2lkdGg6IDFweDtcXG59XFxyXFxuLmJvcmRlci0yIHtcXG4gIGJvcmRlci13aWR0aDogMnB4O1xcbn1cXHJcXG4uYm9yZGVyLTgge1xcbiAgYm9yZGVyLXdpZHRoOiA4cHg7XFxufVxcclxcbi5ib3JkZXItYiB7XFxuICBib3JkZXItYm90dG9tLXdpZHRoOiAxcHg7XFxufVxcclxcbi5ib3JkZXItYi0yIHtcXG4gIGJvcmRlci1ib3R0b20td2lkdGg6IDJweDtcXG59XFxyXFxuLmJvcmRlci1iLVxcXFxbODZweFxcXFxdIHtcXG4gIGJvcmRlci1ib3R0b20td2lkdGg6IDg2cHg7XFxufVxcclxcbi5ib3JkZXItbC1cXFxcWzUwcHhcXFxcXSB7XFxuICBib3JkZXItbGVmdC13aWR0aDogNTBweDtcXG59XFxyXFxuLmJvcmRlci1yLVxcXFxbNTBweFxcXFxdIHtcXG4gIGJvcmRlci1yaWdodC13aWR0aDogNTBweDtcXG59XFxyXFxuLmJvcmRlci10IHtcXG4gIGJvcmRlci10b3Atd2lkdGg6IDFweDtcXG59XFxyXFxuLmJvcmRlci10LTIge1xcbiAgYm9yZGVyLXRvcC13aWR0aDogMnB4O1xcbn1cXHJcXG4uYm9yZGVyLWRhc2hlZCB7XFxuICBib3JkZXItc3R5bGU6IGRhc2hlZDtcXG59XFxyXFxuLmJvcmRlci1hbWJlci0yMDAge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDI1MyAyMzAgMTM4IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxyXFxuLmJvcmRlci1hbWJlci05MDBcXFxcLzUwIHtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDEyMCA1MyAxNSAvIDAuNSk7XFxufVxcclxcbi5ib3JkZXItYmxhY2tcXFxcLzUwIHtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDAgMCAwIC8gMC41KTtcXG59XFxyXFxuLmJvcmRlci1ib3JkZXIge1xcbiAgYm9yZGVyLWNvbG9yOiBoc2wodmFyKC0tYm9yZGVyKSk7XFxufVxcclxcbi5ib3JkZXItZGVzdHJ1Y3RpdmUge1xcbiAgYm9yZGVyLWNvbG9yOiBoc2wodmFyKC0tZGVzdHJ1Y3RpdmUpKTtcXG59XFxyXFxuLmJvcmRlci1kZXN0cnVjdGl2ZVxcXFwvNTAge1xcbiAgYm9yZGVyLWNvbG9yOiBoc2wodmFyKC0tZGVzdHJ1Y3RpdmUpIC8gMC41KTtcXG59XFxyXFxuLmJvcmRlci1ncmF5LTIwMCB7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjI5IDIzMSAyMzUgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbn1cXHJcXG4uYm9yZGVyLWdyYXktMzAwIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigyMDkgMjEzIDIxOSAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcclxcbi5ib3JkZXItZ3JheS01MDAge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDEwNyAxMTQgMTI4IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxyXFxuLmJvcmRlci1ncmVlbi0yMDAge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDE4NyAyNDcgMjA4IC8gdmFyKC0tdHctYm9yZGVyLW9wYWNpdHksIDEpKTtcXG59XFxyXFxuLmJvcmRlci1ncmVlbi05MDBcXFxcLzUwIHtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDIwIDgzIDQ1IC8gMC41KTtcXG59XFxyXFxuLmJvcmRlci1pbnB1dCB7XFxuICBib3JkZXItY29sb3I6IGhzbCh2YXIoLS1pbnB1dCkpO1xcbn1cXHJcXG4uYm9yZGVyLW11dGVkLWZvcmVncm91bmQge1xcbiAgYm9yZGVyLWNvbG9yOiBoc2wodmFyKC0tbXV0ZWQtZm9yZWdyb3VuZCkpO1xcbn1cXHJcXG4uYm9yZGVyLW11dGVkLWZvcmVncm91bmRcXFxcLzI1IHtcXG4gIGJvcmRlci1jb2xvcjogaHNsKHZhcigtLW11dGVkLWZvcmVncm91bmQpIC8gMC4yNSk7XFxufVxcclxcbi5ib3JkZXItcHJpbWFyeSB7XFxuICBib3JkZXItY29sb3I6IGhzbCh2YXIoLS1wcmltYXJ5KSk7XFxufVxcclxcbi5ib3JkZXItcmVkLTIwMCB7XFxuICAtLXR3LWJvcmRlci1vcGFjaXR5OiAxO1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjU0IDIwMiAyMDIgLyB2YXIoLS10dy1ib3JkZXItb3BhY2l0eSwgMSkpO1xcbn1cXHJcXG4uYm9yZGVyLXJlZC01MDAge1xcbiAgLS10dy1ib3JkZXItb3BhY2l0eTogMTtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDIzOSA2OCA2OCAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcclxcbi5ib3JkZXItcmVkLTkwMFxcXFwvNTAge1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMTI3IDI5IDI5IC8gMC41KTtcXG59XFxyXFxuLmJvcmRlci10aGVtZS1ibHVlIHtcXG4gIGJvcmRlci1jb2xvcjogaHNsKHZhcigtLXRoZW1lLWJsdWUpKTtcXG59XFxyXFxuLmJvcmRlci10aGVtZS1ibHVlXFxcXC8yMCB7XFxuICBib3JkZXItY29sb3I6IGhzbCh2YXIoLS10aGVtZS1ibHVlKSAvIDAuMik7XFxufVxcclxcbi5ib3JkZXItdGhlbWUtcHVycGxlXFxcXC8zMCB7XFxuICBib3JkZXItY29sb3I6IGhzbCh2YXIoLS10aGVtZS1wdXJwbGUpIC8gMC4zKTtcXG59XFxyXFxuLmJvcmRlci10aGVtZS10ZWFsXFxcXC8zMCB7XFxuICBib3JkZXItY29sb3I6IGhzbCh2YXIoLS10aGVtZS10ZWFsKSAvIDAuMyk7XFxufVxcclxcbi5ib3JkZXItd2hpdGVcXFxcLzEwIHtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gMC4xKTtcXG59XFxyXFxuLmJvcmRlci13aGl0ZVxcXFwvMjAge1xcbiAgYm9yZGVyLWNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyAwLjIpO1xcbn1cXHJcXG4uYm9yZGVyLXdoaXRlXFxcXC81MCB7XFxuICBib3JkZXItY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIDAuNSk7XFxufVxcclxcbi5ib3JkZXItdGhlbWUtcHVycGxlXFxcXC81MCB7XFxuICBib3JkZXItY29sb3I6IGhzbCh2YXIoLS10aGVtZS1wdXJwbGUpIC8gMC41KTtcXG59XFxyXFxuLmJvcmRlci1iLXRoZW1lLXRlYWxcXFxcLzMwIHtcXG4gIGJvcmRlci1ib3R0b20tY29sb3I6IGhzbCh2YXIoLS10aGVtZS10ZWFsKSAvIDAuMyk7XFxufVxcclxcbi5ib3JkZXItbC10cmFuc3BhcmVudCB7XFxuICBib3JkZXItbGVmdC1jb2xvcjogdHJhbnNwYXJlbnQ7XFxufVxcclxcbi5ib3JkZXItci10cmFuc3BhcmVudCB7XFxuICBib3JkZXItcmlnaHQtY29sb3I6IHRyYW5zcGFyZW50O1xcbn1cXHJcXG4uYmctYW1iZXItNTAge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NSAyNTEgMjM1IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXHJcXG4uYmctYW1iZXItNTAwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNDUgMTU4IDExIC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXHJcXG4uYmctYW1iZXItNTAwXFxcXC8xMCB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjQ1IDE1OCAxMSAvIDAuMSk7XFxufVxcclxcbi5iZy1hbWJlci05MDBcXFxcLzIwIHtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigxMjAgNTMgMTUgLyAwLjIpO1xcbn1cXHJcXG4uYmctYmFja2dyb3VuZCB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBoc2wodmFyKC0tYmFja2dyb3VuZCkpO1xcbn1cXHJcXG4uYmctYmFja2dyb3VuZFxcXFwvNTAge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogaHNsKHZhcigtLWJhY2tncm91bmQpIC8gMC41KTtcXG59XFxyXFxuLmJnLWJhY2tncm91bmRcXFxcLzgwIHtcXG4gIGJhY2tncm91bmQtY29sb3I6IGhzbCh2YXIoLS1iYWNrZ3JvdW5kKSAvIDAuOCk7XFxufVxcclxcbi5iZy1iYWNrZ3JvdW5kXFxcXC85NSB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBoc2wodmFyKC0tYmFja2dyb3VuZCkgLyAwLjk1KTtcXG59XFxyXFxuLmJnLWJsYWNrXFxcXC8yMCB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMCAwIDAgLyAwLjIpO1xcbn1cXHJcXG4uYmctYmxhY2tcXFxcLzgwIHtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigwIDAgMCAvIDAuOCk7XFxufVxcclxcbi5iZy1ib3JkZXIge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogaHNsKHZhcigtLWJvcmRlcikpO1xcbn1cXHJcXG4uYmctY2FyZCB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBoc2wodmFyKC0tY2FyZCkpO1xcbn1cXHJcXG4uYmctZGVzdHJ1Y3RpdmUge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogaHNsKHZhcigtLWRlc3RydWN0aXZlKSk7XFxufVxcclxcbi5iZy1kZXN0cnVjdGl2ZVxcXFwvMTAge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogaHNsKHZhcigtLWRlc3RydWN0aXZlKSAvIDAuMSk7XFxufVxcclxcbi5iZy1ncmF5LTIwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjI5IDIzMSAyMzUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcclxcbi5iZy1ncmF5LTMwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjA5IDIxMyAyMTkgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcclxcbi5iZy1ncmF5LTcwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoNTUgNjUgODEgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcclxcbi5iZy1ncmF5LTgwMCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMzEgNDEgNTUgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcclxcbi5iZy1ncmVlbi01MCB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjQwIDI1MyAyNDQgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcclxcbi5iZy1ncmVlbi01MDBcXFxcLzIwIHtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigzNCAxOTcgOTQgLyAwLjIpO1xcbn1cXHJcXG4uYmctZ3JlZW4tOTAwXFxcXC8yMCB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjAgODMgNDUgLyAwLjIpO1xcbn1cXHJcXG4uYmctbXV0ZWQge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogaHNsKHZhcigtLW11dGVkKSk7XFxufVxcclxcbi5iZy1wb3BvdmVyIHtcXG4gIGJhY2tncm91bmQtY29sb3I6IGhzbCh2YXIoLS1wb3BvdmVyKSk7XFxufVxcclxcbi5iZy1wcmltYXJ5IHtcXG4gIGJhY2tncm91bmQtY29sb3I6IGhzbCh2YXIoLS1wcmltYXJ5KSk7XFxufVxcclxcbi5iZy1wcmltYXJ5XFxcXC8yMCB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBoc2wodmFyKC0tcHJpbWFyeSkgLyAwLjIpO1xcbn1cXHJcXG4uYmctcmVkLTUwIHtcXG4gIC0tdHctYmctb3BhY2l0eTogMTtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNTQgMjQyIDI0MiAvIHZhcigtLXR3LWJnLW9wYWNpdHksIDEpKTtcXG59XFxyXFxuLmJnLXJlZC01MDBcXFxcLzEwIHtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyMzkgNjggNjggLyAwLjEpO1xcbn1cXHJcXG4uYmctcmVkLTUwMFxcXFwvMjAge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIzOSA2OCA2OCAvIDAuMik7XFxufVxcclxcbi5iZy1yZWQtOTAwXFxcXC8yMCB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMTI3IDI5IDI5IC8gMC4yKTtcXG59XFxyXFxuLmJnLXJlZC05NTBcXFxcLzIwIHtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYig2OSAxMCAxMCAvIDAuMik7XFxufVxcclxcbi5iZy1yZWQtOTUwXFxcXC84MCB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoNjkgMTAgMTAgLyAwLjgpO1xcbn1cXHJcXG4uYmctc2Vjb25kYXJ5IHtcXG4gIGJhY2tncm91bmQtY29sb3I6IGhzbCh2YXIoLS1zZWNvbmRhcnkpKTtcXG59XFxyXFxuLmJnLXRoZW1lLWJsdWUge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogaHNsKHZhcigtLXRoZW1lLWJsdWUpKTtcXG59XFxyXFxuLmJnLXRoZW1lLWJsdWVcXFxcLzEwIHtcXG4gIGJhY2tncm91bmQtY29sb3I6IGhzbCh2YXIoLS10aGVtZS1ibHVlKSAvIDAuMSk7XFxufVxcclxcbi5iZy10aGVtZS1ibHVlXFxcXC8yMCB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBoc2wodmFyKC0tdGhlbWUtYmx1ZSkgLyAwLjIpO1xcbn1cXHJcXG4uYmctdGhlbWUtYmx1ZVxcXFwvNSB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBoc2wodmFyKC0tdGhlbWUtYmx1ZSkgLyAwLjA1KTtcXG59XFxyXFxuLmJnLXRoZW1lLXB1cnBsZSB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBoc2wodmFyKC0tdGhlbWUtcHVycGxlKSk7XFxufVxcclxcbi5iZy10aGVtZS1wdXJwbGVcXFxcLzEwIHtcXG4gIGJhY2tncm91bmQtY29sb3I6IGhzbCh2YXIoLS10aGVtZS1wdXJwbGUpIC8gMC4xKTtcXG59XFxyXFxuLmJnLXRoZW1lLXB1cnBsZVxcXFwvMjAge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogaHNsKHZhcigtLXRoZW1lLXB1cnBsZSkgLyAwLjIpO1xcbn1cXHJcXG4uYmctdGhlbWUtdGVhbFxcXFwvMTAge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogaHNsKHZhcigtLXRoZW1lLXRlYWwpIC8gMC4xKTtcXG59XFxyXFxuLmJnLXRyYW5zcGFyZW50IHtcXG4gIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50O1xcbn1cXHJcXG4uYmctd2hpdGUge1xcbiAgLS10dy1iZy1vcGFjaXR5OiAxO1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gdmFyKC0tdHctYmctb3BhY2l0eSwgMSkpO1xcbn1cXHJcXG4uYmctd2hpdGVcXFxcLzEwIHtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIDAuMSk7XFxufVxcclxcbi5iZy13aGl0ZVxcXFwvNSB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyAwLjA1KTtcXG59XFxyXFxuLmJnLWdyYWRpZW50LXRvLWIge1xcbiAgYmFja2dyb3VuZC1pbWFnZTogbGluZWFyLWdyYWRpZW50KHRvIGJvdHRvbSwgdmFyKC0tdHctZ3JhZGllbnQtc3RvcHMpKTtcXG59XFxyXFxuLmJnLWdyYWRpZW50LXRvLWJyIHtcXG4gIGJhY2tncm91bmQtaW1hZ2U6IGxpbmVhci1ncmFkaWVudCh0byBib3R0b20gcmlnaHQsIHZhcigtLXR3LWdyYWRpZW50LXN0b3BzKSk7XFxufVxcclxcbi5iZy1ncmFkaWVudC10by1yIHtcXG4gIGJhY2tncm91bmQtaW1hZ2U6IGxpbmVhci1ncmFkaWVudCh0byByaWdodCwgdmFyKC0tdHctZ3JhZGllbnQtc3RvcHMpKTtcXG59XFxyXFxuLmZyb20tYW1iZXItNTAwIHtcXG4gIC0tdHctZ3JhZGllbnQtZnJvbTogI2Y1OWUwYiB2YXIoLS10dy1ncmFkaWVudC1mcm9tLXBvc2l0aW9uKTtcXG4gIC0tdHctZ3JhZGllbnQtdG86IHJnYigyNDUgMTU4IDExIC8gMCkgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC1zdG9wczogdmFyKC0tdHctZ3JhZGllbnQtZnJvbSksIHZhcigtLXR3LWdyYWRpZW50LXRvKTtcXG59XFxyXFxuLmZyb20tZ3JheS01MCB7XFxuICAtLXR3LWdyYWRpZW50LWZyb206ICNmOWZhZmIgdmFyKC0tdHctZ3JhZGllbnQtZnJvbS1wb3NpdGlvbik7XFxuICAtLXR3LWdyYWRpZW50LXRvOiByZ2IoMjQ5IDI1MCAyNTEgLyAwKSB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxuICAtLXR3LWdyYWRpZW50LXN0b3BzOiB2YXIoLS10dy1ncmFkaWVudC1mcm9tKSwgdmFyKC0tdHctZ3JhZGllbnQtdG8pO1xcbn1cXHJcXG4uZnJvbS1ncmF5LTkwMCB7XFxuICAtLXR3LWdyYWRpZW50LWZyb206ICMxMTE4MjcgdmFyKC0tdHctZ3JhZGllbnQtZnJvbS1wb3NpdGlvbik7XFxuICAtLXR3LWdyYWRpZW50LXRvOiByZ2IoMTcgMjQgMzkgLyAwKSB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxuICAtLXR3LWdyYWRpZW50LXN0b3BzOiB2YXIoLS10dy1ncmFkaWVudC1mcm9tKSwgdmFyKC0tdHctZ3JhZGllbnQtdG8pO1xcbn1cXHJcXG4uZnJvbS1ncmVlbi01MDAge1xcbiAgLS10dy1ncmFkaWVudC1mcm9tOiAjMjJjNTVlIHZhcigtLXR3LWdyYWRpZW50LWZyb20tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC10bzogcmdiKDM0IDE5NyA5NCAvIDApIHZhcigtLXR3LWdyYWRpZW50LXRvLXBvc2l0aW9uKTtcXG4gIC0tdHctZ3JhZGllbnQtc3RvcHM6IHZhcigtLXR3LWdyYWRpZW50LWZyb20pLCB2YXIoLS10dy1ncmFkaWVudC10byk7XFxufVxcclxcbi5mcm9tLXRoZW1lLWJsdWUge1xcbiAgLS10dy1ncmFkaWVudC1mcm9tOiBoc2wodmFyKC0tdGhlbWUtYmx1ZSkpIHZhcigtLXR3LWdyYWRpZW50LWZyb20tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC10bzogaHNsKHZhcigtLXRoZW1lLWJsdWUpIC8gMCkgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC1zdG9wczogdmFyKC0tdHctZ3JhZGllbnQtZnJvbSksIHZhcigtLXR3LWdyYWRpZW50LXRvKTtcXG59XFxyXFxuLmZyb20tdGhlbWUtYmx1ZVxcXFwvMjAge1xcbiAgLS10dy1ncmFkaWVudC1mcm9tOiBoc2wodmFyKC0tdGhlbWUtYmx1ZSkgLyAwLjIpIHZhcigtLXR3LWdyYWRpZW50LWZyb20tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC10bzogaHNsKHZhcigtLXRoZW1lLWJsdWUpIC8gMCkgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC1zdG9wczogdmFyKC0tdHctZ3JhZGllbnQtZnJvbSksIHZhcigtLXR3LWdyYWRpZW50LXRvKTtcXG59XFxyXFxuLmZyb20tdGhlbWUtcHVycGxlIHtcXG4gIC0tdHctZ3JhZGllbnQtZnJvbTogaHNsKHZhcigtLXRoZW1lLXB1cnBsZSkpIHZhcigtLXR3LWdyYWRpZW50LWZyb20tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC10bzogaHNsKHZhcigtLXRoZW1lLXB1cnBsZSkgLyAwKSB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxuICAtLXR3LWdyYWRpZW50LXN0b3BzOiB2YXIoLS10dy1ncmFkaWVudC1mcm9tKSwgdmFyKC0tdHctZ3JhZGllbnQtdG8pO1xcbn1cXHJcXG4uZnJvbS10aGVtZS1wdXJwbGVcXFxcLzUge1xcbiAgLS10dy1ncmFkaWVudC1mcm9tOiBoc2wodmFyKC0tdGhlbWUtcHVycGxlKSAvIDAuMDUpIHZhcigtLXR3LWdyYWRpZW50LWZyb20tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC10bzogaHNsKHZhcigtLXRoZW1lLXB1cnBsZSkgLyAwKSB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxuICAtLXR3LWdyYWRpZW50LXN0b3BzOiB2YXIoLS10dy1ncmFkaWVudC1mcm9tKSwgdmFyKC0tdHctZ3JhZGllbnQtdG8pO1xcbn1cXHJcXG4udmlhLXB1cnBsZS02MDAge1xcbiAgLS10dy1ncmFkaWVudC10bzogcmdiKDE0NyA1MSAyMzQgLyAwKSAgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbiAgLS10dy1ncmFkaWVudC1zdG9wczogdmFyKC0tdHctZ3JhZGllbnQtZnJvbSksICM5MzMzZWEgdmFyKC0tdHctZ3JhZGllbnQtdmlhLXBvc2l0aW9uKSwgdmFyKC0tdHctZ3JhZGllbnQtdG8pO1xcbn1cXHJcXG4udG8tYmxhY2sge1xcbiAgLS10dy1ncmFkaWVudC10bzogIzAwMCB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxufVxcclxcbi50by1lbWVyYWxkLTYwMCB7XFxuICAtLXR3LWdyYWRpZW50LXRvOiAjMDU5NjY5IHZhcigtLXR3LWdyYWRpZW50LXRvLXBvc2l0aW9uKTtcXG59XFxyXFxuLnRvLWdyYXktODAwIHtcXG4gIC0tdHctZ3JhZGllbnQtdG86ICMxZjI5MzcgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbn1cXHJcXG4udG8tb3JhbmdlLTYwMCB7XFxuICAtLXR3LWdyYWRpZW50LXRvOiAjZWE1ODBjIHZhcigtLXR3LWdyYWRpZW50LXRvLXBvc2l0aW9uKTtcXG59XFxyXFxuLnRvLXB1cnBsZS01MDAge1xcbiAgLS10dy1ncmFkaWVudC10bzogI2E4NTVmNyB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxufVxcclxcbi50by1wdXJwbGUtNTAwXFxcXC8yMCB7XFxuICAtLXR3LWdyYWRpZW50LXRvOiByZ2IoMTY4IDg1IDI0NyAvIDAuMikgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbn1cXHJcXG4udG8tcHVycGxlLTYwMCB7XFxuICAtLXR3LWdyYWRpZW50LXRvOiAjOTMzM2VhIHZhcigtLXR3LWdyYWRpZW50LXRvLXBvc2l0aW9uKTtcXG59XFxyXFxuLnRvLXRoZW1lLWJsdWUge1xcbiAgLS10dy1ncmFkaWVudC10bzogaHNsKHZhcigtLXRoZW1lLWJsdWUpKSB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxufVxcclxcbi50by10aGVtZS1wdXJwbGUge1xcbiAgLS10dy1ncmFkaWVudC10bzogaHNsKHZhcigtLXRoZW1lLXB1cnBsZSkpIHZhcigtLXR3LWdyYWRpZW50LXRvLXBvc2l0aW9uKTtcXG59XFxyXFxuLnRvLXdoaXRlIHtcXG4gIC0tdHctZ3JhZGllbnQtdG86ICNmZmYgdmFyKC0tdHctZ3JhZGllbnQtdG8tcG9zaXRpb24pO1xcbn1cXHJcXG4udG8tdGhlbWUtYmx1ZVxcXFwvNSB7XFxuICAtLXR3LWdyYWRpZW50LXRvOiBoc2wodmFyKC0tdGhlbWUtYmx1ZSkgLyAwLjA1KSB2YXIoLS10dy1ncmFkaWVudC10by1wb3NpdGlvbik7XFxufVxcclxcbi5iZy1cXFxcW2xlbmd0aFxcXFw6MjAwXFxcXCVfMTAwXFxcXCVcXFxcXSB7XFxuICBiYWNrZ3JvdW5kLXNpemU6IDIwMCUgMTAwJTtcXG59XFxyXFxuLmJnLWNsaXAtdGV4dCB7XFxuICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcXG4gICAgICAgICAgYmFja2dyb3VuZC1jbGlwOiB0ZXh0O1xcbn1cXHJcXG4uZmlsbC1jdXJyZW50IHtcXG4gIGZpbGw6IGN1cnJlbnRDb2xvcjtcXG59XFxyXFxuLm9iamVjdC1jb3ZlciB7XFxuICAtby1vYmplY3QtZml0OiBjb3ZlcjtcXG4gICAgIG9iamVjdC1maXQ6IGNvdmVyO1xcbn1cXHJcXG4ucC0xIHtcXG4gIHBhZGRpbmc6IDAuMjVyZW07XFxufVxcclxcbi5wLTIge1xcbiAgcGFkZGluZzogMC41cmVtO1xcbn1cXHJcXG4ucC0zIHtcXG4gIHBhZGRpbmc6IDAuNzVyZW07XFxufVxcclxcbi5wLTQge1xcbiAgcGFkZGluZzogMXJlbTtcXG59XFxyXFxuLnAtNSB7XFxuICBwYWRkaW5nOiAxLjI1cmVtO1xcbn1cXHJcXG4ucC02IHtcXG4gIHBhZGRpbmc6IDEuNXJlbTtcXG59XFxyXFxuLnAtOCB7XFxuICBwYWRkaW5nOiAycmVtO1xcbn1cXHJcXG4ucHgtMiB7XFxuICBwYWRkaW5nLWxlZnQ6IDAuNXJlbTtcXG4gIHBhZGRpbmctcmlnaHQ6IDAuNXJlbTtcXG59XFxyXFxuLnB4LTMge1xcbiAgcGFkZGluZy1sZWZ0OiAwLjc1cmVtO1xcbiAgcGFkZGluZy1yaWdodDogMC43NXJlbTtcXG59XFxyXFxuLnB4LTQge1xcbiAgcGFkZGluZy1sZWZ0OiAxcmVtO1xcbiAgcGFkZGluZy1yaWdodDogMXJlbTtcXG59XFxyXFxuLnB4LTgge1xcbiAgcGFkZGluZy1sZWZ0OiAycmVtO1xcbiAgcGFkZGluZy1yaWdodDogMnJlbTtcXG59XFxyXFxuLnB5LTEge1xcbiAgcGFkZGluZy10b3A6IDAuMjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMC4yNXJlbTtcXG59XFxyXFxuLnB5LTFcXFxcLjUge1xcbiAgcGFkZGluZy10b3A6IDAuMzc1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuMzc1cmVtO1xcbn1cXHJcXG4ucHktMTAge1xcbiAgcGFkZGluZy10b3A6IDIuNXJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAyLjVyZW07XFxufVxcclxcbi5weS0xMiB7XFxuICBwYWRkaW5nLXRvcDogM3JlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAzcmVtO1xcbn1cXHJcXG4ucHktMiB7XFxuICBwYWRkaW5nLXRvcDogMC41cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuNXJlbTtcXG59XFxyXFxuLnB5LTIwIHtcXG4gIHBhZGRpbmctdG9wOiA1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDVyZW07XFxufVxcclxcbi5weS0zIHtcXG4gIHBhZGRpbmctdG9wOiAwLjc1cmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDAuNzVyZW07XFxufVxcclxcbi5weS00IHtcXG4gIHBhZGRpbmctdG9wOiAxcmVtO1xcbiAgcGFkZGluZy1ib3R0b206IDFyZW07XFxufVxcclxcbi5weS02IHtcXG4gIHBhZGRpbmctdG9wOiAxLjVyZW07XFxuICBwYWRkaW5nLWJvdHRvbTogMS41cmVtO1xcbn1cXHJcXG4ucHktOCB7XFxuICBwYWRkaW5nLXRvcDogMnJlbTtcXG4gIHBhZGRpbmctYm90dG9tOiAycmVtO1xcbn1cXHJcXG4ucGItMiB7XFxuICBwYWRkaW5nLWJvdHRvbTogMC41cmVtO1xcbn1cXHJcXG4ucGItMjQge1xcbiAgcGFkZGluZy1ib3R0b206IDZyZW07XFxufVxcclxcbi5wbC04IHtcXG4gIHBhZGRpbmctbGVmdDogMnJlbTtcXG59XFxyXFxuLnByLTE2IHtcXG4gIHBhZGRpbmctcmlnaHQ6IDRyZW07XFxufVxcclxcbi5wci0yIHtcXG4gIHBhZGRpbmctcmlnaHQ6IDAuNXJlbTtcXG59XFxyXFxuLnB0LTAge1xcbiAgcGFkZGluZy10b3A6IDBweDtcXG59XFxyXFxuLnB0LTIge1xcbiAgcGFkZGluZy10b3A6IDAuNXJlbTtcXG59XFxyXFxuLnB0LTYge1xcbiAgcGFkZGluZy10b3A6IDEuNXJlbTtcXG59XFxyXFxuLnRleHQtbGVmdCB7XFxuICB0ZXh0LWFsaWduOiBsZWZ0O1xcbn1cXHJcXG4udGV4dC1jZW50ZXIge1xcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xcbn1cXHJcXG4udGV4dC0yeGwge1xcbiAgZm9udC1zaXplOiAxLjVyZW07XFxuICBsaW5lLWhlaWdodDogMnJlbTtcXG59XFxyXFxuLnRleHQtM3hsIHtcXG4gIGZvbnQtc2l6ZTogMS44NzVyZW07XFxuICBsaW5lLWhlaWdodDogMi4yNXJlbTtcXG59XFxyXFxuLnRleHQtNHhsIHtcXG4gIGZvbnQtc2l6ZTogMi4yNXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAyLjVyZW07XFxufVxcclxcbi50ZXh0LWxnIHtcXG4gIGZvbnQtc2l6ZTogMS4xMjVyZW07XFxuICBsaW5lLWhlaWdodDogMS43NXJlbTtcXG59XFxyXFxuLnRleHQtc20ge1xcbiAgZm9udC1zaXplOiAwLjg3NXJlbTtcXG4gIGxpbmUtaGVpZ2h0OiAxLjI1cmVtO1xcbn1cXHJcXG4udGV4dC14bCB7XFxuICBmb250LXNpemU6IDEuMjVyZW07XFxuICBsaW5lLWhlaWdodDogMS43NXJlbTtcXG59XFxyXFxuLnRleHQteHMge1xcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xcbiAgbGluZS1oZWlnaHQ6IDFyZW07XFxufVxcclxcbi5mb250LWJvbGQge1xcbiAgZm9udC13ZWlnaHQ6IDcwMDtcXG59XFxyXFxuLmZvbnQtbWVkaXVtIHtcXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XFxufVxcclxcbi5mb250LXNlbWlib2xkIHtcXG4gIGZvbnQtd2VpZ2h0OiA2MDA7XFxufVxcclxcbi5sZWFkaW5nLW5vbmUge1xcbiAgbGluZS1oZWlnaHQ6IDE7XFxufVxcclxcbi50cmFja2luZy10aWdodCB7XFxuICBsZXR0ZXItc3BhY2luZzogLTAuMDI1ZW07XFxufVxcclxcbi50ZXh0LWFtYmVyLTQwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjUxIDE5MSAzNiAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXHJcXG4udGV4dC1hbWJlci01MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDI0NSAxNTggMTEgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxyXFxuLnRleHQtYW1iZXItNjAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyMTcgMTE5IDYgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxyXFxuLnRleHQtYmxhY2sge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDAgMCAwIC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcclxcbi50ZXh0LWNhcmQtZm9yZWdyb3VuZCB7XFxuICBjb2xvcjogaHNsKHZhcigtLWNhcmQtZm9yZWdyb3VuZCkpO1xcbn1cXHJcXG4udGV4dC1jdXJyZW50IHtcXG4gIGNvbG9yOiBjdXJyZW50Q29sb3I7XFxufVxcclxcbi50ZXh0LWRlc3RydWN0aXZlIHtcXG4gIGNvbG9yOiBoc2wodmFyKC0tZGVzdHJ1Y3RpdmUpKTtcXG59XFxyXFxuLnRleHQtZGVzdHJ1Y3RpdmUtZm9yZWdyb3VuZCB7XFxuICBjb2xvcjogaHNsKHZhcigtLWRlc3RydWN0aXZlLWZvcmVncm91bmQpKTtcXG59XFxyXFxuLnRleHQtZm9yZWdyb3VuZCB7XFxuICBjb2xvcjogaHNsKHZhcigtLWZvcmVncm91bmQpKTtcXG59XFxyXFxuLnRleHQtZ3JheS0yMDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIyOSAyMzEgMjM1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcclxcbi50ZXh0LWdyYXktMzAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyMDkgMjEzIDIxOSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXHJcXG4udGV4dC1ncmF5LTUwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTA3IDExNCAxMjggLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxyXFxuLnRleHQtZ3JheS02MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDc1IDg1IDk5IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcclxcbi50ZXh0LWdyYXktNzAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYig1NSA2NSA4MSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXHJcXG4udGV4dC1ncmF5LTkwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMTcgMjQgMzkgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxyXFxuLnRleHQtZ3JlZW4tNDAwIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYig3NCAyMjIgMTI4IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcclxcbi50ZXh0LWdyZWVuLTUwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMzQgMTk3IDk0IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcclxcbi50ZXh0LWdyZWVuLTYwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjIgMTYzIDc0IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcclxcbi50ZXh0LW11dGVkLWZvcmVncm91bmQge1xcbiAgY29sb3I6IGhzbCh2YXIoLS1tdXRlZC1mb3JlZ3JvdW5kKSk7XFxufVxcclxcbi50ZXh0LXBvcG92ZXItZm9yZWdyb3VuZCB7XFxuICBjb2xvcjogaHNsKHZhcigtLXBvcG92ZXItZm9yZWdyb3VuZCkpO1xcbn1cXHJcXG4udGV4dC1wcmltYXJ5IHtcXG4gIGNvbG9yOiBoc2wodmFyKC0tcHJpbWFyeSkpO1xcbn1cXHJcXG4udGV4dC1wcmltYXJ5LWZvcmVncm91bmQge1xcbiAgY29sb3I6IGhzbCh2YXIoLS1wcmltYXJ5LWZvcmVncm91bmQpKTtcXG59XFxyXFxuLnRleHQtcmVkLTQwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjQ4IDExMyAxMTMgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxyXFxuLnRleHQtcmVkLTUwMCB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjM5IDY4IDY4IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcclxcbi50ZXh0LXJlZC02MDAge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDIyMCAzOCAzOCAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXHJcXG4udGV4dC1zZWNvbmRhcnktZm9yZWdyb3VuZCB7XFxuICBjb2xvcjogaHNsKHZhcigtLXNlY29uZGFyeS1mb3JlZ3JvdW5kKSk7XFxufVxcclxcbi50ZXh0LXRoZW1lLWJsdWUge1xcbiAgY29sb3I6IGhzbCh2YXIoLS10aGVtZS1ibHVlKSk7XFxufVxcclxcbi50ZXh0LXRoZW1lLXB1cnBsZSB7XFxuICBjb2xvcjogaHNsKHZhcigtLXRoZW1lLXB1cnBsZSkpO1xcbn1cXHJcXG4udGV4dC10aGVtZS10ZWFsIHtcXG4gIGNvbG9yOiBoc2wodmFyKC0tdGhlbWUtdGVhbCkpO1xcbn1cXHJcXG4udGV4dC10cmFuc3BhcmVudCB7XFxuICBjb2xvcjogdHJhbnNwYXJlbnQ7XFxufVxcclxcbi50ZXh0LXdoaXRlIHtcXG4gIC0tdHctdGV4dC1vcGFjaXR5OiAxO1xcbiAgY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIHZhcigtLXR3LXRleHQtb3BhY2l0eSwgMSkpO1xcbn1cXHJcXG4udW5kZXJsaW5lLW9mZnNldC00IHtcXG4gIHRleHQtdW5kZXJsaW5lLW9mZnNldDogNHB4O1xcbn1cXHJcXG4ub3BhY2l0eS0wIHtcXG4gIG9wYWNpdHk6IDA7XFxufVxcclxcbi5vcGFjaXR5LTI1IHtcXG4gIG9wYWNpdHk6IDAuMjU7XFxufVxcclxcbi5vcGFjaXR5LTUwIHtcXG4gIG9wYWNpdHk6IDAuNTtcXG59XFxyXFxuLm9wYWNpdHktNzAge1xcbiAgb3BhY2l0eTogMC43O1xcbn1cXHJcXG4ub3BhY2l0eS03NSB7XFxuICBvcGFjaXR5OiAwLjc1O1xcbn1cXHJcXG4ub3BhY2l0eS04MCB7XFxuICBvcGFjaXR5OiAwLjg7XFxufVxcclxcbi5zaGFkb3ctbGcge1xcbiAgLS10dy1zaGFkb3c6IDAgMTBweCAxNXB4IC0zcHggcmdiKDAgMCAwIC8gMC4xKSwgMCA0cHggNnB4IC00cHggcmdiKDAgMCAwIC8gMC4xKTtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMTBweCAxNXB4IC0zcHggdmFyKC0tdHctc2hhZG93LWNvbG9yKSwgMCA0cHggNnB4IC00cHggdmFyKC0tdHctc2hhZG93LWNvbG9yKTtcXG4gIGJveC1zaGFkb3c6IHZhcigtLXR3LXJpbmctb2Zmc2V0LXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctcmluZy1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXNoYWRvdyk7XFxufVxcclxcbi5zaGFkb3ctbWQge1xcbiAgLS10dy1zaGFkb3c6IDAgNHB4IDZweCAtMXB4IHJnYigwIDAgMCAvIDAuMSksIDAgMnB4IDRweCAtMnB4IHJnYigwIDAgMCAvIDAuMSk7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDRweCA2cHggLTFweCB2YXIoLS10dy1zaGFkb3ctY29sb3IpLCAwIDJweCA0cHggLTJweCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG59XFxyXFxuLnNoYWRvdy1zbSB7XFxuICAtLXR3LXNoYWRvdzogMCAxcHggMnB4IDAgcmdiKDAgMCAwIC8gMC4wNSk7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDFweCAycHggMCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG59XFxyXFxuLnNoYWRvdy14bCB7XFxuICAtLXR3LXNoYWRvdzogMCAyMHB4IDI1cHggLTVweCByZ2IoMCAwIDAgLyAwLjEpLCAwIDhweCAxMHB4IC02cHggcmdiKDAgMCAwIC8gMC4xKTtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMjBweCAyNXB4IC01cHggdmFyKC0tdHctc2hhZG93LWNvbG9yKSwgMCA4cHggMTBweCAtNnB4IHZhcigtLXR3LXNoYWRvdy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXJpbmctc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1zaGFkb3cpO1xcbn1cXHJcXG4uc2hhZG93LTJ4bCB7XFxuICAtLXR3LXNoYWRvdzogMCAyNXB4IDUwcHggLTEycHggcmdiKDAgMCAwIC8gMC4yNSk7XFxuICAtLXR3LXNoYWRvdy1jb2xvcmVkOiAwIDI1cHggNTBweCAtMTJweCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG59XFxyXFxuLnNoYWRvdy10aGVtZS1wdXJwbGVcXFxcLzEwIHtcXG4gIC0tdHctc2hhZG93LWNvbG9yOiBoc2wodmFyKC0tdGhlbWUtcHVycGxlKSAvIDAuMSk7XFxuICAtLXR3LXNoYWRvdzogdmFyKC0tdHctc2hhZG93LWNvbG9yZWQpO1xcbn1cXHJcXG4uc2hhZG93LXRoZW1lLXB1cnBsZVxcXFwvMjAge1xcbiAgLS10dy1zaGFkb3ctY29sb3I6IGhzbCh2YXIoLS10aGVtZS1wdXJwbGUpIC8gMC4yKTtcXG4gIC0tdHctc2hhZG93OiB2YXIoLS10dy1zaGFkb3ctY29sb3JlZCk7XFxufVxcclxcbi5vdXRsaW5lLW5vbmUge1xcbiAgb3V0bGluZTogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xcbiAgb3V0bGluZS1vZmZzZXQ6IDJweDtcXG59XFxyXFxuLm91dGxpbmUge1xcbiAgb3V0bGluZS1zdHlsZTogc29saWQ7XFxufVxcclxcbi5yaW5nLW9mZnNldC1iYWNrZ3JvdW5kIHtcXG4gIC0tdHctcmluZy1vZmZzZXQtY29sb3I6IGhzbCh2YXIoLS1iYWNrZ3JvdW5kKSk7XFxufVxcclxcbi5ibHVyIHtcXG4gIC0tdHctYmx1cjogYmx1cig4cHgpO1xcbiAgZmlsdGVyOiB2YXIoLS10dy1ibHVyKSB2YXIoLS10dy1icmlnaHRuZXNzKSB2YXIoLS10dy1jb250cmFzdCkgdmFyKC0tdHctZ3JheXNjYWxlKSB2YXIoLS10dy1odWUtcm90YXRlKSB2YXIoLS10dy1pbnZlcnQpIHZhcigtLXR3LXNhdHVyYXRlKSB2YXIoLS10dy1zZXBpYSkgdmFyKC0tdHctZHJvcC1zaGFkb3cpO1xcbn1cXHJcXG4uZmlsdGVyIHtcXG4gIGZpbHRlcjogdmFyKC0tdHctYmx1cikgdmFyKC0tdHctYnJpZ2h0bmVzcykgdmFyKC0tdHctY29udHJhc3QpIHZhcigtLXR3LWdyYXlzY2FsZSkgdmFyKC0tdHctaHVlLXJvdGF0ZSkgdmFyKC0tdHctaW52ZXJ0KSB2YXIoLS10dy1zYXR1cmF0ZSkgdmFyKC0tdHctc2VwaWEpIHZhcigtLXR3LWRyb3Atc2hhZG93KTtcXG59XFxyXFxuLmJhY2tkcm9wLWJsdXItbWQge1xcbiAgLS10dy1iYWNrZHJvcC1ibHVyOiBibHVyKDEycHgpO1xcbiAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IHZhcigtLXR3LWJhY2tkcm9wLWJsdXIpIHZhcigtLXR3LWJhY2tkcm9wLWJyaWdodG5lc3MpIHZhcigtLXR3LWJhY2tkcm9wLWNvbnRyYXN0KSB2YXIoLS10dy1iYWNrZHJvcC1ncmF5c2NhbGUpIHZhcigtLXR3LWJhY2tkcm9wLWh1ZS1yb3RhdGUpIHZhcigtLXR3LWJhY2tkcm9wLWludmVydCkgdmFyKC0tdHctYmFja2Ryb3Atb3BhY2l0eSkgdmFyKC0tdHctYmFja2Ryb3Atc2F0dXJhdGUpIHZhcigtLXR3LWJhY2tkcm9wLXNlcGlhKTtcXG4gIGJhY2tkcm9wLWZpbHRlcjogdmFyKC0tdHctYmFja2Ryb3AtYmx1cikgdmFyKC0tdHctYmFja2Ryb3AtYnJpZ2h0bmVzcykgdmFyKC0tdHctYmFja2Ryb3AtY29udHJhc3QpIHZhcigtLXR3LWJhY2tkcm9wLWdyYXlzY2FsZSkgdmFyKC0tdHctYmFja2Ryb3AtaHVlLXJvdGF0ZSkgdmFyKC0tdHctYmFja2Ryb3AtaW52ZXJ0KSB2YXIoLS10dy1iYWNrZHJvcC1vcGFjaXR5KSB2YXIoLS10dy1iYWNrZHJvcC1zYXR1cmF0ZSkgdmFyKC0tdHctYmFja2Ryb3Atc2VwaWEpO1xcbn1cXHJcXG4uYmFja2Ryb3AtYmx1ci1zbSB7XFxuICAtLXR3LWJhY2tkcm9wLWJsdXI6IGJsdXIoNHB4KTtcXG4gIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyOiB2YXIoLS10dy1iYWNrZHJvcC1ibHVyKSB2YXIoLS10dy1iYWNrZHJvcC1icmlnaHRuZXNzKSB2YXIoLS10dy1iYWNrZHJvcC1jb250cmFzdCkgdmFyKC0tdHctYmFja2Ryb3AtZ3JheXNjYWxlKSB2YXIoLS10dy1iYWNrZHJvcC1odWUtcm90YXRlKSB2YXIoLS10dy1iYWNrZHJvcC1pbnZlcnQpIHZhcigtLXR3LWJhY2tkcm9wLW9wYWNpdHkpIHZhcigtLXR3LWJhY2tkcm9wLXNhdHVyYXRlKSB2YXIoLS10dy1iYWNrZHJvcC1zZXBpYSk7XFxuICBiYWNrZHJvcC1maWx0ZXI6IHZhcigtLXR3LWJhY2tkcm9wLWJsdXIpIHZhcigtLXR3LWJhY2tkcm9wLWJyaWdodG5lc3MpIHZhcigtLXR3LWJhY2tkcm9wLWNvbnRyYXN0KSB2YXIoLS10dy1iYWNrZHJvcC1ncmF5c2NhbGUpIHZhcigtLXR3LWJhY2tkcm9wLWh1ZS1yb3RhdGUpIHZhcigtLXR3LWJhY2tkcm9wLWludmVydCkgdmFyKC0tdHctYmFja2Ryb3Atb3BhY2l0eSkgdmFyKC0tdHctYmFja2Ryb3Atc2F0dXJhdGUpIHZhcigtLXR3LWJhY2tkcm9wLXNlcGlhKTtcXG59XFxyXFxuLnRyYW5zaXRpb24ge1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogY29sb3IsIGJhY2tncm91bmQtY29sb3IsIGJvcmRlci1jb2xvciwgdGV4dC1kZWNvcmF0aW9uLWNvbG9yLCBmaWxsLCBzdHJva2UsIG9wYWNpdHksIGJveC1zaGFkb3csIHRyYW5zZm9ybSwgZmlsdGVyLCAtd2Via2l0LWJhY2tkcm9wLWZpbHRlcjtcXG4gIHRyYW5zaXRpb24tcHJvcGVydHk6IGNvbG9yLCBiYWNrZ3JvdW5kLWNvbG9yLCBib3JkZXItY29sb3IsIHRleHQtZGVjb3JhdGlvbi1jb2xvciwgZmlsbCwgc3Ryb2tlLCBvcGFjaXR5LCBib3gtc2hhZG93LCB0cmFuc2Zvcm0sIGZpbHRlciwgYmFja2Ryb3AtZmlsdGVyO1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogY29sb3IsIGJhY2tncm91bmQtY29sb3IsIGJvcmRlci1jb2xvciwgdGV4dC1kZWNvcmF0aW9uLWNvbG9yLCBmaWxsLCBzdHJva2UsIG9wYWNpdHksIGJveC1zaGFkb3csIHRyYW5zZm9ybSwgZmlsdGVyLCBiYWNrZHJvcC1maWx0ZXIsIC13ZWJraXQtYmFja2Ryb3AtZmlsdGVyO1xcbiAgdHJhbnNpdGlvbi10aW1pbmctZnVuY3Rpb246IGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XFxuICB0cmFuc2l0aW9uLWR1cmF0aW9uOiAxNTBtcztcXG59XFxyXFxuLnRyYW5zaXRpb24tYWxsIHtcXG4gIHRyYW5zaXRpb24tcHJvcGVydHk6IGFsbDtcXG4gIHRyYW5zaXRpb24tdGltaW5nLWZ1bmN0aW9uOiBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xcbiAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMTUwbXM7XFxufVxcclxcbi50cmFuc2l0aW9uLWNvbG9ycyB7XFxuICB0cmFuc2l0aW9uLXByb3BlcnR5OiBjb2xvciwgYmFja2dyb3VuZC1jb2xvciwgYm9yZGVyLWNvbG9yLCB0ZXh0LWRlY29yYXRpb24tY29sb3IsIGZpbGwsIHN0cm9rZTtcXG4gIHRyYW5zaXRpb24tdGltaW5nLWZ1bmN0aW9uOiBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xcbiAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMTUwbXM7XFxufVxcclxcbi50cmFuc2l0aW9uLW9wYWNpdHkge1xcbiAgdHJhbnNpdGlvbi1wcm9wZXJ0eTogb3BhY2l0eTtcXG4gIHRyYW5zaXRpb24tdGltaW5nLWZ1bmN0aW9uOiBjdWJpYy1iZXppZXIoMC40LCAwLCAwLjIsIDEpO1xcbiAgdHJhbnNpdGlvbi1kdXJhdGlvbjogMTUwbXM7XFxufVxcclxcbi50cmFuc2l0aW9uLXRyYW5zZm9ybSB7XFxuICB0cmFuc2l0aW9uLXByb3BlcnR5OiB0cmFuc2Zvcm07XFxuICB0cmFuc2l0aW9uLXRpbWluZy1mdW5jdGlvbjogY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDE1MG1zO1xcbn1cXHJcXG4uZHVyYXRpb24tMjAwIHtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDIwMG1zO1xcbn1cXHJcXG4uZHVyYXRpb24tMzAwIHtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDMwMG1zO1xcbn1cXHJcXG4uZHVyYXRpb24tNTAwIHtcXG4gIHRyYW5zaXRpb24tZHVyYXRpb246IDUwMG1zO1xcbn1cXHJcXG4ud2lsbC1jaGFuZ2UtXFxcXFt0cmFuc2Zvcm1cXFxcMmMgZmlsdGVyXFxcXDJjIG9wYWNpdHlcXFxcXSB7XFxuICB3aWxsLWNoYW5nZTogdHJhbnNmb3JtLGZpbHRlcixvcGFjaXR5O1xcbn1cXHJcXG5Aa2V5ZnJhbWVzIGVudGVyIHtcXG5cXG4gIGZyb20ge1xcbiAgICBvcGFjaXR5OiB2YXIoLS10dy1lbnRlci1vcGFjaXR5LCAxKTtcXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUzZCh2YXIoLS10dy1lbnRlci10cmFuc2xhdGUteCwgMCksIHZhcigtLXR3LWVudGVyLXRyYW5zbGF0ZS15LCAwKSwgMCkgc2NhbGUzZCh2YXIoLS10dy1lbnRlci1zY2FsZSwgMSksIHZhcigtLXR3LWVudGVyLXNjYWxlLCAxKSwgdmFyKC0tdHctZW50ZXItc2NhbGUsIDEpKSByb3RhdGUodmFyKC0tdHctZW50ZXItcm90YXRlLCAwKSk7XFxuICB9XFxufVxcclxcbkBrZXlmcmFtZXMgZXhpdCB7XFxuXFxuICB0byB7XFxuICAgIG9wYWNpdHk6IHZhcigtLXR3LWV4aXQtb3BhY2l0eSwgMSk7XFxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlM2QodmFyKC0tdHctZXhpdC10cmFuc2xhdGUteCwgMCksIHZhcigtLXR3LWV4aXQtdHJhbnNsYXRlLXksIDApLCAwKSBzY2FsZTNkKHZhcigtLXR3LWV4aXQtc2NhbGUsIDEpLCB2YXIoLS10dy1leGl0LXNjYWxlLCAxKSwgdmFyKC0tdHctZXhpdC1zY2FsZSwgMSkpIHJvdGF0ZSh2YXIoLS10dy1leGl0LXJvdGF0ZSwgMCkpO1xcbiAgfVxcbn1cXHJcXG4uYW5pbWF0ZS1pbiB7XFxuICBhbmltYXRpb24tbmFtZTogZW50ZXI7XFxuICBhbmltYXRpb24tZHVyYXRpb246IDE1MG1zO1xcbiAgLS10dy1lbnRlci1vcGFjaXR5OiBpbml0aWFsO1xcbiAgLS10dy1lbnRlci1zY2FsZTogaW5pdGlhbDtcXG4gIC0tdHctZW50ZXItcm90YXRlOiBpbml0aWFsO1xcbiAgLS10dy1lbnRlci10cmFuc2xhdGUteDogaW5pdGlhbDtcXG4gIC0tdHctZW50ZXItdHJhbnNsYXRlLXk6IGluaXRpYWw7XFxufVxcclxcbi5mYWRlLWluLTgwIHtcXG4gIC0tdHctZW50ZXItb3BhY2l0eTogMC44O1xcbn1cXHJcXG4uZHVyYXRpb24tMjAwIHtcXG4gIGFuaW1hdGlvbi1kdXJhdGlvbjogMjAwbXM7XFxufVxcclxcbi5kdXJhdGlvbi0zMDAge1xcbiAgYW5pbWF0aW9uLWR1cmF0aW9uOiAzMDBtcztcXG59XFxyXFxuLmR1cmF0aW9uLTUwMCB7XFxuICBhbmltYXRpb24tZHVyYXRpb246IDUwMG1zO1xcbn1cXHJcXG5cXHJcXG4vKiBTdWJ0bGUgZ2xhc3MgZWZmZWN0ICovXFxyXFxuLnN1YnRsZS1nbGFzcyB7XFxyXFxuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDMpO1xcclxcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDhweCk7XFxyXFxuICAtd2Via2l0LWJhY2tkcm9wLWZpbHRlcjogYmx1cig4cHgpO1xcclxcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KTtcXHJcXG4gIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgdmFyKC0tdGhlbWUtdHJhbnNpdGlvbiksIGJvcmRlci1jb2xvciB2YXIoLS10aGVtZS10cmFuc2l0aW9uKTtcXHJcXG59XFxyXFxuXFxyXFxuLmxpZ2h0IC5zdWJ0bGUtZ2xhc3Mge1xcclxcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjAzKTtcXHJcXG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMCwgMCwgMC4wNSk7XFxyXFxufVxcclxcblxcclxcbi8qIFN1YnRsZSBib3JkZXIgZ2xvdyAqL1xcclxcbi5zdWJ0bGUtYm9yZGVyLWdsb3cge1xcclxcbiAgYm94LXNoYWRvdzogMCAwIDAgMXB4IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNSk7XFxyXFxuICB0cmFuc2l0aW9uOiBib3gtc2hhZG93IHZhcigtLXRoZW1lLXRyYW5zaXRpb24pO1xcclxcbn1cXHJcXG5cXHJcXG4ubGlnaHQgLnN1YnRsZS1ib3JkZXItZ2xvdyB7XFxyXFxuICBib3gtc2hhZG93OiAwIDAgMCAxcHggcmdiYSgwLCAwLCAwLCAwLjA1KTtcXHJcXG59XFxyXFxuXFxyXFxuLyogU3VidGxlIGhvdmVyIGVmZmVjdCAqL1xcclxcbi5zdWJ0bGUtaG92ZXIge1xcclxcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcXHJcXG59XFxyXFxuXFxyXFxuLnN1YnRsZS1ob3Zlcjpob3ZlciB7XFxyXFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpO1xcclxcbn1cXHJcXG5cXHJcXG4ubGlnaHQgLnN1YnRsZS1ob3Zlcjpob3ZlciB7XFxyXFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuMDUpO1xcclxcbn1cXHJcXG5cXHJcXG4vKiBHcmFkaWVudCB0ZXh0ICovXFxyXFxuLmdyYWRpZW50LXRleHQge1xcclxcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCAjZmZmLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNzUpKTtcXHJcXG4gIC13ZWJraXQtYmFja2dyb3VuZC1jbGlwOiB0ZXh0O1xcclxcbiAgLXdlYmtpdC10ZXh0LWZpbGwtY29sb3I6IHRyYW5zcGFyZW50O1xcclxcbiAgYmFja2dyb3VuZC1jbGlwOiB0ZXh0O1xcclxcbiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZCB2YXIoLS10aGVtZS10cmFuc2l0aW9uKTtcXHJcXG59XFxyXFxuXFxyXFxuLmxpZ2h0IC5ncmFkaWVudC10ZXh0IHtcXHJcXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0byByaWdodCwgIzAwMCwgcmdiYSgwLCAwLCAwLCAwLjc1KSk7XFxyXFxuICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcXHJcXG4gIC13ZWJraXQtdGV4dC1maWxsLWNvbG9yOiB0cmFuc3BhcmVudDtcXHJcXG4gIGJhY2tncm91bmQtY2xpcDogdGV4dDtcXHJcXG59XFxyXFxuXFxyXFxuLyogU3VidGxlIGdyaWQgYmFja2dyb3VuZCAqL1xcclxcbi5zdWJ0bGUtZ3JpZCB7XFxyXFxuICBiYWNrZ3JvdW5kLXNpemU6IDMwcHggMzBweDtcXHJcXG4gIHRyYW5zaXRpb246IGJhY2tncm91bmQtaW1hZ2UgdmFyKC0tdGhlbWUtdHJhbnNpdGlvbik7XFxyXFxufVxcclxcblxcclxcbi5saWdodCAuc3VidGxlLWdyaWQge1xcclxcbiAgYmFja2dyb3VuZC1pbWFnZTogbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCByZ2JhKDAsIDAsIDAsIDAuMDUpIDFweCwgdHJhbnNwYXJlbnQgMXB4KSxcXHJcXG4gICAgbGluZWFyLWdyYWRpZW50KHRvIGJvdHRvbSwgcmdiYSgwLCAwLCAwLCAwLjA1KSAxcHgsIHRyYW5zcGFyZW50IDFweCk7XFxyXFxufVxcclxcblxcclxcbi8qIFJldmVhbCBhbmltYXRpb24gY2xhc3NlcyAqL1xcclxcbi5yZXZlYWwge1xcclxcbiAgb3BhY2l0eTogMDtcXHJcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgxMHB4KTtcXHJcXG4gIHRyYW5zaXRpb246IG9wYWNpdHkgMC41cyBlYXNlLCB0cmFuc2Zvcm0gMC41cyBlYXNlO1xcclxcbn1cXHJcXG5cXHJcXG4ucmV2ZWFsLmFjdGl2ZSB7XFxyXFxuICBvcGFjaXR5OiAxO1xcclxcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xcclxcbn1cXHJcXG5cXHJcXG4vKiBCdXR0b24gc2hpbmUgZWZmZWN0ICovXFxyXFxuLmJ0bi1zaGluZSB7XFxyXFxuICBwb3NpdGlvbjogcmVsYXRpdmU7XFxyXFxuICBvdmVyZmxvdzogaGlkZGVuO1xcclxcbn1cXHJcXG5cXHJcXG4uYnRuLXNoaW5lOjphZnRlciB7XFxyXFxuICBjb250ZW50OiBcXFwiXFxcIjtcXHJcXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcXHJcXG4gIHRvcDogMDtcXHJcXG4gIGxlZnQ6IC0xMDAlO1xcclxcbiAgd2lkdGg6IDEwMCU7XFxyXFxuICBoZWlnaHQ6IDEwMCU7XFxyXFxuICBiYWNrZ3JvdW5kLWltYWdlOiBsaW5lYXItZ3JhZGllbnQoXFxyXFxuICAgIDkwZGVnLFxcclxcbiAgICByZ2JhKDI1NSwgMjU1LCAyNTUsIDApIDAlLFxcclxcbiAgICByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpIDUwJSxcXHJcXG4gICAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwKSAxMDAlXFxyXFxuICApO1xcclxcbiAgdHJhbnNpdGlvbjogbGVmdCAwLjVzIGVhc2U7XFxyXFxufVxcclxcblxcclxcbi5idG4tc2hpbmU6aG92ZXI6OmFmdGVyIHtcXHJcXG4gIGxlZnQ6IDEwMCU7XFxyXFxufVxcclxcblxcclxcbi8qIERvdCBwYXR0ZXJuICovXFxyXFxuLmRvdC1wYXR0ZXJuIHtcXHJcXG4gIGJhY2tncm91bmQtaW1hZ2U6IHJhZGlhbC1ncmFkaWVudChyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgMXB4LCB0cmFuc3BhcmVudCAxcHgpO1xcclxcbiAgYmFja2dyb3VuZC1zaXplOiAyMHB4IDIwcHg7XFxyXFxuICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWltYWdlIHZhcigtLXRoZW1lLXRyYW5zaXRpb24pO1xcclxcbn1cXHJcXG5cXHJcXG4ubGlnaHQgLmRvdC1wYXR0ZXJuIHtcXHJcXG4gIGJhY2tncm91bmQtaW1hZ2U6IHJhZGlhbC1ncmFkaWVudChyZ2JhKDAsIDAsIDAsIDAuMSkgMXB4LCB0cmFuc3BhcmVudCAxcHgpO1xcclxcbn1cXHJcXG5cXHJcXG4vKiBGbG93IGJvcmRlciBlZmZlY3QgKi9cXHJcXG4uZmxvdy1ib3JkZXIge1xcclxcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xcclxcbiAgei1pbmRleDogMDtcXHJcXG59XFxyXFxuXFxyXFxuLmZsb3ctYm9yZGVyOjpiZWZvcmUge1xcclxcbiAgY29udGVudDogXFxcIlxcXCI7XFxyXFxuICBwb3NpdGlvbjogYWJzb2x1dGU7XFxyXFxuICB0b3A6IC0ycHg7XFxyXFxuICBsZWZ0OiAtMnB4O1xcclxcbiAgcmlnaHQ6IC0ycHg7XFxyXFxuICBib3R0b206IC0ycHg7XFxyXFxuICBiYWNrZ3JvdW5kOiB2YXIoXFxyXFxuICAgIC0tYmctZmxvdy1ib3JkZXIsXFxyXFxuICAgIGxpbmVhci1ncmFkaWVudChcXHJcXG4gICAgICA5MGRlZyxcXHJcXG4gICAgICB2YXIoLS1ib3JkZXItc3RhcnQtY29sb3IpLFxcclxcbiAgICAgIHZhcigtLWJvcmRlci1taWQtY29sb3IpLFxcclxcbiAgICAgIHZhcigtLWJvcmRlci1lbmQtY29sb3IpLFxcclxcbiAgICAgIHZhcigtLWJvcmRlci1zdGFydC1jb2xvcilcXHJcXG4gICAgKVxcclxcbiAgKTtcXHJcXG4gIGJvcmRlci1yYWRpdXM6IGluaGVyaXQ7XFxyXFxuICB6LWluZGV4OiAtMTtcXHJcXG4gIGJhY2tncm91bmQtc2l6ZTogMzAwJSAxMDAlO1xcclxcbiAgYW5pbWF0aW9uOiBib3JkZXItZmxvdyAzcyBlYXNlIGluZmluaXRlO1xcclxcbiAgb3BhY2l0eTogMDtcXHJcXG4gIHRyYW5zaXRpb246IG9wYWNpdHkgMC4zcyBlYXNlO1xcclxcbn1cXHJcXG5cXHJcXG4uZmxvdy1ib3JkZXI6aG92ZXI6OmJlZm9yZSB7XFxyXFxuICBvcGFjaXR5OiAxO1xcclxcbn1cXHJcXG5cXHJcXG4vKiBHbG93IGVmZmVjdCAqL1xcclxcbi5nbG93LWVmZmVjdCB7XFxyXFxuICB0cmFuc2l0aW9uOiBib3gtc2hhZG93IDAuM3MgZWFzZTtcXHJcXG59XFxyXFxuXFxyXFxuLmdsb3ctZWZmZWN0OmhvdmVyIHtcXHJcXG4gIC0tZ2xvdy1jb2xvcjogdmFyKC0tdGhlbWUtYmx1ZS1yZ2IsIDMzLCAxNTAsIDI0Myk7XFxyXFxuICBib3gtc2hhZG93OiAwIDAgMTBweCAycHggcmdiYSh2YXIoLS1nbG93LWNvbG9yKSwgMC4zKTtcXHJcXG59XFxyXFxuXFxyXFxuLmdsb3ctZWZmZWN0LnB1cnBsZTpob3ZlciB7XFxyXFxuICAtLWdsb3ctY29sb3I6IHZhcigtLXRoZW1lLXB1cnBsZS1yZ2IsIDE1NiwgMzksIDE3Nik7XFxyXFxufVxcclxcblxcclxcbi5nbG93LWVmZmVjdC50ZWFsOmhvdmVyIHtcXHJcXG4gIC0tZ2xvdy1jb2xvcjogdmFyKC0tdGhlbWUtdGVhbC1yZ2IsIDAsIDE4OCwgMjEyKTtcXHJcXG59XFxyXFxuXFxyXFxuLyogVGhlbWUgdHJhbnNpdGlvbiAqL1xcclxcbi50aGVtZS10cmFuc2l0aW9uIHtcXHJcXG4gIHRyYW5zaXRpb246IGFsbCB2YXIoLS10aGVtZS10cmFuc2l0aW9uKTtcXHJcXG59XFxyXFxuXFxyXFxuLyogUGFnZSB0cmFuc2l0aW9uICovXFxyXFxuLnBhZ2UtdHJhbnNpdGlvbi1lbnRlciB7XFxyXFxuICBvcGFjaXR5OiAwO1xcclxcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDEwcHgpO1xcclxcbn1cXHJcXG5cXHJcXG4ucGFnZS10cmFuc2l0aW9uLWVudGVyLWFjdGl2ZSB7XFxyXFxuICBvcGFjaXR5OiAxO1xcclxcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xcclxcbiAgdHJhbnNpdGlvbjogb3BhY2l0eSAwLjNzLCB0cmFuc2Zvcm0gMC4zcztcXHJcXG59XFxyXFxuXFxyXFxuLnBhZ2UtdHJhbnNpdGlvbi1leGl0IHtcXHJcXG4gIG9wYWNpdHk6IDE7XFxyXFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XFxyXFxufVxcclxcblxcclxcbi5wYWdlLXRyYW5zaXRpb24tZXhpdC1hY3RpdmUge1xcclxcbiAgb3BhY2l0eTogMDtcXHJcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMTBweCk7XFxyXFxuICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuM3MsIHRyYW5zZm9ybSAwLjNzO1xcclxcbn1cXHJcXG5cXHJcXG4vKiBOYXYgbGluayB3aXRoIGluZGljYXRvciAqL1xcclxcbi5uYXYtbGluayB7XFxyXFxuICBwb3NpdGlvbjogcmVsYXRpdmU7XFxyXFxufVxcclxcblxcclxcbi5uYXYtbGluazo6YWZ0ZXIge1xcclxcbiAgY29udGVudDogXFxcIlxcXCI7XFxyXFxuICBwb3NpdGlvbjogYWJzb2x1dGU7XFxyXFxuICBib3R0b206IC00cHg7XFxyXFxuICBsZWZ0OiAwO1xcclxcbiAgd2lkdGg6IDA7XFxyXFxuICBoZWlnaHQ6IDJweDtcXHJcXG4gIGJhY2tncm91bmQtY29sb3I6IGhzbCh2YXIoLS10aGVtZS1ibHVlKSk7XFxyXFxuICB0cmFuc2l0aW9uOiB3aWR0aCAwLjNzIGVhc2U7XFxyXFxufVxcclxcblxcclxcbi5uYXYtbGluazpob3Zlcjo6YWZ0ZXIsXFxyXFxuLm5hdi1saW5rLmFjdGl2ZTo6YWZ0ZXIge1xcclxcbiAgd2lkdGg6IDEwMCU7XFxyXFxufVxcclxcblxcclxcbi8qIEN1c3RvbSBzY3JvbGxiYXIgKi9cXHJcXG46Oi13ZWJraXQtc2Nyb2xsYmFyIHtcXHJcXG4gIHdpZHRoOiA4cHg7XFxyXFxuICBoZWlnaHQ6IDhweDtcXHJcXG59XFxyXFxuXFxyXFxuOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XFxyXFxuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcXHJcXG59XFxyXFxuXFxyXFxuOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XFxyXFxuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XFxyXFxuICBib3JkZXItcmFkaXVzOiA0cHg7XFxyXFxufVxcclxcblxcclxcbjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIge1xcclxcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xcclxcbn1cXHJcXG5cXHJcXG4ubGlnaHQgOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7XFxyXFxuICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMSk7XFxyXFxufVxcclxcblxcclxcbi5saWdodCA6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHtcXHJcXG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4yKTtcXHJcXG59XFxyXFxuXFxyXFxuLyogQW5pbWF0aW9uIGZvciBsb2FkaW5nIHNwaW5uZXIgKi9cXHJcXG5Aa2V5ZnJhbWVzIGJvcmRlci1mbG93IHtcXHJcXG4gIDAlIHtcXHJcXG4gICAgYmFja2dyb3VuZC1wb3NpdGlvbjogMCUgNTAlO1xcclxcbiAgfVxcclxcbiAgNTAlIHtcXHJcXG4gICAgYmFja2dyb3VuZC1wb3NpdGlvbjogMTAwJSA1MCU7XFxyXFxuICB9XFxyXFxuICAxMDAlIHtcXHJcXG4gICAgYmFja2dyb3VuZC1wb3NpdGlvbjogMCUgNTAlO1xcclxcbiAgfVxcclxcbn1cXHJcXG5cXHJcXG5Aa2V5ZnJhbWVzIHN1YnRsZS1wdWxzZSB7XFxyXFxuICAwJSB7XFxyXFxuICAgIG9wYWNpdHk6IDAuNztcXHJcXG4gIH1cXHJcXG4gIDUwJSB7XFxyXFxuICAgIG9wYWNpdHk6IDE7XFxyXFxuICB9XFxyXFxuICAxMDAlIHtcXHJcXG4gICAgb3BhY2l0eTogMC43O1xcclxcbiAgfVxcclxcbn1cXHJcXG5cXHJcXG4uYW5pbWF0ZS1zdWJ0bGUtcHVsc2Uge1xcclxcbiAgYW5pbWF0aW9uOiBzdWJ0bGUtcHVsc2UgMnMgZWFzZS1pbi1vdXQgaW5maW5pdGU7XFxyXFxufVxcclxcblxcclxcbi5hbmltYXRlLXJvdGF0ZS1sb2FkZXIge1xcclxcbiAgYW5pbWF0aW9uOiBzcGluIDFzIGxpbmVhciBpbmZpbml0ZTtcXHJcXG59XFxyXFxuXFxyXFxuQGtleWZyYW1lcyBzcGluIHtcXHJcXG4gIGZyb20ge1xcclxcbiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTtcXHJcXG4gIH1cXHJcXG4gIHRvIHtcXHJcXG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTtcXHJcXG4gIH1cXHJcXG59XFxyXFxuXFxyXFxuLnBsYWNlaG9sZGVyXFxcXDp0ZXh0LW11dGVkLWZvcmVncm91bmQ6Oi1tb3otcGxhY2Vob2xkZXIge1xcbiAgY29sb3I6IGhzbCh2YXIoLS1tdXRlZC1mb3JlZ3JvdW5kKSk7XFxufVxcclxcblxcclxcbi5wbGFjZWhvbGRlclxcXFw6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kOjpwbGFjZWhvbGRlciB7XFxuICBjb2xvcjogaHNsKHZhcigtLW11dGVkLWZvcmVncm91bmQpKTtcXG59XFxyXFxuXFxyXFxuLmhvdmVyXFxcXDpzY2FsZS0xMDU6aG92ZXIge1xcbiAgLS10dy1zY2FsZS14OiAxLjA1O1xcbiAgLS10dy1zY2FsZS15OiAxLjA1O1xcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUodmFyKC0tdHctdHJhbnNsYXRlLXgpLCB2YXIoLS10dy10cmFuc2xhdGUteSkpIHJvdGF0ZSh2YXIoLS10dy1yb3RhdGUpKSBza2V3WCh2YXIoLS10dy1za2V3LXgpKSBza2V3WSh2YXIoLS10dy1za2V3LXkpKSBzY2FsZVgodmFyKC0tdHctc2NhbGUteCkpIHNjYWxlWSh2YXIoLS10dy1zY2FsZS15KSk7XFxufVxcclxcblxcclxcbi5ob3ZlclxcXFw6Ym9yZGVyLWJsYWNrXFxcXC8yMDpob3ZlciB7XFxuICBib3JkZXItY29sb3I6IHJnYigwIDAgMCAvIDAuMik7XFxufVxcclxcblxcclxcbi5ob3ZlclxcXFw6Ym9yZGVyLWJsYWNrXFxcXC83MDpob3ZlciB7XFxuICBib3JkZXItY29sb3I6IHJnYigwIDAgMCAvIDAuNyk7XFxufVxcclxcblxcclxcbi5ob3ZlclxcXFw6Ym9yZGVyLWdyYXktNDAwOmhvdmVyIHtcXG4gIC0tdHctYm9yZGVyLW9wYWNpdHk6IDE7XFxuICBib3JkZXItY29sb3I6IHJnYigxNTYgMTYzIDE3NSAvIHZhcigtLXR3LWJvcmRlci1vcGFjaXR5LCAxKSk7XFxufVxcclxcblxcclxcbi5ob3ZlclxcXFw6Ym9yZGVyLXRoZW1lLWJsdWVcXFxcLzUwOmhvdmVyIHtcXG4gIGJvcmRlci1jb2xvcjogaHNsKHZhcigtLXRoZW1lLWJsdWUpIC8gMC41KTtcXG59XFxyXFxuXFxyXFxuLmhvdmVyXFxcXDpib3JkZXItd2hpdGVcXFxcLzIwOmhvdmVyIHtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gMC4yKTtcXG59XFxyXFxuXFxyXFxuLmhvdmVyXFxcXDpib3JkZXItd2hpdGVcXFxcLzMwOmhvdmVyIHtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gMC4zKTtcXG59XFxyXFxuXFxyXFxuLmhvdmVyXFxcXDpiZy1hY2NlbnQ6aG92ZXIge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogaHNsKHZhcigtLWFjY2VudCkpO1xcbn1cXHJcXG5cXHJcXG4uaG92ZXJcXFxcOmJnLWJsYWNrXFxcXC81OmhvdmVyIHtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigwIDAgMCAvIDAuMDUpO1xcbn1cXHJcXG5cXHJcXG4uaG92ZXJcXFxcOmJnLWRlc3RydWN0aXZlXFxcXC8xMDpob3ZlciB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBoc2wodmFyKC0tZGVzdHJ1Y3RpdmUpIC8gMC4xKTtcXG59XFxyXFxuXFxyXFxuLmhvdmVyXFxcXDpiZy1kZXN0cnVjdGl2ZVxcXFwvOTA6aG92ZXIge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogaHNsKHZhcigtLWRlc3RydWN0aXZlKSAvIDAuOSk7XFxufVxcclxcblxcclxcbi5ob3ZlclxcXFw6YmctZ3JheS01MDpob3ZlciB7XFxuICAtLXR3LWJnLW9wYWNpdHk6IDE7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjQ5IDI1MCAyNTEgLyB2YXIoLS10dy1iZy1vcGFjaXR5LCAxKSk7XFxufVxcclxcblxcclxcbi5ob3ZlclxcXFw6YmctcHJpbWFyeVxcXFwvMTA6aG92ZXIge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogaHNsKHZhcigtLXByaW1hcnkpIC8gMC4xKTtcXG59XFxyXFxuXFxyXFxuLmhvdmVyXFxcXDpiZy1wcmltYXJ5XFxcXC85MDpob3ZlciB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBoc2wodmFyKC0tcHJpbWFyeSkgLyAwLjkpO1xcbn1cXHJcXG5cXHJcXG4uaG92ZXJcXFxcOmJnLXNlY29uZGFyeVxcXFwvODA6aG92ZXIge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogaHNsKHZhcigtLXNlY29uZGFyeSkgLyAwLjgpO1xcbn1cXHJcXG5cXHJcXG4uaG92ZXJcXFxcOmJnLXRoZW1lLWJsdWVcXFxcLzEwOmhvdmVyIHtcXG4gIGJhY2tncm91bmQtY29sb3I6IGhzbCh2YXIoLS10aGVtZS1ibHVlKSAvIDAuMSk7XFxufVxcclxcblxcclxcbi5ob3ZlclxcXFw6YmctdGhlbWUtcHVycGxlXFxcXC85MDpob3ZlciB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBoc2wodmFyKC0tdGhlbWUtcHVycGxlKSAvIDAuOSk7XFxufVxcclxcblxcclxcbi5ob3ZlclxcXFw6Ymctd2hpdGVcXFxcLzU6aG92ZXIge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gMC4wNSk7XFxufVxcclxcblxcclxcbi5ob3ZlclxcXFw6Ymctd2hpdGVcXFxcLzkwOmhvdmVyIHtcXG4gIGJhY2tncm91bmQtY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIDAuOSk7XFxufVxcclxcblxcclxcbi5ob3ZlclxcXFw6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZDpob3ZlciB7XFxuICBjb2xvcjogaHNsKHZhcigtLWFjY2VudC1mb3JlZ3JvdW5kKSk7XFxufVxcclxcblxcclxcbi5ob3ZlclxcXFw6dGV4dC1ibGFjazpob3ZlciB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMCAwIDAgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxyXFxuXFxyXFxuLmhvdmVyXFxcXDp0ZXh0LWZvcmVncm91bmQ6aG92ZXIge1xcbiAgY29sb3I6IGhzbCh2YXIoLS1mb3JlZ3JvdW5kKSk7XFxufVxcclxcblxcclxcbi5ob3ZlclxcXFw6dGV4dC1wcmltYXJ5XFxcXC84MDpob3ZlciB7XFxuICBjb2xvcjogaHNsKHZhcigtLXByaW1hcnkpIC8gMC44KTtcXG59XFxyXFxuXFxyXFxuLmhvdmVyXFxcXDp0ZXh0LXRoZW1lLWJsdWU6aG92ZXIge1xcbiAgY29sb3I6IGhzbCh2YXIoLS10aGVtZS1ibHVlKSk7XFxufVxcclxcblxcclxcbi5ob3ZlclxcXFw6dGV4dC13aGl0ZTpob3ZlciB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxyXFxuXFxyXFxuLmhvdmVyXFxcXDp1bmRlcmxpbmU6aG92ZXIge1xcbiAgdGV4dC1kZWNvcmF0aW9uLWxpbmU6IHVuZGVybGluZTtcXG59XFxyXFxuXFxyXFxuLmhvdmVyXFxcXDpvcGFjaXR5LTEwMDpob3ZlciB7XFxuICBvcGFjaXR5OiAxO1xcbn1cXHJcXG5cXHJcXG4uaG92ZXJcXFxcOm9wYWNpdHktOTA6aG92ZXIge1xcbiAgb3BhY2l0eTogMC45O1xcbn1cXHJcXG5cXHJcXG4uaG92ZXJcXFxcOnNoYWRvdy1sZzpob3ZlciB7XFxuICAtLXR3LXNoYWRvdzogMCAxMHB4IDE1cHggLTNweCByZ2IoMCAwIDAgLyAwLjEpLCAwIDRweCA2cHggLTRweCByZ2IoMCAwIDAgLyAwLjEpO1xcbiAgLS10dy1zaGFkb3ctY29sb3JlZDogMCAxMHB4IDE1cHggLTNweCB2YXIoLS10dy1zaGFkb3ctY29sb3IpLCAwIDRweCA2cHggLTRweCB2YXIoLS10dy1zaGFkb3ctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1yaW5nLXNoYWRvdywgMCAwICMwMDAwKSwgdmFyKC0tdHctc2hhZG93KTtcXG59XFxyXFxuXFxyXFxuLmZvY3VzXFxcXDpib3JkZXItcHJpbWFyeTpmb2N1cyB7XFxuICBib3JkZXItY29sb3I6IGhzbCh2YXIoLS1wcmltYXJ5KSk7XFxufVxcclxcblxcclxcbi5mb2N1c1xcXFw6Ym9yZGVyLXRoZW1lLWJsdWU6Zm9jdXMge1xcbiAgYm9yZGVyLWNvbG9yOiBoc2wodmFyKC0tdGhlbWUtYmx1ZSkpO1xcbn1cXHJcXG5cXHJcXG4uZm9jdXNcXFxcOmJnLWFjY2VudDpmb2N1cyB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBoc2wodmFyKC0tYWNjZW50KSk7XFxufVxcclxcblxcclxcbi5mb2N1c1xcXFw6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZDpmb2N1cyB7XFxuICBjb2xvcjogaHNsKHZhcigtLWFjY2VudC1mb3JlZ3JvdW5kKSk7XFxufVxcclxcblxcclxcbi5mb2N1c1xcXFw6b3V0bGluZS1ub25lOmZvY3VzIHtcXG4gIG91dGxpbmU6IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcXG4gIG91dGxpbmUtb2Zmc2V0OiAycHg7XFxufVxcclxcblxcclxcbi5mb2N1c1xcXFw6cmluZy0yOmZvY3VzIHtcXG4gIC0tdHctcmluZy1vZmZzZXQtc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkgdmFyKC0tdHctcmluZy1vZmZzZXQtY29sb3IpO1xcbiAgLS10dy1yaW5nLXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgY2FsYygycHggKyB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkpIHZhcigtLXR3LXJpbmctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93KSwgdmFyKC0tdHctcmluZy1zaGFkb3cpLCB2YXIoLS10dy1zaGFkb3csIDAgMCAjMDAwMCk7XFxufVxcclxcblxcclxcbi5mb2N1c1xcXFw6cmluZy1wcmltYXJ5OmZvY3VzIHtcXG4gIC0tdHctcmluZy1jb2xvcjogaHNsKHZhcigtLXByaW1hcnkpKTtcXG59XFxyXFxuXFxyXFxuLmZvY3VzXFxcXDpyaW5nLXJpbmc6Zm9jdXMge1xcbiAgLS10dy1yaW5nLWNvbG9yOiBoc2wodmFyKC0tcmluZykpO1xcbn1cXHJcXG5cXHJcXG4uZm9jdXNcXFxcOnJpbmctdGhlbWUtYmx1ZVxcXFwvNTA6Zm9jdXMge1xcbiAgLS10dy1yaW5nLWNvbG9yOiBoc2wodmFyKC0tdGhlbWUtYmx1ZSkgLyAwLjUpO1xcbn1cXHJcXG5cXHJcXG4uZm9jdXNcXFxcOnJpbmctb2Zmc2V0LTI6Zm9jdXMge1xcbiAgLS10dy1yaW5nLW9mZnNldC13aWR0aDogMnB4O1xcbn1cXHJcXG5cXHJcXG4uZm9jdXMtdmlzaWJsZVxcXFw6b3V0bGluZS1ub25lOmZvY3VzLXZpc2libGUge1xcbiAgb3V0bGluZTogMnB4IHNvbGlkIHRyYW5zcGFyZW50O1xcbiAgb3V0bGluZS1vZmZzZXQ6IDJweDtcXG59XFxyXFxuXFxyXFxuLmZvY3VzLXZpc2libGVcXFxcOnJpbmctMjpmb2N1cy12aXNpYmxlIHtcXG4gIC0tdHctcmluZy1vZmZzZXQtc2hhZG93OiB2YXIoLS10dy1yaW5nLWluc2V0KSAwIDAgMCB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkgdmFyKC0tdHctcmluZy1vZmZzZXQtY29sb3IpO1xcbiAgLS10dy1yaW5nLXNoYWRvdzogdmFyKC0tdHctcmluZy1pbnNldCkgMCAwIDAgY2FsYygycHggKyB2YXIoLS10dy1yaW5nLW9mZnNldC13aWR0aCkpIHZhcigtLXR3LXJpbmctY29sb3IpO1xcbiAgYm94LXNoYWRvdzogdmFyKC0tdHctcmluZy1vZmZzZXQtc2hhZG93KSwgdmFyKC0tdHctcmluZy1zaGFkb3cpLCB2YXIoLS10dy1zaGFkb3csIDAgMCAjMDAwMCk7XFxufVxcclxcblxcclxcbi5mb2N1cy12aXNpYmxlXFxcXDpyaW5nLXJpbmc6Zm9jdXMtdmlzaWJsZSB7XFxuICAtLXR3LXJpbmctY29sb3I6IGhzbCh2YXIoLS1yaW5nKSk7XFxufVxcclxcblxcclxcbi5mb2N1cy12aXNpYmxlXFxcXDpyaW5nLW9mZnNldC0yOmZvY3VzLXZpc2libGUge1xcbiAgLS10dy1yaW5nLW9mZnNldC13aWR0aDogMnB4O1xcbn1cXHJcXG5cXHJcXG4uZGlzYWJsZWRcXFxcOnBvaW50ZXItZXZlbnRzLW5vbmU6ZGlzYWJsZWQge1xcbiAgcG9pbnRlci1ldmVudHM6IG5vbmU7XFxufVxcclxcblxcclxcbi5kaXNhYmxlZFxcXFw6Y3Vyc29yLW5vdC1hbGxvd2VkOmRpc2FibGVkIHtcXG4gIGN1cnNvcjogbm90LWFsbG93ZWQ7XFxufVxcclxcblxcclxcbi5kaXNhYmxlZFxcXFw6b3BhY2l0eS01MDpkaXNhYmxlZCB7XFxuICBvcGFjaXR5OiAwLjU7XFxufVxcclxcblxcclxcbi5ncm91cDpob3ZlciAuZ3JvdXAtaG92ZXJcXFxcOm9wYWNpdHktMTAwIHtcXG4gIG9wYWNpdHk6IDE7XFxufVxcclxcblxcclxcbi5wZWVyOmRpc2FibGVkIH4gLnBlZXItZGlzYWJsZWRcXFxcOmN1cnNvci1ub3QtYWxsb3dlZCB7XFxuICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xcbn1cXHJcXG5cXHJcXG4ucGVlcjpkaXNhYmxlZCB+IC5wZWVyLWRpc2FibGVkXFxcXDpvcGFjaXR5LTcwIHtcXG4gIG9wYWNpdHk6IDAuNztcXG59XFxyXFxuXFxyXFxuLmRhdGEtXFxcXFtkaXNhYmxlZFxcXFxdXFxcXDpwb2ludGVyLWV2ZW50cy1ub25lW2RhdGEtZGlzYWJsZWRdIHtcXG4gIHBvaW50ZXItZXZlbnRzOiBub25lO1xcbn1cXHJcXG5cXHJcXG4uZGF0YS1cXFxcW3N0YXRlXFxcXD1hY3RpdmVcXFxcXVxcXFw6YmctYmFja2dyb3VuZFtkYXRhLXN0YXRlPVxcXCJhY3RpdmVcXFwiXSB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBoc2wodmFyKC0tYmFja2dyb3VuZCkpO1xcbn1cXHJcXG5cXHJcXG4uZGF0YS1cXFxcW3N0YXRlXFxcXD1hY3RpdmVcXFxcXVxcXFw6YmctdGhlbWUtYmx1ZVtkYXRhLXN0YXRlPVxcXCJhY3RpdmVcXFwiXSB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBoc2wodmFyKC0tdGhlbWUtYmx1ZSkpO1xcbn1cXHJcXG5cXHJcXG4uZGF0YS1cXFxcW3N0YXRlXFxcXD1vcGVuXFxcXF1cXFxcOmJnLWFjY2VudFtkYXRhLXN0YXRlPVxcXCJvcGVuXFxcIl0ge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogaHNsKHZhcigtLWFjY2VudCkpO1xcbn1cXHJcXG5cXHJcXG4uZGF0YS1cXFxcW3N0YXRlXFxcXD1hY3RpdmVcXFxcXVxcXFw6dGV4dC1mb3JlZ3JvdW5kW2RhdGEtc3RhdGU9XFxcImFjdGl2ZVxcXCJdIHtcXG4gIGNvbG9yOiBoc2wodmFyKC0tZm9yZWdyb3VuZCkpO1xcbn1cXHJcXG5cXHJcXG4uZGF0YS1cXFxcW3N0YXRlXFxcXD1hY3RpdmVcXFxcXVxcXFw6dGV4dC13aGl0ZVtkYXRhLXN0YXRlPVxcXCJhY3RpdmVcXFwiXSB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxyXFxuXFxyXFxuLmRhdGEtXFxcXFtzdGF0ZVxcXFw9b3BlblxcXFxdXFxcXDp0ZXh0LW11dGVkLWZvcmVncm91bmRbZGF0YS1zdGF0ZT1cXFwib3BlblxcXCJdIHtcXG4gIGNvbG9yOiBoc2wodmFyKC0tbXV0ZWQtZm9yZWdyb3VuZCkpO1xcbn1cXHJcXG5cXHJcXG4uZGF0YS1cXFxcW2Rpc2FibGVkXFxcXF1cXFxcOm9wYWNpdHktNTBbZGF0YS1kaXNhYmxlZF0ge1xcbiAgb3BhY2l0eTogMC41O1xcbn1cXHJcXG5cXHJcXG4uZGF0YS1cXFxcW3N0YXRlXFxcXD1hY3RpdmVcXFxcXVxcXFw6c2hhZG93LXNtW2RhdGEtc3RhdGU9XFxcImFjdGl2ZVxcXCJdIHtcXG4gIC0tdHctc2hhZG93OiAwIDFweCAycHggMCByZ2IoMCAwIDAgLyAwLjA1KTtcXG4gIC0tdHctc2hhZG93LWNvbG9yZWQ6IDAgMXB4IDJweCAwIHZhcigtLXR3LXNoYWRvdy1jb2xvcik7XFxuICBib3gtc2hhZG93OiB2YXIoLS10dy1yaW5nLW9mZnNldC1zaGFkb3csIDAgMCAjMDAwMCksIHZhcigtLXR3LXJpbmctc2hhZG93LCAwIDAgIzAwMDApLCB2YXIoLS10dy1zaGFkb3cpO1xcbn1cXHJcXG5cXHJcXG4uZGF0YS1cXFxcW3N0YXRlXFxcXD1vcGVuXFxcXF1cXFxcOmFuaW1hdGUtaW5bZGF0YS1zdGF0ZT1cXFwib3BlblxcXCJdIHtcXG4gIGFuaW1hdGlvbi1uYW1lOiBlbnRlcjtcXG4gIGFuaW1hdGlvbi1kdXJhdGlvbjogMTUwbXM7XFxuICAtLXR3LWVudGVyLW9wYWNpdHk6IGluaXRpYWw7XFxuICAtLXR3LWVudGVyLXNjYWxlOiBpbml0aWFsO1xcbiAgLS10dy1lbnRlci1yb3RhdGU6IGluaXRpYWw7XFxuICAtLXR3LWVudGVyLXRyYW5zbGF0ZS14OiBpbml0aWFsO1xcbiAgLS10dy1lbnRlci10cmFuc2xhdGUteTogaW5pdGlhbDtcXG59XFxyXFxuXFxyXFxuLmRhdGEtXFxcXFtzdGF0ZVxcXFw9Y2xvc2VkXFxcXF1cXFxcOmFuaW1hdGUtb3V0W2RhdGEtc3RhdGU9XFxcImNsb3NlZFxcXCJdIHtcXG4gIGFuaW1hdGlvbi1uYW1lOiBleGl0O1xcbiAgYW5pbWF0aW9uLWR1cmF0aW9uOiAxNTBtcztcXG4gIC0tdHctZXhpdC1vcGFjaXR5OiBpbml0aWFsO1xcbiAgLS10dy1leGl0LXNjYWxlOiBpbml0aWFsO1xcbiAgLS10dy1leGl0LXJvdGF0ZTogaW5pdGlhbDtcXG4gIC0tdHctZXhpdC10cmFuc2xhdGUteDogaW5pdGlhbDtcXG4gIC0tdHctZXhpdC10cmFuc2xhdGUteTogaW5pdGlhbDtcXG59XFxyXFxuXFxyXFxuLmRhdGEtXFxcXFtzdGF0ZVxcXFw9Y2xvc2VkXFxcXF1cXFxcOmZhZGUtb3V0LTBbZGF0YS1zdGF0ZT1cXFwiY2xvc2VkXFxcIl0ge1xcbiAgLS10dy1leGl0LW9wYWNpdHk6IDA7XFxufVxcclxcblxcclxcbi5kYXRhLVxcXFxbc3RhdGVcXFxcPW9wZW5cXFxcXVxcXFw6ZmFkZS1pbi0wW2RhdGEtc3RhdGU9XFxcIm9wZW5cXFwiXSB7XFxuICAtLXR3LWVudGVyLW9wYWNpdHk6IDA7XFxufVxcclxcblxcclxcbi5kYXRhLVxcXFxbc3RhdGVcXFxcPWNsb3NlZFxcXFxdXFxcXDp6b29tLW91dC05NVtkYXRhLXN0YXRlPVxcXCJjbG9zZWRcXFwiXSB7XFxuICAtLXR3LWV4aXQtc2NhbGU6IC45NTtcXG59XFxyXFxuXFxyXFxuLmRhdGEtXFxcXFtzdGF0ZVxcXFw9b3BlblxcXFxdXFxcXDp6b29tLWluLTk1W2RhdGEtc3RhdGU9XFxcIm9wZW5cXFwiXSB7XFxuICAtLXR3LWVudGVyLXNjYWxlOiAuOTU7XFxufVxcclxcblxcclxcbi5kYXRhLVxcXFxbc3RhdGVcXFxcPWNsb3NlZFxcXFxdXFxcXDpzbGlkZS1vdXQtdG8tbGVmdC0xXFxcXC8yW2RhdGEtc3RhdGU9XFxcImNsb3NlZFxcXCJdIHtcXG4gIC0tdHctZXhpdC10cmFuc2xhdGUteDogLTUwJTtcXG59XFxyXFxuXFxyXFxuLmRhdGEtXFxcXFtzdGF0ZVxcXFw9Y2xvc2VkXFxcXF1cXFxcOnNsaWRlLW91dC10by10b3AtXFxcXFs0OFxcXFwlXFxcXF1bZGF0YS1zdGF0ZT1cXFwiY2xvc2VkXFxcIl0ge1xcbiAgLS10dy1leGl0LXRyYW5zbGF0ZS15OiAtNDglO1xcbn1cXHJcXG5cXHJcXG4uZGF0YS1cXFxcW3N0YXRlXFxcXD1vcGVuXFxcXF1cXFxcOnNsaWRlLWluLWZyb20tbGVmdC0xXFxcXC8yW2RhdGEtc3RhdGU9XFxcIm9wZW5cXFwiXSB7XFxuICAtLXR3LWVudGVyLXRyYW5zbGF0ZS14OiAtNTAlO1xcbn1cXHJcXG5cXHJcXG4uZGF0YS1cXFxcW3N0YXRlXFxcXD1vcGVuXFxcXF1cXFxcOnNsaWRlLWluLWZyb20tdG9wLVxcXFxbNDhcXFxcJVxcXFxdW2RhdGEtc3RhdGU9XFxcIm9wZW5cXFwiXSB7XFxuICAtLXR3LWVudGVyLXRyYW5zbGF0ZS15OiAtNDglO1xcbn1cXHJcXG5cXHJcXG4uZGFya1xcXFw6Ym9yZGVyLWRlc3RydWN0aXZlOmlzKC5kYXJrICopIHtcXG4gIGJvcmRlci1jb2xvcjogaHNsKHZhcigtLWRlc3RydWN0aXZlKSk7XFxufVxcclxcblxcclxcbi5kYXJrXFxcXDpib3JkZXItd2hpdGVcXFxcLzIwOmlzKC5kYXJrICopIHtcXG4gIGJvcmRlci1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gMC4yKTtcXG59XFxyXFxuXFxyXFxuLmRhcmtcXFxcOnRleHQtd2hpdGU6aXMoLmRhcmsgKikge1xcbiAgLS10dy10ZXh0LW9wYWNpdHk6IDE7XFxuICBjb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gdmFyKC0tdHctdGV4dC1vcGFjaXR5LCAxKSk7XFxufVxcclxcblxcclxcbi5kYXJrXFxcXDpob3ZlclxcXFw6Ym9yZGVyLXdoaXRlXFxcXC8yMDpob3ZlcjppcyguZGFyayAqKSB7XFxuICBib3JkZXItY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIDAuMik7XFxufVxcclxcblxcclxcbi5kYXJrXFxcXDpob3ZlclxcXFw6Ym9yZGVyLXdoaXRlXFxcXC80MDpob3ZlcjppcyguZGFyayAqKSB7XFxuICBib3JkZXItY29sb3I6IHJnYigyNTUgMjU1IDI1NSAvIDAuNCk7XFxufVxcclxcblxcclxcbi5kYXJrXFxcXDpob3ZlclxcXFw6Ymctd2hpdGVcXFxcLzU6aG92ZXI6aXMoLmRhcmsgKikge1xcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI1NSAyNTUgMjU1IC8gMC4wNSk7XFxufVxcclxcblxcclxcbi5kYXJrXFxcXDpob3ZlclxcXFw6dGV4dC13aGl0ZTpob3ZlcjppcyguZGFyayAqKSB7XFxuICAtLXR3LXRleHQtb3BhY2l0eTogMTtcXG4gIGNvbG9yOiByZ2IoMjU1IDI1NSAyNTUgLyB2YXIoLS10dy10ZXh0LW9wYWNpdHksIDEpKTtcXG59XFxyXFxuXFxyXFxuQG1lZGlhIChtaW4td2lkdGg6IDY0MHB4KSB7XFxuXFxuICAuc21cXFxcOmNvbC1zcGFuLTIge1xcbiAgICBncmlkLWNvbHVtbjogc3BhbiAyIC8gc3BhbiAyO1xcbiAgfVxcblxcbiAgLnNtXFxcXDptYXgtdy1cXFxcWzYwMHB4XFxcXF0ge1xcbiAgICBtYXgtd2lkdGg6IDYwMHB4O1xcbiAgfVxcblxcbiAgLnNtXFxcXDpncmlkLWNvbHMtMiB7XFxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDIsIG1pbm1heCgwLCAxZnIpKTtcXG4gIH1cXG5cXG4gIC5zbVxcXFw6ZmxleC1yb3cge1xcbiAgICBmbGV4LWRpcmVjdGlvbjogcm93O1xcbiAgfVxcblxcbiAgLnNtXFxcXDpqdXN0aWZ5LWVuZCB7XFxuICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7XFxuICB9XFxuXFxuICAuc21cXFxcOnNwYWNlLXgtMiA+IDpub3QoW2hpZGRlbl0pIH4gOm5vdChbaGlkZGVuXSkge1xcbiAgICAtLXR3LXNwYWNlLXgtcmV2ZXJzZTogMDtcXG4gICAgbWFyZ2luLXJpZ2h0OiBjYWxjKDAuNXJlbSAqIHZhcigtLXR3LXNwYWNlLXgtcmV2ZXJzZSkpO1xcbiAgICBtYXJnaW4tbGVmdDogY2FsYygwLjVyZW0gKiBjYWxjKDEgLSB2YXIoLS10dy1zcGFjZS14LXJldmVyc2UpKSk7XFxuICB9XFxuXFxuICAuc21cXFxcOnJvdW5kZWQtbGcge1xcbiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMpO1xcbiAgfVxcblxcbiAgLnNtXFxcXDp0ZXh0LWxlZnQge1xcbiAgICB0ZXh0LWFsaWduOiBsZWZ0O1xcbiAgfVxcbn1cXHJcXG5cXHJcXG5AbWVkaWEgKG1pbi13aWR0aDogNzY4cHgpIHtcXG5cXG4gIC5tZFxcXFw6bWItMCB7XFxuICAgIG1hcmdpbi1ib3R0b206IDBweDtcXG4gIH1cXG5cXG4gIC5tZFxcXFw6bXQtXFxcXFstMTgwcHhcXFxcXSB7XFxuICAgIG1hcmdpbi10b3A6IC0xODBweDtcXG4gIH1cXG5cXG4gIC5tZFxcXFw6bXQtMCB7XFxuICAgIG1hcmdpbi10b3A6IDBweDtcXG4gIH1cXG5cXG4gIC5tZFxcXFw6bXQtNiB7XFxuICAgIG1hcmdpbi10b3A6IDEuNXJlbTtcXG4gIH1cXG5cXG4gIC5tZFxcXFw6ZmxleCB7XFxuICAgIGRpc3BsYXk6IGZsZXg7XFxuICB9XFxuXFxuICAubWRcXFxcOmhpZGRlbiB7XFxuICAgIGRpc3BsYXk6IG5vbmU7XFxuICB9XFxuXFxuICAubWRcXFxcOmdyaWQtY29scy0yIHtcXG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMiwgbWlubWF4KDAsIDFmcikpO1xcbiAgfVxcblxcbiAgLm1kXFxcXDpncmlkLWNvbHMtMyB7XFxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDMsIG1pbm1heCgwLCAxZnIpKTtcXG4gIH1cXG5cXG4gIC5tZFxcXFw6ZmxleC1yb3cge1xcbiAgICBmbGV4LWRpcmVjdGlvbjogcm93O1xcbiAgfVxcblxcbiAgLm1kXFxcXDpnYXAtOCB7XFxuICAgIGdhcDogMnJlbTtcXG4gIH1cXG5cXG4gIC5tZFxcXFw6cC02IHtcXG4gICAgcGFkZGluZzogMS41cmVtO1xcbiAgfVxcblxcbiAgLm1kXFxcXDpweS0xMiB7XFxuICAgIHBhZGRpbmctdG9wOiAzcmVtO1xcbiAgICBwYWRkaW5nLWJvdHRvbTogM3JlbTtcXG4gIH1cXG5cXG4gIC5tZFxcXFw6cHktMTYge1xcbiAgICBwYWRkaW5nLXRvcDogNHJlbTtcXG4gICAgcGFkZGluZy1ib3R0b206IDRyZW07XFxuICB9XFxuXFxuICAubWRcXFxcOnB5LTI0IHtcXG4gICAgcGFkZGluZy10b3A6IDZyZW07XFxuICAgIHBhZGRpbmctYm90dG9tOiA2cmVtO1xcbiAgfVxcblxcbiAgLm1kXFxcXDpweS0zMiB7XFxuICAgIHBhZGRpbmctdG9wOiA4cmVtO1xcbiAgICBwYWRkaW5nLWJvdHRvbTogOHJlbTtcXG4gIH1cXG5cXG4gIC5tZFxcXFw6dGV4dC0yeGwge1xcbiAgICBmb250LXNpemU6IDEuNXJlbTtcXG4gICAgbGluZS1oZWlnaHQ6IDJyZW07XFxuICB9XFxuXFxuICAubWRcXFxcOnRleHQtM3hsIHtcXG4gICAgZm9udC1zaXplOiAxLjg3NXJlbTtcXG4gICAgbGluZS1oZWlnaHQ6IDIuMjVyZW07XFxuICB9XFxuXFxuICAubWRcXFxcOnRleHQtNHhsIHtcXG4gICAgZm9udC1zaXplOiAyLjI1cmVtO1xcbiAgICBsaW5lLWhlaWdodDogMi41cmVtO1xcbiAgfVxcblxcbiAgLm1kXFxcXDp0ZXh0LTV4bCB7XFxuICAgIGZvbnQtc2l6ZTogM3JlbTtcXG4gICAgbGluZS1oZWlnaHQ6IDE7XFxuICB9XFxuXFxuICAubWRcXFxcOnRleHQtNnhsIHtcXG4gICAgZm9udC1zaXplOiAzLjc1cmVtO1xcbiAgICBsaW5lLWhlaWdodDogMTtcXG4gIH1cXG5cXG4gIC5tZFxcXFw6dGV4dC14bCB7XFxuICAgIGZvbnQtc2l6ZTogMS4yNXJlbTtcXG4gICAgbGluZS1oZWlnaHQ6IDEuNzVyZW07XFxuICB9XFxufVxcclxcblxcclxcbkBtZWRpYSAobWluLXdpZHRoOiAxMDI0cHgpIHtcXG5cXG4gIC5sZ1xcXFw6Y29sLXNwYW4tMiB7XFxuICAgIGdyaWQtY29sdW1uOiBzcGFuIDIgLyBzcGFuIDI7XFxuICB9XFxuXFxuICAubGdcXFxcOmdyaWQtY29scy0zIHtcXG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoMywgbWlubWF4KDAsIDFmcikpO1xcbiAgfVxcblxcbiAgLmxnXFxcXDpncmlkLWNvbHMtNSB7XFxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDUsIG1pbm1heCgwLCAxZnIpKTtcXG4gIH1cXG59XFxyXFxuXFxyXFxuLlxcXFxbXFxcXCZcXFxcPnN2Z1xcXFwrZGl2XFxcXF1cXFxcOnRyYW5zbGF0ZS15LVxcXFxbLTNweFxcXFxdPnN2ZytkaXYge1xcbiAgLS10dy10cmFuc2xhdGUteTogLTNweDtcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlKHZhcigtLXR3LXRyYW5zbGF0ZS14KSwgdmFyKC0tdHctdHJhbnNsYXRlLXkpKSByb3RhdGUodmFyKC0tdHctcm90YXRlKSkgc2tld1godmFyKC0tdHctc2tldy14KSkgc2tld1kodmFyKC0tdHctc2tldy15KSkgc2NhbGVYKHZhcigtLXR3LXNjYWxlLXgpKSBzY2FsZVkodmFyKC0tdHctc2NhbGUteSkpO1xcbn1cXHJcXG5cXHJcXG4uXFxcXFtcXFxcJlxcXFw+c3ZnXFxcXF1cXFxcOmFic29sdXRlPnN2ZyB7XFxuICBwb3NpdGlvbjogYWJzb2x1dGU7XFxufVxcclxcblxcclxcbi5cXFxcW1xcXFwmXFxcXD5zdmdcXFxcXVxcXFw6bGVmdC00PnN2ZyB7XFxuICBsZWZ0OiAxcmVtO1xcbn1cXHJcXG5cXHJcXG4uXFxcXFtcXFxcJlxcXFw+c3ZnXFxcXF1cXFxcOnRvcC00PnN2ZyB7XFxuICB0b3A6IDFyZW07XFxufVxcclxcblxcclxcbi5cXFxcW1xcXFwmXFxcXD5zdmdcXFxcXVxcXFw6dGV4dC1kZXN0cnVjdGl2ZT5zdmcge1xcbiAgY29sb3I6IGhzbCh2YXIoLS1kZXN0cnVjdGl2ZSkpO1xcbn1cXHJcXG5cXHJcXG4uXFxcXFtcXFxcJlxcXFw+c3ZnXFxcXF1cXFxcOnRleHQtZm9yZWdyb3VuZD5zdmcge1xcbiAgY29sb3I6IGhzbCh2YXIoLS1mb3JlZ3JvdW5kKSk7XFxufVxcclxcblxcclxcbi5cXFxcW1xcXFwmXFxcXD5zdmdcXFxcflxcXFwqXFxcXF1cXFxcOnBsLTc+c3Znfioge1xcbiAgcGFkZGluZy1sZWZ0OiAxLjc1cmVtO1xcbn1cXHJcXG5cXHJcXG4uXFxcXFtcXFxcJl9wXFxcXF1cXFxcOmxlYWRpbmctcmVsYXhlZCBwIHtcXG4gIGxpbmUtaGVpZ2h0OiAxLjYyNTtcXG59XFxyXFxuXFxyXFxuXFxyXFxuXFxyXFxuXFxyXFxuXFxyXFxuXFxyXFxuXFxyXFxuXCIsIFwiXCIse1widmVyc2lvblwiOjMsXCJzb3VyY2VzXCI6W1wid2VicGFjazovL3N0eWxlcy9nbG9iYWxzLmNzc1wiXSxcIm5hbWVzXCI6W10sXCJtYXBwaW5nc1wiOlwiQUFBQTtFQUFBLHdCQUFjO0VBQWQsd0JBQWM7RUFBZCxtQkFBYztFQUFkLG1CQUFjO0VBQWQsY0FBYztFQUFkLGNBQWM7RUFBZCxjQUFjO0VBQWQsZUFBYztFQUFkLGVBQWM7RUFBZCxhQUFjO0VBQWQsYUFBYztFQUFkLGtCQUFjO0VBQWQsc0NBQWM7RUFBZCw4QkFBYztFQUFkLDZCQUFjO0VBQWQsNEJBQWM7RUFBZCxlQUFjO0VBQWQsb0JBQWM7RUFBZCxzQkFBYztFQUFkLHVCQUFjO0VBQWQsd0JBQWM7RUFBZCxrQkFBYztFQUFkLDJCQUFjO0VBQWQsNEJBQWM7RUFBZCxzQ0FBYztFQUFkLGtDQUFjO0VBQWQsMkJBQWM7RUFBZCxzQkFBYztFQUFkLDhCQUFjO0VBQWQsWUFBYztFQUFkLGtCQUFjO0VBQWQsZ0JBQWM7RUFBZCxpQkFBYztFQUFkLGtCQUFjO0VBQWQsY0FBYztFQUFkLGdCQUFjO0VBQWQsYUFBYztFQUFkLG1CQUFjO0VBQWQscUJBQWM7RUFBZCwyQkFBYztFQUFkLHlCQUFjO0VBQWQsMEJBQWM7RUFBZCwyQkFBYztFQUFkLHVCQUFjO0VBQWQsd0JBQWM7RUFBZCx5QkFBYztFQUFkLHNCQUFjO0VBQWQsb0JBQWM7RUFBZCxzQkFBYztFQUFkLHFCQUFjO0VBQWQ7QUFBYzs7QUFBZDtFQUFBLHdCQUFjO0VBQWQsd0JBQWM7RUFBZCxtQkFBYztFQUFkLG1CQUFjO0VBQWQsY0FBYztFQUFkLGNBQWM7RUFBZCxjQUFjO0VBQWQsZUFBYztFQUFkLGVBQWM7RUFBZCxhQUFjO0VBQWQsYUFBYztFQUFkLGtCQUFjO0VBQWQsc0NBQWM7RUFBZCw4QkFBYztFQUFkLDZCQUFjO0VBQWQsNEJBQWM7RUFBZCxlQUFjO0VBQWQsb0JBQWM7RUFBZCxzQkFBYztFQUFkLHVCQUFjO0VBQWQsd0JBQWM7RUFBZCxrQkFBYztFQUFkLDJCQUFjO0VBQWQsNEJBQWM7RUFBZCxzQ0FBYztFQUFkLGtDQUFjO0VBQWQsMkJBQWM7RUFBZCxzQkFBYztFQUFkLDhCQUFjO0VBQWQsWUFBYztFQUFkLGtCQUFjO0VBQWQsZ0JBQWM7RUFBZCxpQkFBYztFQUFkLGtCQUFjO0VBQWQsY0FBYztFQUFkLGdCQUFjO0VBQWQsYUFBYztFQUFkLG1CQUFjO0VBQWQscUJBQWM7RUFBZCwyQkFBYztFQUFkLHlCQUFjO0VBQWQsMEJBQWM7RUFBZCwyQkFBYztFQUFkLHVCQUFjO0VBQWQsd0JBQWM7RUFBZCx5QkFBYztFQUFkLHNCQUFjO0VBQWQsb0JBQWM7RUFBZCxzQkFBYztFQUFkLHFCQUFjO0VBQWQ7QUFBYyxDQUFkOztDQUFjLENBQWQ7OztDQUFjOztBQUFkOzs7RUFBQSxzQkFBYyxFQUFkLE1BQWM7RUFBZCxlQUFjLEVBQWQsTUFBYztFQUFkLG1CQUFjLEVBQWQsTUFBYztFQUFkLHFCQUFjLEVBQWQsTUFBYztBQUFBOztBQUFkOztFQUFBLGdCQUFjO0FBQUE7O0FBQWQ7Ozs7Ozs7O0NBQWM7O0FBQWQ7O0VBQUEsZ0JBQWMsRUFBZCxNQUFjO0VBQWQsOEJBQWMsRUFBZCxNQUFjO0VBQWQsZ0JBQWMsRUFBZCxNQUFjO0VBQWQsY0FBYztLQUFkLFdBQWMsRUFBZCxNQUFjO0VBQWQsK0hBQWMsRUFBZCxNQUFjO0VBQWQsNkJBQWMsRUFBZCxNQUFjO0VBQWQsK0JBQWMsRUFBZCxNQUFjO0VBQWQsd0NBQWMsRUFBZCxNQUFjO0FBQUE7O0FBQWQ7OztDQUFjOztBQUFkO0VBQUEsU0FBYyxFQUFkLE1BQWM7RUFBZCxvQkFBYyxFQUFkLE1BQWM7QUFBQTs7QUFBZDs7OztDQUFjOztBQUFkO0VBQUEsU0FBYyxFQUFkLE1BQWM7RUFBZCxjQUFjLEVBQWQsTUFBYztFQUFkLHFCQUFjLEVBQWQsTUFBYztBQUFBOztBQUFkOztDQUFjOztBQUFkO0VBQUEseUNBQWM7VUFBZCxpQ0FBYztBQUFBOztBQUFkOztDQUFjOztBQUFkOzs7Ozs7RUFBQSxrQkFBYztFQUFkLG9CQUFjO0FBQUE7O0FBQWQ7O0NBQWM7O0FBQWQ7RUFBQSxjQUFjO0VBQWQsd0JBQWM7QUFBQTs7QUFBZDs7Q0FBYzs7QUFBZDs7RUFBQSxtQkFBYztBQUFBOztBQUFkOzs7OztDQUFjOztBQUFkOzs7O0VBQUEsK0dBQWMsRUFBZCxNQUFjO0VBQWQsNkJBQWMsRUFBZCxNQUFjO0VBQWQsK0JBQWMsRUFBZCxNQUFjO0VBQWQsY0FBYyxFQUFkLE1BQWM7QUFBQTs7QUFBZDs7Q0FBYzs7QUFBZDtFQUFBLGNBQWM7QUFBQTs7QUFBZDs7Q0FBYzs7QUFBZDs7RUFBQSxjQUFjO0VBQWQsY0FBYztFQUFkLGtCQUFjO0VBQWQsd0JBQWM7QUFBQTs7QUFBZDtFQUFBLGVBQWM7QUFBQTs7QUFBZDtFQUFBLFdBQWM7QUFBQTs7QUFBZDs7OztDQUFjOztBQUFkO0VBQUEsY0FBYyxFQUFkLE1BQWM7RUFBZCxxQkFBYyxFQUFkLE1BQWM7RUFBZCx5QkFBYyxFQUFkLE1BQWM7QUFBQTs7QUFBZDs7OztDQUFjOztBQUFkOzs7OztFQUFBLG9CQUFjLEVBQWQsTUFBYztFQUFkLDhCQUFjLEVBQWQsTUFBYztFQUFkLGdDQUFjLEVBQWQsTUFBYztFQUFkLGVBQWMsRUFBZCxNQUFjO0VBQWQsb0JBQWMsRUFBZCxNQUFjO0VBQWQsb0JBQWMsRUFBZCxNQUFjO0VBQWQsdUJBQWMsRUFBZCxNQUFjO0VBQWQsY0FBYyxFQUFkLE1BQWM7RUFBZCxTQUFjLEVBQWQsTUFBYztFQUFkLFVBQWMsRUFBZCxNQUFjO0FBQUE7O0FBQWQ7O0NBQWM7O0FBQWQ7O0VBQUEsb0JBQWM7QUFBQTs7QUFBZDs7O0NBQWM7O0FBQWQ7Ozs7RUFBQSwwQkFBYyxFQUFkLE1BQWM7RUFBZCw2QkFBYyxFQUFkLE1BQWM7RUFBZCxzQkFBYyxFQUFkLE1BQWM7QUFBQTs7QUFBZDs7Q0FBYzs7QUFBZDtFQUFBLGFBQWM7QUFBQTs7QUFBZDs7Q0FBYzs7QUFBZDtFQUFBLGdCQUFjO0FBQUE7O0FBQWQ7O0NBQWM7O0FBQWQ7RUFBQSx3QkFBYztBQUFBOztBQUFkOztDQUFjOztBQUFkOztFQUFBLFlBQWM7QUFBQTs7QUFBZDs7O0NBQWM7O0FBQWQ7RUFBQSw2QkFBYyxFQUFkLE1BQWM7RUFBZCxvQkFBYyxFQUFkLE1BQWM7QUFBQTs7QUFBZDs7Q0FBYzs7QUFBZDtFQUFBLHdCQUFjO0FBQUE7O0FBQWQ7OztDQUFjOztBQUFkO0VBQUEsMEJBQWMsRUFBZCxNQUFjO0VBQWQsYUFBYyxFQUFkLE1BQWM7QUFBQTs7QUFBZDs7Q0FBYzs7QUFBZDtFQUFBLGtCQUFjO0FBQUE7O0FBQWQ7O0NBQWM7O0FBQWQ7Ozs7Ozs7Ozs7Ozs7RUFBQSxTQUFjO0FBQUE7O0FBQWQ7RUFBQSxTQUFjO0VBQWQsVUFBYztBQUFBOztBQUFkO0VBQUEsVUFBYztBQUFBOztBQUFkOzs7RUFBQSxnQkFBYztFQUFkLFNBQWM7RUFBZCxVQUFjO0FBQUE7O0FBQWQ7O0NBQWM7QUFBZDtFQUFBLFVBQWM7QUFBQTs7QUFBZDs7Q0FBYzs7QUFBZDtFQUFBLGdCQUFjO0FBQUE7O0FBQWQ7OztDQUFjOztBQUFkO0VBQUEsVUFBYyxFQUFkLE1BQWM7RUFBZCxjQUFjLEVBQWQsTUFBYztBQUFBOztBQUFkOztFQUFBLFVBQWMsRUFBZCxNQUFjO0VBQWQsY0FBYyxFQUFkLE1BQWM7QUFBQTs7QUFBZDs7Q0FBYzs7QUFBZDs7RUFBQSxlQUFjO0FBQUE7O0FBQWQ7O0NBQWM7QUFBZDtFQUFBLGVBQWM7QUFBQTs7QUFBZDs7OztDQUFjOztBQUFkOzs7Ozs7OztFQUFBLGNBQWMsRUFBZCxNQUFjO0VBQWQsc0JBQWMsRUFBZCxNQUFjO0FBQUE7O0FBQWQ7O0NBQWM7O0FBQWQ7O0VBQUEsZUFBYztFQUFkLFlBQWM7QUFBQTs7QUFBZCx3RUFBYztBQUFkO0VBQUEsYUFBYztBQUFBO0VBQWQ7SUFBQSx1QkFBYztJQUFkLDBCQUFjO0lBQWQsaUJBQWM7SUFBZCwrQkFBYztJQUFkLG9CQUFjO0lBQWQsa0NBQWM7SUFBZCx1QkFBYztJQUFkLDhCQUFjO0lBQWQsMkJBQWM7SUFBZCxvQ0FBYztJQUFkLHVCQUFjO0lBQWQsa0NBQWM7SUFBZCx3QkFBYztJQUFkLGlDQUFjO0lBQWQsNEJBQWM7SUFBZCxrQ0FBYztJQUFkLHNCQUFjO0lBQWQscUJBQWM7SUFBZCxvQkFBYztJQUFkLGdCQUFjOztJQUFkLGlCQUFjO0lBQWQsMEJBQWM7SUFBZCw0QkFBYztJQUFkLDBCQUFjOztJQUFkLGdCQUFjO0lBQWQsNEJBQWM7O0lBQWQsdUJBQWM7SUFBZCw2Q0FBYztJQUFkLDJDQUFjO0lBQWQsMENBQWM7O0lBQWQscUJBQWM7SUFBZCw2QkFBYztFQUFBOztFQUFkO0lBQUEsMEJBQWM7SUFBZCxzQkFBYztJQUFkLG9CQUFjO0lBQWQsMkJBQWM7SUFBZCx1QkFBYztJQUFkLDhCQUFjO0lBQWQsbUJBQWM7SUFBZCxrQ0FBYztJQUFkLDJCQUFjO0lBQWQsZ0NBQWM7SUFBZCx1QkFBYztJQUFkLGdDQUFjO0lBQWQsd0JBQWM7SUFBZCw2QkFBYztJQUFkLDRCQUFjO0lBQWQsa0NBQWM7SUFBZCx3QkFBYztJQUFkLHVCQUFjO0lBQWQsc0JBQWM7O0lBQWQsaUJBQWM7SUFBZCwwQkFBYztJQUFkLDRCQUFjO0lBQWQsMEJBQWM7O0lBQWQsZ0JBQWM7SUFBZCw0QkFBYzs7SUFBZCx1QkFBYztJQUFkLDZDQUFjO0lBQWQsMkNBQWM7SUFBZCwwQ0FBYztFQUFBO0VBQWQ7RUFBQTtBQUFjO0VBQWQ7RUFBQSx3Q0FBYztFQUFkLDZCQUFjO0lBQWQseUNBQWM7SUFBZDtBQUFjO0FBQ2Q7RUFBQSxXQUFvQjtFQUFwQixrQkFBb0I7RUFBcEIsaUJBQW9CO0VBQXBCLG1CQUFvQjtFQUFwQjtBQUFvQjtBQUFwQjs7RUFBQTtJQUFBO0VBQW9CO0FBQUE7QUFDcEI7RUFBQSxrQkFBbUI7RUFBbkIsVUFBbUI7RUFBbkIsV0FBbUI7RUFBbkIsVUFBbUI7RUFBbkIsWUFBbUI7RUFBbkIsZ0JBQW1CO0VBQW5CLHNCQUFtQjtFQUFuQixtQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLHFCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG9CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG1CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGlCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG1CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGdCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQSx1QkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQSxzQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxzQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSx5QkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxzQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxpQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxrQkFBbUI7RUFBbkIsa0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7O0VBQUE7SUFBQTtFQUFtQjs7RUFBbkI7SUFBQTtFQUFtQjtBQUFBO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7O0VBQUE7SUFBQTtFQUFtQjtBQUFBO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7O0VBQUE7SUFBQTtFQUFtQjtBQUFBO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUEseUJBQW1CO0tBQW5CLHNCQUFtQjtVQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLHVCQUFtQjtFQUFuQixzREFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSx1QkFBbUI7RUFBbkIsb0RBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsdUJBQW1CO0VBQW5CLHNEQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLHVCQUFtQjtFQUFuQixvREFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSx1QkFBbUI7RUFBbkIsK0RBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsdUJBQW1CO0VBQW5CLGdFQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLHVCQUFtQjtFQUFuQiw4REFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSx1QkFBbUI7RUFBbkIsK0RBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsdUJBQW1CO0VBQW5CLDREQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLHVCQUFtQjtFQUFuQiw4REFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSx1QkFBbUI7RUFBbkIsNERBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLGdCQUFtQjtFQUFuQix1QkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQSxzQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUEsc0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsc0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsc0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsc0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLHNCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLHNCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQSw0REFBbUI7RUFBbkIsb0VBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsNERBQW1CO0VBQW5CLHFFQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLDREQUFtQjtFQUFuQixrRUFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSw0REFBbUI7RUFBbkIsbUVBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsMkVBQW1CO0VBQW5CLDJFQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGlGQUFtQjtFQUFuQiwyRUFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSw2RUFBbUI7RUFBbkIsNkVBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsb0ZBQW1CO0VBQW5CLDZFQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLHFFQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLDZCQUFtQjtVQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUEsb0JBQW1CO0tBQW5CO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQSxvQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxxQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxrQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxrQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxvQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxxQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxtQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxpQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxtQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxpQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxvQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxpQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxtQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxpQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLGlCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG1CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG1CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLG1CQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGtCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQSxvQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxvQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxvQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxvQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUEsb0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsb0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsb0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsb0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsb0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsb0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsb0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsb0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsb0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQSxvQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxvQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxvQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUEsb0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQSwrRUFBbUI7RUFBbkIsbUdBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsNkVBQW1CO0VBQW5CLGlHQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLDBDQUFtQjtFQUFuQix1REFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSxnRkFBbUI7RUFBbkIsb0dBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsZ0RBQW1CO0VBQW5CLDZEQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGlEQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLGlEQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLDhCQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQSxvQkFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBLDhCQUFtQjtFQUFuQiwrUUFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSw2QkFBbUI7RUFBbkIsK1FBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsZ0tBQW1CO0VBQW5CLHdKQUFtQjtFQUFuQixpTEFBbUI7RUFBbkIsd0RBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsd0JBQW1CO0VBQW5CLHdEQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBLCtGQUFtQjtFQUFuQix3REFBbUI7RUFBbkI7QUFBbUI7QUFBbkI7RUFBQSw0QkFBbUI7RUFBbkIsd0RBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUEsOEJBQW1CO0VBQW5CLHdEQUFtQjtFQUFuQjtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5COztFQUFBO0lBQUEsbUNBQW1CO0lBQW5CO0VBQW1CO0FBQUE7QUFBbkI7O0VBQUE7SUFBQSxrQ0FBbUI7SUFBbkI7RUFBbUI7QUFBQTtBQUFuQjtFQUFBLHFCQUFtQjtFQUFuQix5QkFBbUI7RUFBbkIsMkJBQW1CO0VBQW5CLHlCQUFtQjtFQUFuQiwwQkFBbUI7RUFBbkIsK0JBQW1CO0VBQW5CO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7QUFBbkI7RUFBQTtBQUFtQjtBQUFuQjtFQUFBO0FBQW1CO0FBQW5CO0VBQUE7QUFBbUI7O0FBeUZuQix3QkFBd0I7QUFDeEI7RUFDRSxxQ0FBcUM7RUFDckMsMEJBQTBCO0VBQzFCLGtDQUFrQztFQUNsQywyQ0FBMkM7RUFDM0MsMEZBQTBGO0FBQzVGOztBQUVBO0VBQ0UsK0JBQStCO0VBQy9CLHFDQUFxQztBQUN2Qzs7QUFFQSx1QkFBdUI7QUFDdkI7RUFDRSwrQ0FBK0M7RUFDL0MsOENBQThDO0FBQ2hEOztBQUVBO0VBQ0UseUNBQXlDO0FBQzNDOztBQUVBLHdCQUF3QjtBQUN4QjtFQUNFLHlCQUF5QjtBQUMzQjs7QUFFQTtFQUNFLDJDQUEyQztBQUM3Qzs7QUFFQTtFQUNFLHFDQUFxQztBQUN2Qzs7QUFFQSxrQkFBa0I7QUFDbEI7RUFDRSxzRUFBc0U7RUFDdEUsNkJBQTZCO0VBQzdCLG9DQUFvQztFQUNwQyxxQkFBcUI7RUFDckIsOENBQThDO0FBQ2hEOztBQUVBO0VBQ0UsZ0VBQWdFO0VBQ2hFLDZCQUE2QjtFQUM3QixvQ0FBb0M7RUFDcEMscUJBQXFCO0FBQ3ZCOztBQUVBLDJCQUEyQjtBQUMzQjtFQUNFLDBCQUEwQjtFQUMxQixvREFBb0Q7QUFDdEQ7O0FBRUE7RUFDRTt3RUFDc0U7QUFDeEU7O0FBRUEsNkJBQTZCO0FBQzdCO0VBQ0UsVUFBVTtFQUNWLDJCQUEyQjtFQUMzQixrREFBa0Q7QUFDcEQ7O0FBRUE7RUFDRSxVQUFVO0VBQ1Ysd0JBQXdCO0FBQzFCOztBQUVBLHdCQUF3QjtBQUN4QjtFQUNFLGtCQUFrQjtFQUNsQixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLE1BQU07RUFDTixXQUFXO0VBQ1gsV0FBVztFQUNYLFlBQVk7RUFDWjs7Ozs7R0FLQztFQUNELDBCQUEwQjtBQUM1Qjs7QUFFQTtFQUNFLFVBQVU7QUFDWjs7QUFFQSxnQkFBZ0I7QUFDaEI7RUFDRSxnRkFBZ0Y7RUFDaEYsMEJBQTBCO0VBQzFCLG9EQUFvRDtBQUN0RDs7QUFFQTtFQUNFLDBFQUEwRTtBQUM1RTs7QUFFQSx1QkFBdUI7QUFDdkI7RUFDRSxrQkFBa0I7RUFDbEIsVUFBVTtBQUNaOztBQUVBO0VBQ0UsV0FBVztFQUNYLGtCQUFrQjtFQUNsQixTQUFTO0VBQ1QsVUFBVTtFQUNWLFdBQVc7RUFDWCxZQUFZO0VBQ1o7Ozs7Ozs7OztHQVNDO0VBQ0Qsc0JBQXNCO0VBQ3RCLFdBQVc7RUFDWCwwQkFBMEI7RUFDMUIsdUNBQXVDO0VBQ3ZDLFVBQVU7RUFDViw2QkFBNkI7QUFDL0I7O0FBRUE7RUFDRSxVQUFVO0FBQ1o7O0FBRUEsZ0JBQWdCO0FBQ2hCO0VBQ0UsZ0NBQWdDO0FBQ2xDOztBQUVBO0VBQ0UsaURBQWlEO0VBQ2pELHFEQUFxRDtBQUN2RDs7QUFFQTtFQUNFLG1EQUFtRDtBQUNyRDs7QUFFQTtFQUNFLGdEQUFnRDtBQUNsRDs7QUFFQSxxQkFBcUI7QUFDckI7RUFDRSx1Q0FBdUM7QUFDekM7O0FBRUEsb0JBQW9CO0FBQ3BCO0VBQ0UsVUFBVTtFQUNWLDJCQUEyQjtBQUM3Qjs7QUFFQTtFQUNFLFVBQVU7RUFDVix3QkFBd0I7RUFDeEIsd0NBQXdDO0FBQzFDOztBQUVBO0VBQ0UsVUFBVTtFQUNWLHdCQUF3QjtBQUMxQjs7QUFFQTtFQUNFLFVBQVU7RUFDViw0QkFBNEI7RUFDNUIsd0NBQXdDO0FBQzFDOztBQUVBLDRCQUE0QjtBQUM1QjtFQUNFLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsWUFBWTtFQUNaLE9BQU87RUFDUCxRQUFRO0VBQ1IsV0FBVztFQUNYLHdDQUF3QztFQUN4QywyQkFBMkI7QUFDN0I7O0FBRUE7O0VBRUUsV0FBVztBQUNiOztBQUVBLHFCQUFxQjtBQUNyQjtFQUNFLFVBQVU7RUFDVixXQUFXO0FBQ2I7O0FBRUE7RUFDRSx1QkFBdUI7QUFDekI7O0FBRUE7RUFDRSxvQ0FBb0M7RUFDcEMsa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0Usb0NBQW9DO0FBQ3RDOztBQUVBO0VBQ0UsOEJBQThCO0FBQ2hDOztBQUVBO0VBQ0UsOEJBQThCO0FBQ2hDOztBQUVBLGtDQUFrQztBQUNsQztFQUNFO0lBQ0UsMkJBQTJCO0VBQzdCO0VBQ0E7SUFDRSw2QkFBNkI7RUFDL0I7RUFDQTtJQUNFLDJCQUEyQjtFQUM3QjtBQUNGOztBQUVBO0VBQ0U7SUFDRSxZQUFZO0VBQ2Q7RUFDQTtJQUNFLFVBQVU7RUFDWjtFQUNBO0lBQ0UsWUFBWTtFQUNkO0FBQ0Y7O0FBRUE7RUFDRSwrQ0FBK0M7QUFDakQ7O0FBRUE7RUFDRSxrQ0FBa0M7QUFDcEM7O0FBRUE7RUFDRTtJQUNFLHVCQUF1QjtFQUN6QjtFQUNBO0lBQ0UseUJBQXlCO0VBQzNCO0FBQ0Y7O0FBclhBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUEsa0JBNlhBO0VBN1hBLGtCQTZYQTtFQTdYQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQSxzQkE2WEE7RUE3WEE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUEsa0JBNlhBO0VBN1hBO0FBNlhBOztBQTdYQTtFQUFBO0FBNlhBOztBQTdYQTtFQUFBO0FBNlhBOztBQTdYQTtFQUFBO0FBNlhBOztBQTdYQTtFQUFBO0FBNlhBOztBQTdYQTtFQUFBO0FBNlhBOztBQTdYQTtFQUFBO0FBNlhBOztBQTdYQTtFQUFBO0FBNlhBOztBQTdYQTtFQUFBO0FBNlhBOztBQTdYQTtFQUFBLG9CQTZYQTtFQTdYQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQSxvQkE2WEE7RUE3WEE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUEsK0VBNlhBO0VBN1hBLG1HQTZYQTtFQTdYQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQSw4QkE2WEE7RUE3WEE7QUE2WEE7O0FBN1hBO0VBQUEsMkdBNlhBO0VBN1hBLHlHQTZYQTtFQTdYQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQSw4QkE2WEE7RUE3WEE7QUE2WEE7O0FBN1hBO0VBQUEsMkdBNlhBO0VBN1hBLHlHQTZYQTtFQTdYQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQSxvQkE2WEE7RUE3WEE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUEsMENBNlhBO0VBN1hBLHVEQTZYQTtFQTdYQTtBQTZYQTs7QUE3WEE7RUFBQSxxQkE2WEE7RUE3WEEseUJBNlhBO0VBN1hBLDJCQTZYQTtFQTdYQSx5QkE2WEE7RUE3WEEsMEJBNlhBO0VBN1hBLCtCQTZYQTtFQTdYQTtBQTZYQTs7QUE3WEE7RUFBQSxvQkE2WEE7RUE3WEEseUJBNlhBO0VBN1hBLDBCQTZYQTtFQTdYQSx3QkE2WEE7RUE3WEEseUJBNlhBO0VBN1hBLDhCQTZYQTtFQTdYQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQTtBQTZYQTs7QUE3WEE7RUFBQSxvQkE2WEE7RUE3WEE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUE7QUE2WEE7O0FBN1hBO0VBQUEsb0JBNlhBO0VBN1hBO0FBNlhBOztBQTdYQTs7RUFBQTtJQUFBO0VBNlhBOztFQTdYQTtJQUFBO0VBNlhBOztFQTdYQTtJQUFBO0VBNlhBOztFQTdYQTtJQUFBO0VBNlhBOztFQTdYQTtJQUFBO0VBNlhBOztFQTdYQTtJQUFBLHVCQTZYQTtJQTdYQSxzREE2WEE7SUE3WEE7RUE2WEE7O0VBN1hBO0lBQUE7RUE2WEE7O0VBN1hBO0lBQUE7RUE2WEE7QUFBQTs7QUE3WEE7O0VBQUE7SUFBQTtFQTZYQTs7RUE3WEE7SUFBQTtFQTZYQTs7RUE3WEE7SUFBQTtFQTZYQTs7RUE3WEE7SUFBQTtFQTZYQTs7RUE3WEE7SUFBQTtFQTZYQTs7RUE3WEE7SUFBQTtFQTZYQTs7RUE3WEE7SUFBQTtFQTZYQTs7RUE3WEE7SUFBQTtFQTZYQTs7RUE3WEE7SUFBQTtFQTZYQTs7RUE3WEE7SUFBQTtFQTZYQTs7RUE3WEE7SUFBQTtFQTZYQTs7RUE3WEE7SUFBQSxpQkE2WEE7SUE3WEE7RUE2WEE7O0VBN1hBO0lBQUEsaUJBNlhBO0lBN1hBO0VBNlhBOztFQTdYQTtJQUFBLGlCQTZYQTtJQTdYQTtFQTZYQTs7RUE3WEE7SUFBQSxpQkE2WEE7SUE3WEE7RUE2WEE7O0VBN1hBO0lBQUEsaUJBNlhBO0lBN1hBO0VBNlhBOztFQTdYQTtJQUFBLG1CQTZYQTtJQTdYQTtFQTZYQTs7RUE3WEE7SUFBQSxrQkE2WEE7SUE3WEE7RUE2WEE7O0VBN1hBO0lBQUEsZUE2WEE7SUE3WEE7RUE2WEE7O0VBN1hBO0lBQUEsa0JBNlhBO0lBN1hBO0VBNlhBOztFQTdYQTtJQUFBLGtCQTZYQTtJQTdYQTtFQTZYQTtBQUFBOztBQTdYQTs7RUFBQTtJQUFBO0VBNlhBOztFQTdYQTtJQUFBO0VBNlhBOztFQTdYQTtJQUFBO0VBNlhBO0FBQUE7O0FBN1hBO0VBQUEsc0JBNlhBO0VBN1hBO0FBNlhBOztBQTdYQTtFQUFBO0FBNlhBOztBQTdYQTtFQUFBO0FBNlhBOztBQTdYQTtFQUFBO0FBNlhBOztBQTdYQTtFQUFBO0FBNlhBOztBQTdYQTtFQUFBO0FBNlhBOztBQTdYQTtFQUFBO0FBNlhBOztBQTdYQTtFQUFBO0FBNlhBXCIsXCJzb3VyY2VzQ29udGVudFwiOltcIkB0YWlsd2luZCBiYXNlO1xcclxcbkB0YWlsd2luZCBjb21wb25lbnRzO1xcclxcbkB0YWlsd2luZCB1dGlsaXRpZXM7XFxyXFxuXFxyXFxuQGxheWVyIGJhc2Uge1xcclxcbiAgOnJvb3Qge1xcclxcbiAgICAtLWJhY2tncm91bmQ6IDAgMCUgMTAwJTtcXHJcXG4gICAgLS1mb3JlZ3JvdW5kOiAyNDAgMTAlIDMuOSU7XFxyXFxuICAgIC0tY2FyZDogMCAwJSAxMDAlO1xcclxcbiAgICAtLWNhcmQtZm9yZWdyb3VuZDogMjQwIDEwJSAzLjklO1xcclxcbiAgICAtLXBvcG92ZXI6IDAgMCUgMTAwJTtcXHJcXG4gICAgLS1wb3BvdmVyLWZvcmVncm91bmQ6IDI0MCAxMCUgMy45JTtcXHJcXG4gICAgLS1wcmltYXJ5OiAyNDAgNS45JSAxMCU7XFxyXFxuICAgIC0tcHJpbWFyeS1mb3JlZ3JvdW5kOiAwIDAlIDk4JTtcXHJcXG4gICAgLS1zZWNvbmRhcnk6IDI0MCA0LjglIDk1LjklO1xcclxcbiAgICAtLXNlY29uZGFyeS1mb3JlZ3JvdW5kOiAyNDAgNS45JSAxMCU7XFxyXFxuICAgIC0tbXV0ZWQ6IDI0MCA0LjglIDk1LjklO1xcclxcbiAgICAtLW11dGVkLWZvcmVncm91bmQ6IDI0MCAzLjglIDQ2LjElO1xcclxcbiAgICAtLWFjY2VudDogMjQwIDQuOCUgOTUuOSU7XFxyXFxuICAgIC0tYWNjZW50LWZvcmVncm91bmQ6IDI0MCA1LjklIDEwJTtcXHJcXG4gICAgLS1kZXN0cnVjdGl2ZTogMCA4NC4yJSA2MC4yJTtcXHJcXG4gICAgLS1kZXN0cnVjdGl2ZS1mb3JlZ3JvdW5kOiAwIDAlIDk4JTtcXHJcXG4gICAgLS1ib3JkZXI6IDI0MCA1LjklIDkwJTtcXHJcXG4gICAgLS1pbnB1dDogMjQwIDUuOSUgOTAlO1xcclxcbiAgICAtLXJpbmc6IDI0MCA1LjklIDEwJTtcXHJcXG4gICAgLS1yYWRpdXM6IDAuNXJlbTtcXHJcXG5cXHJcXG4gICAgLyogVGhlbWUgY29sb3JzICovXFxyXFxuICAgIC0tdGhlbWUtYmx1ZTogMjEwIDEwMCUgNTAlO1xcclxcbiAgICAtLXRoZW1lLXB1cnBsZTogMjYwIDEwMCUgNjAlO1xcclxcbiAgICAtLXRoZW1lLXRlYWw6IDE4MCAxMDAlIDQwJTtcXHJcXG5cXHJcXG4gICAgLyogR2xvdyBjb2xvcnMgKi9cXHJcXG4gICAgLS1nbG93LWNvbG9yOiAyMTAsIDEwMCUsIDUwJTtcXHJcXG5cXHJcXG4gICAgLyogRmxvdyBib3JkZXIgY29sb3JzICovXFxyXFxuICAgIC0tYm9yZGVyLXN0YXJ0LWNvbG9yOiByZ2JhKDMzLCAxNTAsIDI0MywgMC41KTtcXHJcXG4gICAgLS1ib3JkZXItbWlkLWNvbG9yOiByZ2JhKDE1NiwgMzksIDE3NiwgMC41KTtcXHJcXG4gICAgLS1ib3JkZXItZW5kLWNvbG9yOiByZ2JhKDAsIDE4OCwgMjEyLCAwLjUpO1xcclxcblxcclxcbiAgICAvKiBUaGVtZSB0cmFuc2l0aW9uICovXFxyXFxuICAgIC0tdGhlbWUtdHJhbnNpdGlvbjogMC4zcyBlYXNlO1xcclxcbiAgfVxcclxcblxcclxcbiAgLmRhcmsge1xcclxcbiAgICAtLWJhY2tncm91bmQ6IDI0MCAxMCUgMy45JTtcXHJcXG4gICAgLS1mb3JlZ3JvdW5kOiAwIDAlIDk4JTtcXHJcXG4gICAgLS1jYXJkOiAyNDAgMTAlIDMuOSU7XFxyXFxuICAgIC0tY2FyZC1mb3JlZ3JvdW5kOiAwIDAlIDk4JTtcXHJcXG4gICAgLS1wb3BvdmVyOiAyNDAgMTAlIDMuOSU7XFxyXFxuICAgIC0tcG9wb3Zlci1mb3JlZ3JvdW5kOiAwIDAlIDk4JTtcXHJcXG4gICAgLS1wcmltYXJ5OiAwIDAlIDk4JTtcXHJcXG4gICAgLS1wcmltYXJ5LWZvcmVncm91bmQ6IDI0MCA1LjklIDEwJTtcXHJcXG4gICAgLS1zZWNvbmRhcnk6IDI0MCAzLjclIDE1LjklO1xcclxcbiAgICAtLXNlY29uZGFyeS1mb3JlZ3JvdW5kOiAwIDAlIDk4JTtcXHJcXG4gICAgLS1tdXRlZDogMjQwIDMuNyUgMTUuOSU7XFxyXFxuICAgIC0tbXV0ZWQtZm9yZWdyb3VuZDogMjQwIDUlIDY0LjklO1xcclxcbiAgICAtLWFjY2VudDogMjQwIDMuNyUgMTUuOSU7XFxyXFxuICAgIC0tYWNjZW50LWZvcmVncm91bmQ6IDAgMCUgOTglO1xcclxcbiAgICAtLWRlc3RydWN0aXZlOiAwIDYyLjglIDMwLjYlO1xcclxcbiAgICAtLWRlc3RydWN0aXZlLWZvcmVncm91bmQ6IDAgMCUgOTglO1xcclxcbiAgICAtLWJvcmRlcjogMjQwIDMuNyUgMTUuOSU7XFxyXFxuICAgIC0taW5wdXQ6IDI0MCAzLjclIDE1LjklO1xcclxcbiAgICAtLXJpbmc6IDI0MCA0LjklIDgzLjklO1xcclxcblxcclxcbiAgICAvKiBUaGVtZSBjb2xvcnMgKi9cXHJcXG4gICAgLS10aGVtZS1ibHVlOiAyMTAgMTAwJSA2MCU7XFxyXFxuICAgIC0tdGhlbWUtcHVycGxlOiAyNjAgMTAwJSA3MCU7XFxyXFxuICAgIC0tdGhlbWUtdGVhbDogMTgwIDEwMCUgNTAlO1xcclxcblxcclxcbiAgICAvKiBHbG93IGNvbG9ycyAqL1xcclxcbiAgICAtLWdsb3ctY29sb3I6IDIxMCwgMTAwJSwgNjAlO1xcclxcblxcclxcbiAgICAvKiBGbG93IGJvcmRlciBjb2xvcnMgKi9cXHJcXG4gICAgLS1ib3JkZXItc3RhcnQtY29sb3I6IHJnYmEoMzMsIDE1MCwgMjQzLCAwLjcpO1xcclxcbiAgICAtLWJvcmRlci1taWQtY29sb3I6IHJnYmEoMTU2LCAzOSwgMTc2LCAwLjcpO1xcclxcbiAgICAtLWJvcmRlci1lbmQtY29sb3I6IHJnYmEoMCwgMTg4LCAyMTIsIDAuNyk7XFxyXFxuICB9XFxyXFxufVxcclxcblxcclxcbkBsYXllciBiYXNlIHtcXHJcXG4gICoge1xcclxcbiAgICBAYXBwbHkgYm9yZGVyLWJvcmRlcjtcXHJcXG4gIH1cXHJcXG4gIGJvZHkge1xcclxcbiAgICBAYXBwbHkgYmctYmFja2dyb3VuZCB0ZXh0LWZvcmVncm91bmQ7XFxyXFxuICAgIGZvbnQtZmVhdHVyZS1zZXR0aW5nczogXFxcInJsaWdcXFwiIDEsIFxcXCJjYWx0XFxcIiAxO1xcclxcbiAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIHZhcigtLXRoZW1lLXRyYW5zaXRpb24pLCBjb2xvciB2YXIoLS10aGVtZS10cmFuc2l0aW9uKTtcXHJcXG4gIH1cXHJcXG59XFxyXFxuXFxyXFxuLyogU3VidGxlIGdsYXNzIGVmZmVjdCAqL1xcclxcbi5zdWJ0bGUtZ2xhc3Mge1xcclxcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjAzKTtcXHJcXG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cig4cHgpO1xcclxcbiAgLXdlYmtpdC1iYWNrZHJvcC1maWx0ZXI6IGJsdXIoOHB4KTtcXHJcXG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4wNSk7XFxyXFxuICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIHZhcigtLXRoZW1lLXRyYW5zaXRpb24pLCBib3JkZXItY29sb3IgdmFyKC0tdGhlbWUtdHJhbnNpdGlvbik7XFxyXFxufVxcclxcblxcclxcbi5saWdodCAuc3VidGxlLWdsYXNzIHtcXHJcXG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4wMyk7XFxyXFxuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDAsIDAsIDAuMDUpO1xcclxcbn1cXHJcXG5cXHJcXG4vKiBTdWJ0bGUgYm9yZGVyIGdsb3cgKi9cXHJcXG4uc3VidGxlLWJvcmRlci1nbG93IHtcXHJcXG4gIGJveC1zaGFkb3c6IDAgMCAwIDFweCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpO1xcclxcbiAgdHJhbnNpdGlvbjogYm94LXNoYWRvdyB2YXIoLS10aGVtZS10cmFuc2l0aW9uKTtcXHJcXG59XFxyXFxuXFxyXFxuLmxpZ2h0IC5zdWJ0bGUtYm9yZGVyLWdsb3cge1xcclxcbiAgYm94LXNoYWRvdzogMCAwIDAgMXB4IHJnYmEoMCwgMCwgMCwgMC4wNSk7XFxyXFxufVxcclxcblxcclxcbi8qIFN1YnRsZSBob3ZlciBlZmZlY3QgKi9cXHJcXG4uc3VidGxlLWhvdmVyIHtcXHJcXG4gIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XFxyXFxufVxcclxcblxcclxcbi5zdWJ0bGUtaG92ZXI6aG92ZXIge1xcclxcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KTtcXHJcXG59XFxyXFxuXFxyXFxuLmxpZ2h0IC5zdWJ0bGUtaG92ZXI6aG92ZXIge1xcclxcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjA1KTtcXHJcXG59XFxyXFxuXFxyXFxuLyogR3JhZGllbnQgdGV4dCAqL1xcclxcbi5ncmFkaWVudC10ZXh0IHtcXHJcXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0byByaWdodCwgI2ZmZiwgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjc1KSk7XFxyXFxuICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcXHJcXG4gIC13ZWJraXQtdGV4dC1maWxsLWNvbG9yOiB0cmFuc3BhcmVudDtcXHJcXG4gIGJhY2tncm91bmQtY2xpcDogdGV4dDtcXHJcXG4gIHRyYW5zaXRpb246IGJhY2tncm91bmQgdmFyKC0tdGhlbWUtdHJhbnNpdGlvbik7XFxyXFxufVxcclxcblxcclxcbi5saWdodCAuZ3JhZGllbnQtdGV4dCB7XFxyXFxuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQodG8gcmlnaHQsICMwMDAsIHJnYmEoMCwgMCwgMCwgMC43NSkpO1xcclxcbiAgLXdlYmtpdC1iYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XFxyXFxuICAtd2Via2l0LXRleHQtZmlsbC1jb2xvcjogdHJhbnNwYXJlbnQ7XFxyXFxuICBiYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XFxyXFxufVxcclxcblxcclxcbi8qIFN1YnRsZSBncmlkIGJhY2tncm91bmQgKi9cXHJcXG4uc3VidGxlLWdyaWQge1xcclxcbiAgYmFja2dyb3VuZC1zaXplOiAzMHB4IDMwcHg7XFxyXFxuICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWltYWdlIHZhcigtLXRoZW1lLXRyYW5zaXRpb24pO1xcclxcbn1cXHJcXG5cXHJcXG4ubGlnaHQgLnN1YnRsZS1ncmlkIHtcXHJcXG4gIGJhY2tncm91bmQtaW1hZ2U6IGxpbmVhci1ncmFkaWVudCh0byByaWdodCwgcmdiYSgwLCAwLCAwLCAwLjA1KSAxcHgsIHRyYW5zcGFyZW50IDFweCksXFxyXFxuICAgIGxpbmVhci1ncmFkaWVudCh0byBib3R0b20sIHJnYmEoMCwgMCwgMCwgMC4wNSkgMXB4LCB0cmFuc3BhcmVudCAxcHgpO1xcclxcbn1cXHJcXG5cXHJcXG4vKiBSZXZlYWwgYW5pbWF0aW9uIGNsYXNzZXMgKi9cXHJcXG4ucmV2ZWFsIHtcXHJcXG4gIG9wYWNpdHk6IDA7XFxyXFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMTBweCk7XFxyXFxuICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuNXMgZWFzZSwgdHJhbnNmb3JtIDAuNXMgZWFzZTtcXHJcXG59XFxyXFxuXFxyXFxuLnJldmVhbC5hY3RpdmUge1xcclxcbiAgb3BhY2l0eTogMTtcXHJcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcXHJcXG59XFxyXFxuXFxyXFxuLyogQnV0dG9uIHNoaW5lIGVmZmVjdCAqL1xcclxcbi5idG4tc2hpbmUge1xcclxcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xcclxcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcXHJcXG59XFxyXFxuXFxyXFxuLmJ0bi1zaGluZTo6YWZ0ZXIge1xcclxcbiAgY29udGVudDogXFxcIlxcXCI7XFxyXFxuICBwb3NpdGlvbjogYWJzb2x1dGU7XFxyXFxuICB0b3A6IDA7XFxyXFxuICBsZWZ0OiAtMTAwJTtcXHJcXG4gIHdpZHRoOiAxMDAlO1xcclxcbiAgaGVpZ2h0OiAxMDAlO1xcclxcbiAgYmFja2dyb3VuZC1pbWFnZTogbGluZWFyLWdyYWRpZW50KFxcclxcbiAgICA5MGRlZyxcXHJcXG4gICAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwKSAwJSxcXHJcXG4gICAgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjA1KSA1MCUsXFxyXFxuICAgIHJnYmEoMjU1LCAyNTUsIDI1NSwgMCkgMTAwJVxcclxcbiAgKTtcXHJcXG4gIHRyYW5zaXRpb246IGxlZnQgMC41cyBlYXNlO1xcclxcbn1cXHJcXG5cXHJcXG4uYnRuLXNoaW5lOmhvdmVyOjphZnRlciB7XFxyXFxuICBsZWZ0OiAxMDAlO1xcclxcbn1cXHJcXG5cXHJcXG4vKiBEb3QgcGF0dGVybiAqL1xcclxcbi5kb3QtcGF0dGVybiB7XFxyXFxuICBiYWNrZ3JvdW5kLWltYWdlOiByYWRpYWwtZ3JhZGllbnQocmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpIDFweCwgdHJhbnNwYXJlbnQgMXB4KTtcXHJcXG4gIGJhY2tncm91bmQtc2l6ZTogMjBweCAyMHB4O1xcclxcbiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1pbWFnZSB2YXIoLS10aGVtZS10cmFuc2l0aW9uKTtcXHJcXG59XFxyXFxuXFxyXFxuLmxpZ2h0IC5kb3QtcGF0dGVybiB7XFxyXFxuICBiYWNrZ3JvdW5kLWltYWdlOiByYWRpYWwtZ3JhZGllbnQocmdiYSgwLCAwLCAwLCAwLjEpIDFweCwgdHJhbnNwYXJlbnQgMXB4KTtcXHJcXG59XFxyXFxuXFxyXFxuLyogRmxvdyBib3JkZXIgZWZmZWN0ICovXFxyXFxuLmZsb3ctYm9yZGVyIHtcXHJcXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcXHJcXG4gIHotaW5kZXg6IDA7XFxyXFxufVxcclxcblxcclxcbi5mbG93LWJvcmRlcjo6YmVmb3JlIHtcXHJcXG4gIGNvbnRlbnQ6IFxcXCJcXFwiO1xcclxcbiAgcG9zaXRpb246IGFic29sdXRlO1xcclxcbiAgdG9wOiAtMnB4O1xcclxcbiAgbGVmdDogLTJweDtcXHJcXG4gIHJpZ2h0OiAtMnB4O1xcclxcbiAgYm90dG9tOiAtMnB4O1xcclxcbiAgYmFja2dyb3VuZDogdmFyKFxcclxcbiAgICAtLWJnLWZsb3ctYm9yZGVyLFxcclxcbiAgICBsaW5lYXItZ3JhZGllbnQoXFxyXFxuICAgICAgOTBkZWcsXFxyXFxuICAgICAgdmFyKC0tYm9yZGVyLXN0YXJ0LWNvbG9yKSxcXHJcXG4gICAgICB2YXIoLS1ib3JkZXItbWlkLWNvbG9yKSxcXHJcXG4gICAgICB2YXIoLS1ib3JkZXItZW5kLWNvbG9yKSxcXHJcXG4gICAgICB2YXIoLS1ib3JkZXItc3RhcnQtY29sb3IpXFxyXFxuICAgIClcXHJcXG4gICk7XFxyXFxuICBib3JkZXItcmFkaXVzOiBpbmhlcml0O1xcclxcbiAgei1pbmRleDogLTE7XFxyXFxuICBiYWNrZ3JvdW5kLXNpemU6IDMwMCUgMTAwJTtcXHJcXG4gIGFuaW1hdGlvbjogYm9yZGVyLWZsb3cgM3MgZWFzZSBpbmZpbml0ZTtcXHJcXG4gIG9wYWNpdHk6IDA7XFxyXFxuICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuM3MgZWFzZTtcXHJcXG59XFxyXFxuXFxyXFxuLmZsb3ctYm9yZGVyOmhvdmVyOjpiZWZvcmUge1xcclxcbiAgb3BhY2l0eTogMTtcXHJcXG59XFxyXFxuXFxyXFxuLyogR2xvdyBlZmZlY3QgKi9cXHJcXG4uZ2xvdy1lZmZlY3Qge1xcclxcbiAgdHJhbnNpdGlvbjogYm94LXNoYWRvdyAwLjNzIGVhc2U7XFxyXFxufVxcclxcblxcclxcbi5nbG93LWVmZmVjdDpob3ZlciB7XFxyXFxuICAtLWdsb3ctY29sb3I6IHZhcigtLXRoZW1lLWJsdWUtcmdiLCAzMywgMTUwLCAyNDMpO1xcclxcbiAgYm94LXNoYWRvdzogMCAwIDEwcHggMnB4IHJnYmEodmFyKC0tZ2xvdy1jb2xvciksIDAuMyk7XFxyXFxufVxcclxcblxcclxcbi5nbG93LWVmZmVjdC5wdXJwbGU6aG92ZXIge1xcclxcbiAgLS1nbG93LWNvbG9yOiB2YXIoLS10aGVtZS1wdXJwbGUtcmdiLCAxNTYsIDM5LCAxNzYpO1xcclxcbn1cXHJcXG5cXHJcXG4uZ2xvdy1lZmZlY3QudGVhbDpob3ZlciB7XFxyXFxuICAtLWdsb3ctY29sb3I6IHZhcigtLXRoZW1lLXRlYWwtcmdiLCAwLCAxODgsIDIxMik7XFxyXFxufVxcclxcblxcclxcbi8qIFRoZW1lIHRyYW5zaXRpb24gKi9cXHJcXG4udGhlbWUtdHJhbnNpdGlvbiB7XFxyXFxuICB0cmFuc2l0aW9uOiBhbGwgdmFyKC0tdGhlbWUtdHJhbnNpdGlvbik7XFxyXFxufVxcclxcblxcclxcbi8qIFBhZ2UgdHJhbnNpdGlvbiAqL1xcclxcbi5wYWdlLXRyYW5zaXRpb24tZW50ZXIge1xcclxcbiAgb3BhY2l0eTogMDtcXHJcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgxMHB4KTtcXHJcXG59XFxyXFxuXFxyXFxuLnBhZ2UtdHJhbnNpdGlvbi1lbnRlci1hY3RpdmUge1xcclxcbiAgb3BhY2l0eTogMTtcXHJcXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcXHJcXG4gIHRyYW5zaXRpb246IG9wYWNpdHkgMC4zcywgdHJhbnNmb3JtIDAuM3M7XFxyXFxufVxcclxcblxcclxcbi5wYWdlLXRyYW5zaXRpb24tZXhpdCB7XFxyXFxuICBvcGFjaXR5OiAxO1xcclxcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xcclxcbn1cXHJcXG5cXHJcXG4ucGFnZS10cmFuc2l0aW9uLWV4aXQtYWN0aXZlIHtcXHJcXG4gIG9wYWNpdHk6IDA7XFxyXFxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTEwcHgpO1xcclxcbiAgdHJhbnNpdGlvbjogb3BhY2l0eSAwLjNzLCB0cmFuc2Zvcm0gMC4zcztcXHJcXG59XFxyXFxuXFxyXFxuLyogTmF2IGxpbmsgd2l0aCBpbmRpY2F0b3IgKi9cXHJcXG4ubmF2LWxpbmsge1xcclxcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xcclxcbn1cXHJcXG5cXHJcXG4ubmF2LWxpbms6OmFmdGVyIHtcXHJcXG4gIGNvbnRlbnQ6IFxcXCJcXFwiO1xcclxcbiAgcG9zaXRpb246IGFic29sdXRlO1xcclxcbiAgYm90dG9tOiAtNHB4O1xcclxcbiAgbGVmdDogMDtcXHJcXG4gIHdpZHRoOiAwO1xcclxcbiAgaGVpZ2h0OiAycHg7XFxyXFxuICBiYWNrZ3JvdW5kLWNvbG9yOiBoc2wodmFyKC0tdGhlbWUtYmx1ZSkpO1xcclxcbiAgdHJhbnNpdGlvbjogd2lkdGggMC4zcyBlYXNlO1xcclxcbn1cXHJcXG5cXHJcXG4ubmF2LWxpbms6aG92ZXI6OmFmdGVyLFxcclxcbi5uYXYtbGluay5hY3RpdmU6OmFmdGVyIHtcXHJcXG4gIHdpZHRoOiAxMDAlO1xcclxcbn1cXHJcXG5cXHJcXG4vKiBDdXN0b20gc2Nyb2xsYmFyICovXFxyXFxuOjotd2Via2l0LXNjcm9sbGJhciB7XFxyXFxuICB3aWR0aDogOHB4O1xcclxcbiAgaGVpZ2h0OiA4cHg7XFxyXFxufVxcclxcblxcclxcbjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sge1xcclxcbiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XFxyXFxufVxcclxcblxcclxcbjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xcclxcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xcclxcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xcclxcbn1cXHJcXG5cXHJcXG46Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHtcXHJcXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcXHJcXG59XFxyXFxuXFxyXFxuLmxpZ2h0IDo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xcclxcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjEpO1xcclxcbn1cXHJcXG5cXHJcXG4ubGlnaHQgOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7XFxyXFxuICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMik7XFxyXFxufVxcclxcblxcclxcbi8qIEFuaW1hdGlvbiBmb3IgbG9hZGluZyBzcGlubmVyICovXFxyXFxuQGtleWZyYW1lcyBib3JkZXItZmxvdyB7XFxyXFxuICAwJSB7XFxyXFxuICAgIGJhY2tncm91bmQtcG9zaXRpb246IDAlIDUwJTtcXHJcXG4gIH1cXHJcXG4gIDUwJSB7XFxyXFxuICAgIGJhY2tncm91bmQtcG9zaXRpb246IDEwMCUgNTAlO1xcclxcbiAgfVxcclxcbiAgMTAwJSB7XFxyXFxuICAgIGJhY2tncm91bmQtcG9zaXRpb246IDAlIDUwJTtcXHJcXG4gIH1cXHJcXG59XFxyXFxuXFxyXFxuQGtleWZyYW1lcyBzdWJ0bGUtcHVsc2Uge1xcclxcbiAgMCUge1xcclxcbiAgICBvcGFjaXR5OiAwLjc7XFxyXFxuICB9XFxyXFxuICA1MCUge1xcclxcbiAgICBvcGFjaXR5OiAxO1xcclxcbiAgfVxcclxcbiAgMTAwJSB7XFxyXFxuICAgIG9wYWNpdHk6IDAuNztcXHJcXG4gIH1cXHJcXG59XFxyXFxuXFxyXFxuLmFuaW1hdGUtc3VidGxlLXB1bHNlIHtcXHJcXG4gIGFuaW1hdGlvbjogc3VidGxlLXB1bHNlIDJzIGVhc2UtaW4tb3V0IGluZmluaXRlO1xcclxcbn1cXHJcXG5cXHJcXG4uYW5pbWF0ZS1yb3RhdGUtbG9hZGVyIHtcXHJcXG4gIGFuaW1hdGlvbjogc3BpbiAxcyBsaW5lYXIgaW5maW5pdGU7XFxyXFxufVxcclxcblxcclxcbkBrZXlmcmFtZXMgc3BpbiB7XFxyXFxuICBmcm9tIHtcXHJcXG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7XFxyXFxuICB9XFxyXFxuICB0byB7XFxyXFxuICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7XFxyXFxuICB9XFxyXFxufVxcclxcblxcclxcblxcclxcblxcclxcblxcclxcblxcclxcblxcclxcblxcclxcblwiXSxcInNvdXJjZVJvb3RcIjpcIlwifV0pO1xuLy8gRXhwb3J0c1xuZXhwb3J0IGRlZmF1bHQgX19fQ1NTX0xPQURFUl9FWFBPUlRfX187XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});