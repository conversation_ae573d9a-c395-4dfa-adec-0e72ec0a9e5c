"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\r\\n  :root {\\r\\n    --background: 0 0% 100%;\\r\\n    --foreground: 240 10% 3.9%;\\r\\n    --card: 0 0% 100%;\\r\\n    --card-foreground: 240 10% 3.9%;\\r\\n    --popover: 0 0% 100%;\\r\\n    --popover-foreground: 240 10% 3.9%;\\r\\n    --primary: 240 5.9% 10%;\\r\\n    --primary-foreground: 0 0% 98%;\\r\\n    --secondary: 240 4.8% 95.9%;\\r\\n    --secondary-foreground: 240 5.9% 10%;\\r\\n    --muted: 240 4.8% 95.9%;\\r\\n    --muted-foreground: 240 3.8% 46.1%;\\r\\n    --accent: 240 4.8% 95.9%;\\r\\n    --accent-foreground: 240 5.9% 10%;\\r\\n    --destructive: 0 84.2% 60.2%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 5.9% 90%;\\r\\n    --input: 240 5.9% 90%;\\r\\n    --ring: 240 5.9% 10%;\\r\\n    --radius: 0.5rem;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 50%;\\r\\n    --theme-purple: 260 100% 60%;\\r\\n    --theme-teal: 180 100% 40%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 50%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.5);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.5);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.5);\\r\\n\\r\\n    /* Theme transition */\\r\\n    --theme-transition: 0.3s ease;\\r\\n  }\\r\\n\\r\\n  .dark {\\r\\n    --background: 240 10% 3.9%;\\r\\n    --foreground: 0 0% 98%;\\r\\n    --card: 240 10% 3.9%;\\r\\n    --card-foreground: 0 0% 98%;\\r\\n    --popover: 240 10% 3.9%;\\r\\n    --popover-foreground: 0 0% 98%;\\r\\n    --primary: 0 0% 98%;\\r\\n    --primary-foreground: 240 5.9% 10%;\\r\\n    --secondary: 240 3.7% 15.9%;\\r\\n    --secondary-foreground: 0 0% 98%;\\r\\n    --muted: 240 3.7% 15.9%;\\r\\n    --muted-foreground: 240 5% 64.9%;\\r\\n    --accent: 240 3.7% 15.9%;\\r\\n    --accent-foreground: 0 0% 98%;\\r\\n    --destructive: 0 62.8% 30.6%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 3.7% 15.9%;\\r\\n    --input: 240 3.7% 15.9%;\\r\\n    --ring: 240 4.9% 83.9%;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 60%;\\r\\n    --theme-purple: 260 100% 70%;\\r\\n    --theme-teal: 180 100% 50%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 60%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.7);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.7);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.7);\\r\\n  }\\r\\n  * {\\n  border-color: hsl(var(--border));\\n}\\r\\n  body {\\n  background-color: hsl(var(--background));\\n  color: hsl(var(--foreground));\\r\\n    font-feature-settings: \\\"rlig\\\" 1, \\\"calt\\\" 1;\\r\\n    transition: background-color var(--theme-transition), color var(--theme-transition);\\n}\\r\\n.container {\\n  width: 100%;\\n  margin-right: auto;\\n  margin-left: auto;\\n  padding-right: 2rem;\\n  padding-left: 2rem;\\n}\\r\\n@media (min-width: 1400px) {\\n\\n  .container {\\n    max-width: 1400px;\\n  }\\n}\\r\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\r\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\r\\n.visible {\\n  visibility: visible;\\n}\\r\\n.fixed {\\n  position: fixed;\\n}\\r\\n.absolute {\\n  position: absolute;\\n}\\r\\n.relative {\\n  position: relative;\\n}\\r\\n.inset-0 {\\n  inset: 0px;\\n}\\r\\n.-top-1 {\\n  top: -0.25rem;\\n}\\r\\n.bottom-2 {\\n  bottom: 0.5rem;\\n}\\r\\n.bottom-20 {\\n  bottom: 5rem;\\n}\\r\\n.bottom-40 {\\n  bottom: 10rem;\\n}\\r\\n.bottom-full {\\n  bottom: 100%;\\n}\\r\\n.left-0 {\\n  left: 0px;\\n}\\r\\n.left-1\\\\/2 {\\n  left: 50%;\\n}\\r\\n.left-1\\\\/4 {\\n  left: 25%;\\n}\\r\\n.left-10 {\\n  left: 2.5rem;\\n}\\r\\n.left-2 {\\n  left: 0.5rem;\\n}\\r\\n.left-\\\\[50\\\\%\\\\] {\\n  left: 50%;\\n}\\r\\n.right-0 {\\n  right: 0px;\\n}\\r\\n.right-10 {\\n  right: 2.5rem;\\n}\\r\\n.right-2 {\\n  right: 0.5rem;\\n}\\r\\n.right-20 {\\n  right: 5rem;\\n}\\r\\n.right-4 {\\n  right: 1rem;\\n}\\r\\n.top-0 {\\n  top: 0px;\\n}\\r\\n.top-20 {\\n  top: 5rem;\\n}\\r\\n.top-4 {\\n  top: 1rem;\\n}\\r\\n.top-40 {\\n  top: 10rem;\\n}\\r\\n.top-8 {\\n  top: 2rem;\\n}\\r\\n.top-\\\\[50\\\\%\\\\] {\\n  top: 50%;\\n}\\r\\n.-top-3 {\\n  top: -0.75rem;\\n}\\r\\n.isolate {\\n  isolation: isolate;\\n}\\r\\n.-z-10 {\\n  z-index: -10;\\n}\\r\\n.z-0 {\\n  z-index: 0;\\n}\\r\\n.z-10 {\\n  z-index: 10;\\n}\\r\\n.z-40 {\\n  z-index: 40;\\n}\\r\\n.z-50 {\\n  z-index: 50;\\n}\\r\\n.z-20 {\\n  z-index: 20;\\n}\\r\\n.col-span-1 {\\n  grid-column: span 1 / span 1;\\n}\\r\\n.-mx-1 {\\n  margin-left: -0.25rem;\\n  margin-right: -0.25rem;\\n}\\r\\n.mx-1 {\\n  margin-left: 0.25rem;\\n  margin-right: 0.25rem;\\n}\\r\\n.mx-2 {\\n  margin-left: 0.5rem;\\n  margin-right: 0.5rem;\\n}\\r\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\r\\n.my-1 {\\n  margin-top: 0.25rem;\\n  margin-bottom: 0.25rem;\\n}\\r\\n.my-4 {\\n  margin-top: 1rem;\\n  margin-bottom: 1rem;\\n}\\r\\n.my-6 {\\n  margin-top: 1.5rem;\\n  margin-bottom: 1.5rem;\\n}\\r\\n.-ml-1 {\\n  margin-left: -0.25rem;\\n}\\r\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\r\\n.mb-12 {\\n  margin-bottom: 3rem;\\n}\\r\\n.mb-16 {\\n  margin-bottom: 4rem;\\n}\\r\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\r\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\r\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\r\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\r\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\r\\n.ml-1 {\\n  margin-left: 0.25rem;\\n}\\r\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\r\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\r\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\r\\n.mt-0 {\\n  margin-top: 0px;\\n}\\r\\n.mt-0\\\\.5 {\\n  margin-top: 0.125rem;\\n}\\r\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\r\\n.mt-12 {\\n  margin-top: 3rem;\\n}\\r\\n.mt-16 {\\n  margin-top: 4rem;\\n}\\r\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\r\\n.mt-24 {\\n  margin-top: 6rem;\\n}\\r\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\r\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\r\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\r\\n.mt-\\\\[-150px\\\\] {\\n  margin-top: -150px;\\n}\\r\\n.mt-auto {\\n  margin-top: auto;\\n}\\r\\n.block {\\n  display: block;\\n}\\r\\n.inline-block {\\n  display: inline-block;\\n}\\r\\n.flex {\\n  display: flex;\\n}\\r\\n.inline-flex {\\n  display: inline-flex;\\n}\\r\\n.table {\\n  display: table;\\n}\\r\\n.grid {\\n  display: grid;\\n}\\r\\n.hidden {\\n  display: none;\\n}\\r\\n.aspect-square {\\n  aspect-ratio: 1 / 1;\\n}\\r\\n.aspect-video {\\n  aspect-ratio: 16 / 9;\\n}\\r\\n.h-0 {\\n  height: 0px;\\n}\\r\\n.h-1 {\\n  height: 0.25rem;\\n}\\r\\n.h-10 {\\n  height: 2.5rem;\\n}\\r\\n.h-11 {\\n  height: 2.75rem;\\n}\\r\\n.h-12 {\\n  height: 3rem;\\n}\\r\\n.h-16 {\\n  height: 4rem;\\n}\\r\\n.h-2 {\\n  height: 0.5rem;\\n}\\r\\n.h-2\\\\.5 {\\n  height: 0.625rem;\\n}\\r\\n.h-20 {\\n  height: 5rem;\\n}\\r\\n.h-24 {\\n  height: 6rem;\\n}\\r\\n.h-3 {\\n  height: 0.75rem;\\n}\\r\\n.h-3\\\\.5 {\\n  height: 0.875rem;\\n}\\r\\n.h-32 {\\n  height: 8rem;\\n}\\r\\n.h-4 {\\n  height: 1rem;\\n}\\r\\n.h-40 {\\n  height: 10rem;\\n}\\r\\n.h-5 {\\n  height: 1.25rem;\\n}\\r\\n.h-6 {\\n  height: 1.5rem;\\n}\\r\\n.h-7 {\\n  height: 1.75rem;\\n}\\r\\n.h-8 {\\n  height: 2rem;\\n}\\r\\n.h-9 {\\n  height: 2.25rem;\\n}\\r\\n.h-\\\\[1px\\\\] {\\n  height: 1px;\\n}\\r\\n.h-\\\\[var\\\\(--radix-select-trigger-height\\\\)\\\\] {\\n  height: var(--radix-select-trigger-height);\\n}\\r\\n.h-auto {\\n  height: auto;\\n}\\r\\n.h-full {\\n  height: 100%;\\n}\\r\\n.h-px {\\n  height: 1px;\\n}\\r\\n.min-h-\\\\[100px\\\\] {\\n  min-height: 100px;\\n}\\r\\n.min-h-\\\\[150px\\\\] {\\n  min-height: 150px;\\n}\\r\\n.min-h-\\\\[80px\\\\] {\\n  min-height: 80px;\\n}\\r\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\r\\n.min-h-\\\\[400px\\\\] {\\n  min-height: 400px;\\n}\\r\\n.w-0 {\\n  width: 0px;\\n}\\r\\n.w-10 {\\n  width: 2.5rem;\\n}\\r\\n.w-12 {\\n  width: 3rem;\\n}\\r\\n.w-16 {\\n  width: 4rem;\\n}\\r\\n.w-2 {\\n  width: 0.5rem;\\n}\\r\\n.w-2\\\\.5 {\\n  width: 0.625rem;\\n}\\r\\n.w-20 {\\n  width: 5rem;\\n}\\r\\n.w-24 {\\n  width: 6rem;\\n}\\r\\n.w-3 {\\n  width: 0.75rem;\\n}\\r\\n.w-3\\\\.5 {\\n  width: 0.875rem;\\n}\\r\\n.w-32 {\\n  width: 8rem;\\n}\\r\\n.w-4 {\\n  width: 1rem;\\n}\\r\\n.w-48 {\\n  width: 12rem;\\n}\\r\\n.w-5 {\\n  width: 1.25rem;\\n}\\r\\n.w-6 {\\n  width: 1.5rem;\\n}\\r\\n.w-8 {\\n  width: 2rem;\\n}\\r\\n.w-\\\\[1px\\\\] {\\n  width: 1px;\\n}\\r\\n.w-fit {\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\r\\n.w-full {\\n  width: 100%;\\n}\\r\\n.min-w-\\\\[8rem\\\\] {\\n  min-width: 8rem;\\n}\\r\\n.min-w-\\\\[var\\\\(--radix-select-trigger-width\\\\)\\\\] {\\n  min-width: var(--radix-select-trigger-width);\\n}\\r\\n.max-w-2xl {\\n  max-width: 42rem;\\n}\\r\\n.max-w-3xl {\\n  max-width: 48rem;\\n}\\r\\n.max-w-4xl {\\n  max-width: 56rem;\\n}\\r\\n.max-w-5xl {\\n  max-width: 64rem;\\n}\\r\\n.max-w-lg {\\n  max-width: 32rem;\\n}\\r\\n.max-w-md {\\n  max-width: 28rem;\\n}\\r\\n.max-w-xs {\\n  max-width: 20rem;\\n}\\r\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\r\\n.shrink-0 {\\n  flex-shrink: 0;\\n}\\r\\n.flex-grow {\\n  flex-grow: 1;\\n}\\r\\n.grow {\\n  flex-grow: 1;\\n}\\r\\n.border-collapse {\\n  border-collapse: collapse;\\n}\\r\\n.-translate-x-1\\\\/2 {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-\\\\[-50\\\\%\\\\] {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-1 {\\n  --tw-translate-y: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-\\\\[-50\\\\%\\\\] {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-0 {\\n  --tw-rotate: 0deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-105 {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n@keyframes gradient-x {\\n\\n  0%, 100% {\\n    background-position: 0% 50%;\\n  }\\n\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n}\\r\\n.animate-gradient-x {\\n  animation: gradient-x 3s ease infinite;\\n}\\r\\n@keyframes pulse {\\n\\n  50% {\\n    opacity: .5;\\n  }\\n}\\r\\n.animate-pulse {\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\r\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\r\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\r\\n.cursor-default {\\n  cursor: default;\\n}\\r\\n.cursor-help {\\n  cursor: help;\\n}\\r\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\r\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\r\\n.touch-none {\\n  touch-action: none;\\n}\\r\\n.select-none {\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n}\\r\\n.resize-none {\\n  resize: none;\\n}\\r\\n.resize {\\n  resize: both;\\n}\\r\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\r\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\r\\n.flex-col {\\n  flex-direction: column;\\n}\\r\\n.flex-col-reverse {\\n  flex-direction: column-reverse;\\n}\\r\\n.flex-wrap {\\n  flex-wrap: wrap;\\n}\\r\\n.items-start {\\n  align-items: flex-start;\\n}\\r\\n.items-end {\\n  align-items: flex-end;\\n}\\r\\n.items-center {\\n  align-items: center;\\n}\\r\\n.justify-center {\\n  justify-content: center;\\n}\\r\\n.justify-between {\\n  justify-content: space-between;\\n}\\r\\n.gap-1 {\\n  gap: 0.25rem;\\n}\\r\\n.gap-2 {\\n  gap: 0.5rem;\\n}\\r\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\r\\n.gap-4 {\\n  gap: 1rem;\\n}\\r\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\r\\n.gap-8 {\\n  gap: 2rem;\\n}\\r\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-1\\\\.5 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\r\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\r\\n.overflow-x-auto {\\n  overflow-x: auto;\\n}\\r\\n.truncate {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\r\\n.whitespace-nowrap {\\n  white-space: nowrap;\\n}\\r\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\r\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\r\\n.rounded-lg {\\n  border-radius: var(--radius);\\n}\\r\\n.rounded-md {\\n  border-radius: calc(var(--radius) - 2px);\\n}\\r\\n.rounded-sm {\\n  border-radius: calc(var(--radius) - 4px);\\n}\\r\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\r\\n.border {\\n  border-width: 1px;\\n}\\r\\n.border-2 {\\n  border-width: 2px;\\n}\\r\\n.border-8 {\\n  border-width: 8px;\\n}\\r\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\r\\n.border-b-2 {\\n  border-bottom-width: 2px;\\n}\\r\\n.border-b-\\\\[86px\\\\] {\\n  border-bottom-width: 86px;\\n}\\r\\n.border-l-\\\\[50px\\\\] {\\n  border-left-width: 50px;\\n}\\r\\n.border-r-\\\\[50px\\\\] {\\n  border-right-width: 50px;\\n}\\r\\n.border-t {\\n  border-top-width: 1px;\\n}\\r\\n.border-t-2 {\\n  border-top-width: 2px;\\n}\\r\\n.border-dashed {\\n  border-style: dashed;\\n}\\r\\n.border-amber-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-amber-900\\\\/50 {\\n  border-color: rgb(120 53 15 / 0.5);\\n}\\r\\n.border-black\\\\/50 {\\n  border-color: rgb(0 0 0 / 0.5);\\n}\\r\\n.border-border {\\n  border-color: hsl(var(--border));\\n}\\r\\n.border-destructive {\\n  border-color: hsl(var(--destructive));\\n}\\r\\n.border-destructive\\\\/50 {\\n  border-color: hsl(var(--destructive) / 0.5);\\n}\\r\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-gray-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-gray-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-green-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-green-900\\\\/50 {\\n  border-color: rgb(20 83 45 / 0.5);\\n}\\r\\n.border-input {\\n  border-color: hsl(var(--input));\\n}\\r\\n.border-muted-foreground {\\n  border-color: hsl(var(--muted-foreground));\\n}\\r\\n.border-muted-foreground\\\\/25 {\\n  border-color: hsl(var(--muted-foreground) / 0.25);\\n}\\r\\n.border-primary {\\n  border-color: hsl(var(--primary));\\n}\\r\\n.border-red-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-red-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-red-900\\\\/50 {\\n  border-color: rgb(127 29 29 / 0.5);\\n}\\r\\n.border-theme-blue {\\n  border-color: hsl(var(--theme-blue));\\n}\\r\\n.border-theme-blue\\\\/20 {\\n  border-color: hsl(var(--theme-blue) / 0.2);\\n}\\r\\n.border-theme-purple\\\\/30 {\\n  border-color: hsl(var(--theme-purple) / 0.3);\\n}\\r\\n.border-theme-teal\\\\/30 {\\n  border-color: hsl(var(--theme-teal) / 0.3);\\n}\\r\\n.border-white\\\\/10 {\\n  border-color: rgb(255 255 255 / 0.1);\\n}\\r\\n.border-white\\\\/20 {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n.border-white\\\\/50 {\\n  border-color: rgb(255 255 255 / 0.5);\\n}\\r\\n.border-theme-purple\\\\/50 {\\n  border-color: hsl(var(--theme-purple) / 0.5);\\n}\\r\\n.border-b-theme-teal\\\\/30 {\\n  border-bottom-color: hsl(var(--theme-teal) / 0.3);\\n}\\r\\n.border-l-transparent {\\n  border-left-color: transparent;\\n}\\r\\n.border-r-transparent {\\n  border-right-color: transparent;\\n}\\r\\n.bg-amber-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-amber-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-amber-500\\\\/10 {\\n  background-color: rgb(245 158 11 / 0.1);\\n}\\r\\n.bg-amber-900\\\\/20 {\\n  background-color: rgb(120 53 15 / 0.2);\\n}\\r\\n.bg-background {\\n  background-color: hsl(var(--background));\\n}\\r\\n.bg-background\\\\/50 {\\n  background-color: hsl(var(--background) / 0.5);\\n}\\r\\n.bg-background\\\\/80 {\\n  background-color: hsl(var(--background) / 0.8);\\n}\\r\\n.bg-background\\\\/95 {\\n  background-color: hsl(var(--background) / 0.95);\\n}\\r\\n.bg-black\\\\/20 {\\n  background-color: rgb(0 0 0 / 0.2);\\n}\\r\\n.bg-black\\\\/80 {\\n  background-color: rgb(0 0 0 / 0.8);\\n}\\r\\n.bg-border {\\n  background-color: hsl(var(--border));\\n}\\r\\n.bg-card {\\n  background-color: hsl(var(--card));\\n}\\r\\n.bg-destructive {\\n  background-color: hsl(var(--destructive));\\n}\\r\\n.bg-destructive\\\\/10 {\\n  background-color: hsl(var(--destructive) / 0.1);\\n}\\r\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-300 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-700 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-800 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-green-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-green-500\\\\/20 {\\n  background-color: rgb(34 197 94 / 0.2);\\n}\\r\\n.bg-green-900\\\\/20 {\\n  background-color: rgb(20 83 45 / 0.2);\\n}\\r\\n.bg-muted {\\n  background-color: hsl(var(--muted));\\n}\\r\\n.bg-popover {\\n  background-color: hsl(var(--popover));\\n}\\r\\n.bg-primary {\\n  background-color: hsl(var(--primary));\\n}\\r\\n.bg-primary\\\\/20 {\\n  background-color: hsl(var(--primary) / 0.2);\\n}\\r\\n.bg-red-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-red-500\\\\/10 {\\n  background-color: rgb(239 68 68 / 0.1);\\n}\\r\\n.bg-red-500\\\\/20 {\\n  background-color: rgb(239 68 68 / 0.2);\\n}\\r\\n.bg-red-900\\\\/20 {\\n  background-color: rgb(127 29 29 / 0.2);\\n}\\r\\n.bg-red-950\\\\/20 {\\n  background-color: rgb(69 10 10 / 0.2);\\n}\\r\\n.bg-red-950\\\\/80 {\\n  background-color: rgb(69 10 10 / 0.8);\\n}\\r\\n.bg-secondary {\\n  background-color: hsl(var(--secondary));\\n}\\r\\n.bg-theme-blue {\\n  background-color: hsl(var(--theme-blue));\\n}\\r\\n.bg-theme-blue\\\\/10 {\\n  background-color: hsl(var(--theme-blue) / 0.1);\\n}\\r\\n.bg-theme-blue\\\\/20 {\\n  background-color: hsl(var(--theme-blue) / 0.2);\\n}\\r\\n.bg-theme-blue\\\\/5 {\\n  background-color: hsl(var(--theme-blue) / 0.05);\\n}\\r\\n.bg-theme-purple {\\n  background-color: hsl(var(--theme-purple));\\n}\\r\\n.bg-theme-purple\\\\/10 {\\n  background-color: hsl(var(--theme-purple) / 0.1);\\n}\\r\\n.bg-theme-purple\\\\/20 {\\n  background-color: hsl(var(--theme-purple) / 0.2);\\n}\\r\\n.bg-theme-teal\\\\/10 {\\n  background-color: hsl(var(--theme-teal) / 0.1);\\n}\\r\\n.bg-transparent {\\n  background-color: transparent;\\n}\\r\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-white\\\\/10 {\\n  background-color: rgb(255 255 255 / 0.1);\\n}\\r\\n.bg-white\\\\/5 {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\r\\n.bg-gradient-to-b {\\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\\n}\\r\\n.bg-gradient-to-br {\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\r\\n.bg-gradient-to-r {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\r\\n.from-amber-500 {\\n  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-gray-50 {\\n  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-gray-900 {\\n  --tw-gradient-from: #111827 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-green-500 {\\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-blue {\\n  --tw-gradient-from: hsl(var(--theme-blue)) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-blue) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-blue\\\\/20 {\\n  --tw-gradient-from: hsl(var(--theme-blue) / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-blue) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-purple {\\n  --tw-gradient-from: hsl(var(--theme-purple)) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-purple) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-purple\\\\/5 {\\n  --tw-gradient-from: hsl(var(--theme-purple) / 0.05) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-purple) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.via-purple-600 {\\n  --tw-gradient-to: rgb(147 51 234 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #9333ea var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.to-black {\\n  --tw-gradient-to: #000 var(--tw-gradient-to-position);\\n}\\r\\n.to-emerald-600 {\\n  --tw-gradient-to: #059669 var(--tw-gradient-to-position);\\n}\\r\\n.to-gray-800 {\\n  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);\\n}\\r\\n.to-orange-600 {\\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-500 {\\n  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-500\\\\/20 {\\n  --tw-gradient-to: rgb(168 85 247 / 0.2) var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-600 {\\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\\n}\\r\\n.to-theme-blue {\\n  --tw-gradient-to: hsl(var(--theme-blue)) var(--tw-gradient-to-position);\\n}\\r\\n.to-theme-purple {\\n  --tw-gradient-to: hsl(var(--theme-purple)) var(--tw-gradient-to-position);\\n}\\r\\n.to-white {\\n  --tw-gradient-to: #fff var(--tw-gradient-to-position);\\n}\\r\\n.to-theme-blue\\\\/5 {\\n  --tw-gradient-to: hsl(var(--theme-blue) / 0.05) var(--tw-gradient-to-position);\\n}\\r\\n.bg-\\\\[length\\\\:200\\\\%_100\\\\%\\\\] {\\n  background-size: 200% 100%;\\n}\\r\\n.bg-clip-text {\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n}\\r\\n.fill-current {\\n  fill: currentColor;\\n}\\r\\n.object-cover {\\n  -o-object-fit: cover;\\n     object-fit: cover;\\n}\\r\\n.p-1 {\\n  padding: 0.25rem;\\n}\\r\\n.p-2 {\\n  padding: 0.5rem;\\n}\\r\\n.p-3 {\\n  padding: 0.75rem;\\n}\\r\\n.p-4 {\\n  padding: 1rem;\\n}\\r\\n.p-5 {\\n  padding: 1.25rem;\\n}\\r\\n.p-6 {\\n  padding: 1.5rem;\\n}\\r\\n.p-8 {\\n  padding: 2rem;\\n}\\r\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\r\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\r\\n.px-8 {\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\r\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\r\\n.py-1\\\\.5 {\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\r\\n.py-10 {\\n  padding-top: 2.5rem;\\n  padding-bottom: 2.5rem;\\n}\\r\\n.py-12 {\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\r\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\r\\n.py-20 {\\n  padding-top: 5rem;\\n  padding-bottom: 5rem;\\n}\\r\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\r\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\r\\n.py-6 {\\n  padding-top: 1.5rem;\\n  padding-bottom: 1.5rem;\\n}\\r\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\r\\n.pb-2 {\\n  padding-bottom: 0.5rem;\\n}\\r\\n.pb-24 {\\n  padding-bottom: 6rem;\\n}\\r\\n.pl-8 {\\n  padding-left: 2rem;\\n}\\r\\n.pr-16 {\\n  padding-right: 4rem;\\n}\\r\\n.pr-2 {\\n  padding-right: 0.5rem;\\n}\\r\\n.pt-0 {\\n  padding-top: 0px;\\n}\\r\\n.pt-2 {\\n  padding-top: 0.5rem;\\n}\\r\\n.pt-6 {\\n  padding-top: 1.5rem;\\n}\\r\\n.text-left {\\n  text-align: left;\\n}\\r\\n.text-center {\\n  text-align: center;\\n}\\r\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\r\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\r\\n.text-4xl {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\r\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\r\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\r\\n.font-bold {\\n  font-weight: 700;\\n}\\r\\n.font-medium {\\n  font-weight: 500;\\n}\\r\\n.font-semibold {\\n  font-weight: 600;\\n}\\r\\n.leading-none {\\n  line-height: 1;\\n}\\r\\n.tracking-tight {\\n  letter-spacing: -0.025em;\\n}\\r\\n.text-amber-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 191 36 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-amber-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(245 158 11 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-amber-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(217 119 6 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-black {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-card-foreground {\\n  color: hsl(var(--card-foreground));\\n}\\r\\n.text-current {\\n  color: currentColor;\\n}\\r\\n.text-destructive {\\n  color: hsl(var(--destructive));\\n}\\r\\n.text-destructive-foreground {\\n  color: hsl(var(--destructive-foreground));\\n}\\r\\n.text-foreground {\\n  color: hsl(var(--foreground));\\n}\\r\\n.text-gray-200 {\\n  --tw-text-opacity: 1;\\n  color: rgb(229 231 235 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-green-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-green-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-muted-foreground {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n.text-popover-foreground {\\n  color: hsl(var(--popover-foreground));\\n}\\r\\n.text-primary {\\n  color: hsl(var(--primary));\\n}\\r\\n.text-primary-foreground {\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n.text-red-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-red-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-secondary-foreground {\\n  color: hsl(var(--secondary-foreground));\\n}\\r\\n.text-theme-blue {\\n  color: hsl(var(--theme-blue));\\n}\\r\\n.text-theme-purple {\\n  color: hsl(var(--theme-purple));\\n}\\r\\n.text-theme-teal {\\n  color: hsl(var(--theme-teal));\\n}\\r\\n.text-transparent {\\n  color: transparent;\\n}\\r\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n.underline-offset-4 {\\n  text-underline-offset: 4px;\\n}\\r\\n.opacity-0 {\\n  opacity: 0;\\n}\\r\\n.opacity-25 {\\n  opacity: 0.25;\\n}\\r\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\r\\n.opacity-70 {\\n  opacity: 0.7;\\n}\\r\\n.opacity-75 {\\n  opacity: 0.75;\\n}\\r\\n.opacity-80 {\\n  opacity: 0.8;\\n}\\r\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-md {\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-xl {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-2xl {\\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-theme-purple\\\\/10 {\\n  --tw-shadow-color: hsl(var(--theme-purple) / 0.1);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\r\\n.shadow-theme-purple\\\\/20 {\\n  --tw-shadow-color: hsl(var(--theme-purple) / 0.2);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\r\\n.outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n.outline {\\n  outline-style: solid;\\n}\\r\\n.ring-offset-background {\\n  --tw-ring-offset-color: hsl(var(--background));\\n}\\r\\n.blur {\\n  --tw-blur: blur(8px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.backdrop-blur-md {\\n  --tw-backdrop-blur: blur(12px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-opacity {\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-transform {\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\r\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\r\\n.duration-500 {\\n  transition-duration: 500ms;\\n}\\r\\n.will-change-\\\\[transform\\\\2c filter\\\\2c opacity\\\\] {\\n  will-change: transform,filter,opacity;\\n}\\r\\n@keyframes enter {\\n\\n  from {\\n    opacity: var(--tw-enter-opacity, 1);\\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\\n  }\\n}\\r\\n@keyframes exit {\\n\\n  to {\\n    opacity: var(--tw-exit-opacity, 1);\\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\\n  }\\n}\\r\\n.animate-in {\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n.fade-in-80 {\\n  --tw-enter-opacity: 0.8;\\n}\\r\\n.duration-200 {\\n  animation-duration: 200ms;\\n}\\r\\n.duration-300 {\\n  animation-duration: 300ms;\\n}\\r\\n.duration-500 {\\n  animation-duration: 500ms;\\n}\\r\\n\\r\\n/* Subtle glass effect */\\r\\n.subtle-glass {\\r\\n  background: rgba(255, 255, 255, 0.03);\\r\\n  backdrop-filter: blur(8px);\\r\\n  -webkit-backdrop-filter: blur(8px);\\r\\n  border: 1px solid rgba(255, 255, 255, 0.05);\\r\\n  transition: background-color var(--theme-transition), border-color var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-glass {\\r\\n  background: rgba(0, 0, 0, 0.03);\\r\\n  border: 1px solid rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle border glow */\\r\\n.subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05);\\r\\n  transition: box-shadow var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle hover effect */\\r\\n.subtle-hover {\\r\\n  transition: all 0.2s ease;\\r\\n}\\r\\n\\r\\n.subtle-hover:hover {\\r\\n  background-color: rgba(255, 255, 255, 0.05);\\r\\n}\\r\\n\\r\\n.light .subtle-hover:hover {\\r\\n  background-color: rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Gradient text */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n  transition: background var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .gradient-text {\\r\\n  background: linear-gradient(to right, #000, rgba(0, 0, 0, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n}\\r\\n\\r\\n/* Subtle grid background */\\r\\n.subtle-grid {\\r\\n  background-size: 30px 30px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-grid {\\r\\n  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),\\r\\n    linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Reveal animation classes */\\r\\n.reveal {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n  transition: opacity 0.5s ease, transform 0.5s ease;\\r\\n}\\r\\n\\r\\n.reveal.active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n/* Button shine effect */\\r\\n.btn-shine {\\r\\n  position: relative;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.btn-shine::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: -100%;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-image: linear-gradient(\\r\\n    90deg,\\r\\n    rgba(255, 255, 255, 0) 0%,\\r\\n    rgba(255, 255, 255, 0.05) 50%,\\r\\n    rgba(255, 255, 255, 0) 100%\\r\\n  );\\r\\n  transition: left 0.5s ease;\\r\\n}\\r\\n\\r\\n.btn-shine:hover::after {\\r\\n  left: 100%;\\r\\n}\\r\\n\\r\\n/* Dot pattern */\\r\\n.dot-pattern {\\r\\n  background-image: radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .dot-pattern {\\r\\n  background-image: radial-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Flow border effect */\\r\\n.flow-border {\\r\\n  position: relative;\\r\\n  z-index: 0;\\r\\n}\\r\\n\\r\\n.flow-border::before {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: -2px;\\r\\n  left: -2px;\\r\\n  right: -2px;\\r\\n  bottom: -2px;\\r\\n  background: var(\\r\\n    --bg-flow-border,\\r\\n    linear-gradient(\\r\\n      90deg,\\r\\n      var(--border-start-color),\\r\\n      var(--border-mid-color),\\r\\n      var(--border-end-color),\\r\\n      var(--border-start-color)\\r\\n    )\\r\\n  );\\r\\n  border-radius: inherit;\\r\\n  z-index: -1;\\r\\n  background-size: 300% 100%;\\r\\n  animation: border-flow 3s ease infinite;\\r\\n  opacity: 0;\\r\\n  transition: opacity 0.3s ease;\\r\\n}\\r\\n\\r\\n.flow-border:hover::before {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n/* Glow effect */\\r\\n.glow-effect {\\r\\n  transition: box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\n.glow-effect:hover {\\r\\n  --glow-color: var(--theme-blue-rgb, 33, 150, 243);\\r\\n  box-shadow: 0 0 10px 2px rgba(var(--glow-color), 0.3);\\r\\n}\\r\\n\\r\\n.glow-effect.purple:hover {\\r\\n  --glow-color: var(--theme-purple-rgb, 156, 39, 176);\\r\\n}\\r\\n\\r\\n.glow-effect.teal:hover {\\r\\n  --glow-color: var(--theme-teal-rgb, 0, 188, 212);\\r\\n}\\r\\n\\r\\n/* Theme transition */\\r\\n.theme-transition {\\r\\n  transition: all var(--theme-transition);\\r\\n}\\r\\n\\r\\n/* Page transition */\\r\\n.page-transition-enter {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n}\\r\\n\\r\\n.page-transition-enter-active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n.page-transition-exit {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n.page-transition-exit-active {\\r\\n  opacity: 0;\\r\\n  transform: translateY(-10px);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n/* Nav link with indicator */\\r\\n.nav-link {\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.nav-link::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  bottom: -4px;\\r\\n  left: 0;\\r\\n  width: 0;\\r\\n  height: 2px;\\r\\n  background-color: hsl(var(--theme-blue));\\r\\n  transition: width 0.3s ease;\\r\\n}\\r\\n\\r\\n.nav-link:hover::after,\\r\\n.nav-link.active::after {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n  height: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: transparent;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: rgba(255, 255, 255, 0.1);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(255, 255, 255, 0.2);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb {\\r\\n  background: rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(0, 0, 0, 0.2);\\r\\n}\\r\\n\\r\\n/* Animation for loading spinner */\\r\\n@keyframes border-flow {\\r\\n  0% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n  100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes subtle-pulse {\\r\\n  0% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 1;\\r\\n  }\\r\\n  100% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-subtle-pulse {\\r\\n  animation: subtle-pulse 2s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-rotate-loader {\\r\\n  animation: spin 1s linear infinite;\\r\\n}\\r\\n\\r\\n@keyframes spin {\\r\\n  from {\\r\\n    transform: rotate(0deg);\\r\\n  }\\r\\n  to {\\r\\n    transform: rotate(360deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n.placeholder\\\\:text-muted-foreground::-moz-placeholder {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.placeholder\\\\:text-muted-foreground::placeholder {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.hover\\\\:scale-105:hover {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.hover\\\\:border-black\\\\/20:hover {\\n  border-color: rgb(0 0 0 / 0.2);\\n}\\r\\n\\r\\n.hover\\\\:border-black\\\\/70:hover {\\n  border-color: rgb(0 0 0 / 0.7);\\n}\\r\\n\\r\\n.hover\\\\:border-gray-400:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:border-theme-blue\\\\/50:hover {\\n  border-color: hsl(var(--theme-blue) / 0.5);\\n}\\r\\n\\r\\n.hover\\\\:border-white\\\\/20:hover {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n\\r\\n.hover\\\\:border-white\\\\/30:hover {\\n  border-color: rgb(255 255 255 / 0.3);\\n}\\r\\n\\r\\n.hover\\\\:bg-accent:hover {\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.hover\\\\:bg-black\\\\/5:hover {\\n  background-color: rgb(0 0 0 / 0.05);\\n}\\r\\n\\r\\n.hover\\\\:bg-destructive\\\\/10:hover {\\n  background-color: hsl(var(--destructive) / 0.1);\\n}\\r\\n\\r\\n.hover\\\\:bg-destructive\\\\/90:hover {\\n  background-color: hsl(var(--destructive) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-primary\\\\/10:hover {\\n  background-color: hsl(var(--primary) / 0.1);\\n}\\r\\n\\r\\n.hover\\\\:bg-primary\\\\/90:hover {\\n  background-color: hsl(var(--primary) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-secondary\\\\/80:hover {\\n  background-color: hsl(var(--secondary) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:bg-theme-blue\\\\/10:hover {\\n  background-color: hsl(var(--theme-blue) / 0.1);\\n}\\r\\n\\r\\n.hover\\\\:bg-theme-purple\\\\/90:hover {\\n  background-color: hsl(var(--theme-purple) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-white\\\\/5:hover {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\r\\n\\r\\n.hover\\\\:bg-white\\\\/90:hover {\\n  background-color: rgb(255 255 255 / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:text-accent-foreground:hover {\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-black:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:text-foreground:hover {\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-primary\\\\/80:hover {\\n  color: hsl(var(--primary) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:text-theme-blue:hover {\\n  color: hsl(var(--theme-blue));\\n}\\r\\n\\r\\n.hover\\\\:text-white:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:underline:hover {\\n  text-decoration-line: underline;\\n}\\r\\n\\r\\n.hover\\\\:opacity-100:hover {\\n  opacity: 1;\\n}\\r\\n\\r\\n.hover\\\\:opacity-90:hover {\\n  opacity: 0.9;\\n}\\r\\n\\r\\n.hover\\\\:shadow-lg:hover {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.focus\\\\:border-primary:focus {\\n  border-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.focus\\\\:border-theme-blue:focus {\\n  border-color: hsl(var(--theme-blue));\\n}\\r\\n\\r\\n.focus\\\\:bg-accent:focus {\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.focus\\\\:text-accent-foreground:focus {\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus\\\\:ring-primary:focus {\\n  --tw-ring-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.focus\\\\:ring-ring:focus {\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n\\r\\n.focus\\\\:ring-theme-blue\\\\/50:focus {\\n  --tw-ring-color: hsl(var(--theme-blue) / 0.5);\\n}\\r\\n\\r\\n.focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:outline-none:focus-visible {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-2:focus-visible {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-ring:focus-visible {\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-offset-2:focus-visible {\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n\\r\\n.disabled\\\\:pointer-events-none:disabled {\\n  pointer-events: none;\\n}\\r\\n\\r\\n.disabled\\\\:cursor-not-allowed:disabled {\\n  cursor: not-allowed;\\n}\\r\\n\\r\\n.disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:opacity-100 {\\n  opacity: 1;\\n}\\r\\n\\r\\n.peer:disabled ~ .peer-disabled\\\\:cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\r\\n\\r\\n.peer:disabled ~ .peer-disabled\\\\:opacity-70 {\\n  opacity: 0.7;\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\]\\\\:pointer-events-none[data-disabled] {\\n  pointer-events: none;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:bg-background[data-state=\\\"active\\\"] {\\n  background-color: hsl(var(--background));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:bg-theme-blue[data-state=\\\"active\\\"] {\\n  background-color: hsl(var(--theme-blue));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:bg-accent[data-state=\\\"open\\\"] {\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:text-foreground[data-state=\\\"active\\\"] {\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:text-white[data-state=\\\"active\\\"] {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:text-muted-foreground[data-state=\\\"open\\\"] {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\]\\\\:opacity-50[data-disabled] {\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:shadow-sm[data-state=\\\"active\\\"] {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:animate-in[data-state=\\\"open\\\"] {\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:animate-out[data-state=\\\"closed\\\"] {\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-0[data-state=\\\"closed\\\"] {\\n  --tw-exit-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:fade-in-0[data-state=\\\"open\\\"] {\\n  --tw-enter-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:zoom-out-95[data-state=\\\"closed\\\"] {\\n  --tw-exit-scale: .95;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-95[data-state=\\\"open\\\"] {\\n  --tw-enter-scale: .95;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left-1\\\\/2[data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-x: -50%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top-\\\\[48\\\\%\\\\][data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-y: -48%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left-1\\\\/2[data-state=\\\"open\\\"] {\\n  --tw-enter-translate-x: -50%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top-\\\\[48\\\\%\\\\][data-state=\\\"open\\\"] {\\n  --tw-enter-translate-y: -48%;\\n}\\r\\n\\r\\n.dark\\\\:border-destructive:is(.dark *) {\\n  border-color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.dark\\\\:border-white\\\\/20:is(.dark *) {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n\\r\\n.dark\\\\:text-white:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:border-white\\\\/20:hover:is(.dark *) {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:border-white\\\\/40:hover:is(.dark *) {\\n  border-color: rgb(255 255 255 / 0.4);\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:bg-white\\\\/5:hover:is(.dark *) {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:text-white:hover:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .sm\\\\:max-w-\\\\[600px\\\\] {\\n    max-width: 600px;\\n  }\\n\\n  .sm\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:justify-end {\\n    justify-content: flex-end;\\n  }\\n\\n  .sm\\\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\n\\n  .sm\\\\:rounded-lg {\\n    border-radius: var(--radius);\\n  }\\n\\n  .sm\\\\:text-left {\\n    text-align: left;\\n  }\\n}\\r\\n\\r\\n@media (min-width: 768px) {\\n\\n  .md\\\\:mb-0 {\\n    margin-bottom: 0px;\\n  }\\n\\n  .md\\\\:mt-\\\\[-180px\\\\] {\\n    margin-top: -180px;\\n  }\\n\\n  .md\\\\:mt-0 {\\n    margin-top: 0px;\\n  }\\n\\n  .md\\\\:mt-6 {\\n    margin-top: 1.5rem;\\n  }\\n\\n  .md\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .md\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .md\\\\:gap-8 {\\n    gap: 2rem;\\n  }\\n\\n  .md\\\\:p-6 {\\n    padding: 1.5rem;\\n  }\\n\\n  .md\\\\:py-12 {\\n    padding-top: 3rem;\\n    padding-bottom: 3rem;\\n  }\\n\\n  .md\\\\:py-16 {\\n    padding-top: 4rem;\\n    padding-bottom: 4rem;\\n  }\\n\\n  .md\\\\:py-24 {\\n    padding-top: 6rem;\\n    padding-bottom: 6rem;\\n  }\\n\\n  .md\\\\:py-32 {\\n    padding-top: 8rem;\\n    padding-bottom: 8rem;\\n  }\\n\\n  .md\\\\:text-2xl {\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n\\n  .md\\\\:text-3xl {\\n    font-size: 1.875rem;\\n    line-height: 2.25rem;\\n  }\\n\\n  .md\\\\:text-4xl {\\n    font-size: 2.25rem;\\n    line-height: 2.5rem;\\n  }\\n\\n  .md\\\\:text-5xl {\\n    font-size: 3rem;\\n    line-height: 1;\\n  }\\n\\n  .md\\\\:text-6xl {\\n    font-size: 3.75rem;\\n    line-height: 1;\\n  }\\n\\n  .md\\\\:text-xl {\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n}\\r\\n\\r\\n@media (min-width: 1024px) {\\n\\n  .lg\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-5 {\\n    grid-template-columns: repeat(5, minmax(0, 1fr));\\n  }\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\+div\\\\]\\\\:translate-y-\\\\[-3px\\\\]>svg+div {\\n  --tw-translate-y: -3px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:absolute>svg {\\n  position: absolute;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:left-4>svg {\\n  left: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:top-4>svg {\\n  top: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-destructive>svg {\\n  color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-foreground>svg {\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\~\\\\*\\\\]\\\\:pl-7>svg~* {\\n  padding-left: 1.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_p\\\\]\\\\:leading-relaxed p {\\n  line-height: 1.625;\\n}\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;EAAd;IAAA,uBAAc;IAAd,0BAAc;IAAd,iBAAc;IAAd,+BAAc;IAAd,oBAAc;IAAd,kCAAc;IAAd,uBAAc;IAAd,8BAAc;IAAd,2BAAc;IAAd,oCAAc;IAAd,uBAAc;IAAd,kCAAc;IAAd,wBAAc;IAAd,iCAAc;IAAd,4BAAc;IAAd,kCAAc;IAAd,sBAAc;IAAd,qBAAc;IAAd,oBAAc;IAAd,gBAAc;;IAAd,iBAAc;IAAd,0BAAc;IAAd,4BAAc;IAAd,0BAAc;;IAAd,gBAAc;IAAd,4BAAc;;IAAd,uBAAc;IAAd,6CAAc;IAAd,2CAAc;IAAd,0CAAc;;IAAd,qBAAc;IAAd,6BAAc;EAAA;;EAAd;IAAA,0BAAc;IAAd,sBAAc;IAAd,oBAAc;IAAd,2BAAc;IAAd,uBAAc;IAAd,8BAAc;IAAd,mBAAc;IAAd,kCAAc;IAAd,2BAAc;IAAd,gCAAc;IAAd,uBAAc;IAAd,gCAAc;IAAd,wBAAc;IAAd,6BAAc;IAAd,4BAAc;IAAd,kCAAc;IAAd,wBAAc;IAAd,uBAAc;IAAd,sBAAc;;IAAd,iBAAc;IAAd,0BAAc;IAAd,4BAAc;IAAd,0BAAc;;IAAd,gBAAc;IAAd,4BAAc;;IAAd,uBAAc;IAAd,6CAAc;IAAd,2CAAc;IAAd,0CAAc;EAAA;EAAd;EAAA;AAAc;EAAd;EAAA,wCAAc;EAAd,6BAAc;IAAd,yCAAc;IAAd;AAAc;AACd;EAAA,WAAoB;EAApB,kBAAoB;EAApB,iBAAoB;EAApB,mBAAoB;EAApB;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AACpB;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,yBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;;EAAnB;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yBAAmB;KAAnB,sBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,gEAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,2EAAmB;EAAnB;AAAmB;AAAnB;EAAA,iFAAmB;EAAnB,2EAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,6EAAmB;EAAnB;AAAmB;AAAnB;EAAA,oFAAmB;EAAnB,6EAAmB;EAAnB;AAAmB;AAAnB;EAAA,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;KAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,iDAAmB;EAAnB;AAAmB;AAAnB;EAAA,iDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA,mCAAmB;IAAnB;EAAmB;AAAA;AAAnB;;EAAA;IAAA,kCAAmB;IAAnB;EAAmB;AAAA;AAAnB;EAAA,qBAAmB;EAAnB,yBAAmB;EAAnB,2BAAmB;EAAnB,yBAAmB;EAAnB,0BAAmB;EAAnB,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAyFnB,wBAAwB;AACxB;EACE,qCAAqC;EACrC,0BAA0B;EAC1B,kCAAkC;EAClC,2CAA2C;EAC3C,0FAA0F;AAC5F;;AAEA;EACE,+BAA+B;EAC/B,qCAAqC;AACvC;;AAEA,uBAAuB;AACvB;EACE,+CAA+C;EAC/C,8CAA8C;AAChD;;AAEA;EACE,yCAAyC;AAC3C;;AAEA,wBAAwB;AACxB;EACE,yBAAyB;AAC3B;;AAEA;EACE,2CAA2C;AAC7C;;AAEA;EACE,qCAAqC;AACvC;;AAEA,kBAAkB;AAClB;EACE,sEAAsE;EACtE,6BAA6B;EAC7B,oCAAoC;EACpC,qBAAqB;EACrB,8CAA8C;AAChD;;AAEA;EACE,gEAAgE;EAChE,6BAA6B;EAC7B,oCAAoC;EACpC,qBAAqB;AACvB;;AAEA,2BAA2B;AAC3B;EACE,0BAA0B;EAC1B,oDAAoD;AACtD;;AAEA;EACE;wEACsE;AACxE;;AAEA,6BAA6B;AAC7B;EACE,UAAU;EACV,2BAA2B;EAC3B,kDAAkD;AACpD;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA,wBAAwB;AACxB;EACE,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,WAAW;EACX,WAAW;EACX,YAAY;EACZ;;;;;GAKC;EACD,0BAA0B;AAC5B;;AAEA;EACE,UAAU;AACZ;;AAEA,gBAAgB;AAChB;EACE,gFAAgF;EAChF,0BAA0B;EAC1B,oDAAoD;AACtD;;AAEA;EACE,0EAA0E;AAC5E;;AAEA,uBAAuB;AACvB;EACE,kBAAkB;EAClB,UAAU;AACZ;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,SAAS;EACT,UAAU;EACV,WAAW;EACX,YAAY;EACZ;;;;;;;;;GASC;EACD,sBAAsB;EACtB,WAAW;EACX,0BAA0B;EAC1B,uCAAuC;EACvC,UAAU;EACV,6BAA6B;AAC/B;;AAEA;EACE,UAAU;AACZ;;AAEA,gBAAgB;AAChB;EACE,gCAAgC;AAClC;;AAEA;EACE,iDAAiD;EACjD,qDAAqD;AACvD;;AAEA;EACE,mDAAmD;AACrD;;AAEA;EACE,gDAAgD;AAClD;;AAEA,qBAAqB;AACrB;EACE,uCAAuC;AACzC;;AAEA,oBAAoB;AACpB;EACE,UAAU;EACV,2BAA2B;AAC7B;;AAEA;EACE,UAAU;EACV,wBAAwB;EACxB,wCAAwC;AAC1C;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA;EACE,UAAU;EACV,4BAA4B;EAC5B,wCAAwC;AAC1C;;AAEA,4BAA4B;AAC5B;EACE,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,YAAY;EACZ,OAAO;EACP,QAAQ;EACR,WAAW;EACX,wCAAwC;EACxC,2BAA2B;AAC7B;;AAEA;;EAEE,WAAW;AACb;;AAEA,qBAAqB;AACrB;EACE,UAAU;EACV,WAAW;AACb;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,8BAA8B;AAChC;;AAEA,kCAAkC;AAClC;EACE;IACE,2BAA2B;EAC7B;EACA;IACE,6BAA6B;EAC/B;EACA;IACE,2BAA2B;EAC7B;AACF;;AAEA;EACE;IACE,YAAY;EACd;EACA;IACE,UAAU;EACZ;EACA;IACE,YAAY;EACd;AACF;;AAEA;EACE,+CAA+C;AACjD;;AAEA;EACE,kCAAkC;AACpC;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,yBAAyB;EAC3B;AACF;;AArXA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,kBA6XA;EA7XA,kBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,sBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,kBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,+EA6XA;EA7XA,mGA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,8BA6XA;EA7XA;AA6XA;;AA7XA;EAAA,2GA6XA;EA7XA,yGA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,8BA6XA;EA7XA;AA6XA;;AA7XA;EAAA,2GA6XA;EA7XA,yGA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,0CA6XA;EA7XA,uDA6XA;EA7XA;AA6XA;;AA7XA;EAAA,qBA6XA;EA7XA,yBA6XA;EA7XA,2BA6XA;EA7XA,yBA6XA;EA7XA,0BA6XA;EA7XA,+BA6XA;EA7XA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA,yBA6XA;EA7XA,0BA6XA;EA7XA,wBA6XA;EA7XA,yBA6XA;EA7XA,8BA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;;EAAA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA,uBA6XA;IA7XA,sDA6XA;IA7XA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;AAAA;;AA7XA;;EAAA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,mBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,kBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,eA6XA;IA7XA;EA6XA;;EA7XA;IAAA,kBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,kBA6XA;IA7XA;EA6XA;AAAA;;AA7XA;;EAAA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;AAAA;;AA7XA;EAAA,sBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA\",\"sourcesContent\":[\"@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n\\r\\n@layer base {\\r\\n  :root {\\r\\n    --background: 0 0% 100%;\\r\\n    --foreground: 240 10% 3.9%;\\r\\n    --card: 0 0% 100%;\\r\\n    --card-foreground: 240 10% 3.9%;\\r\\n    --popover: 0 0% 100%;\\r\\n    --popover-foreground: 240 10% 3.9%;\\r\\n    --primary: 240 5.9% 10%;\\r\\n    --primary-foreground: 0 0% 98%;\\r\\n    --secondary: 240 4.8% 95.9%;\\r\\n    --secondary-foreground: 240 5.9% 10%;\\r\\n    --muted: 240 4.8% 95.9%;\\r\\n    --muted-foreground: 240 3.8% 46.1%;\\r\\n    --accent: 240 4.8% 95.9%;\\r\\n    --accent-foreground: 240 5.9% 10%;\\r\\n    --destructive: 0 84.2% 60.2%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 5.9% 90%;\\r\\n    --input: 240 5.9% 90%;\\r\\n    --ring: 240 5.9% 10%;\\r\\n    --radius: 0.5rem;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 50%;\\r\\n    --theme-purple: 260 100% 60%;\\r\\n    --theme-teal: 180 100% 40%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 50%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.5);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.5);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.5);\\r\\n\\r\\n    /* Theme transition */\\r\\n    --theme-transition: 0.3s ease;\\r\\n  }\\r\\n\\r\\n  .dark {\\r\\n    --background: 240 10% 3.9%;\\r\\n    --foreground: 0 0% 98%;\\r\\n    --card: 240 10% 3.9%;\\r\\n    --card-foreground: 0 0% 98%;\\r\\n    --popover: 240 10% 3.9%;\\r\\n    --popover-foreground: 0 0% 98%;\\r\\n    --primary: 0 0% 98%;\\r\\n    --primary-foreground: 240 5.9% 10%;\\r\\n    --secondary: 240 3.7% 15.9%;\\r\\n    --secondary-foreground: 0 0% 98%;\\r\\n    --muted: 240 3.7% 15.9%;\\r\\n    --muted-foreground: 240 5% 64.9%;\\r\\n    --accent: 240 3.7% 15.9%;\\r\\n    --accent-foreground: 0 0% 98%;\\r\\n    --destructive: 0 62.8% 30.6%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 3.7% 15.9%;\\r\\n    --input: 240 3.7% 15.9%;\\r\\n    --ring: 240 4.9% 83.9%;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 60%;\\r\\n    --theme-purple: 260 100% 70%;\\r\\n    --theme-teal: 180 100% 50%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 60%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.7);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.7);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.7);\\r\\n  }\\r\\n}\\r\\n\\r\\n@layer base {\\r\\n  * {\\r\\n    @apply border-border;\\r\\n  }\\r\\n  body {\\r\\n    @apply bg-background text-foreground;\\r\\n    font-feature-settings: \\\"rlig\\\" 1, \\\"calt\\\" 1;\\r\\n    transition: background-color var(--theme-transition), color var(--theme-transition);\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Subtle glass effect */\\r\\n.subtle-glass {\\r\\n  background: rgba(255, 255, 255, 0.03);\\r\\n  backdrop-filter: blur(8px);\\r\\n  -webkit-backdrop-filter: blur(8px);\\r\\n  border: 1px solid rgba(255, 255, 255, 0.05);\\r\\n  transition: background-color var(--theme-transition), border-color var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-glass {\\r\\n  background: rgba(0, 0, 0, 0.03);\\r\\n  border: 1px solid rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle border glow */\\r\\n.subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05);\\r\\n  transition: box-shadow var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle hover effect */\\r\\n.subtle-hover {\\r\\n  transition: all 0.2s ease;\\r\\n}\\r\\n\\r\\n.subtle-hover:hover {\\r\\n  background-color: rgba(255, 255, 255, 0.05);\\r\\n}\\r\\n\\r\\n.light .subtle-hover:hover {\\r\\n  background-color: rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Gradient text */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n  transition: background var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .gradient-text {\\r\\n  background: linear-gradient(to right, #000, rgba(0, 0, 0, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n}\\r\\n\\r\\n/* Subtle grid background */\\r\\n.subtle-grid {\\r\\n  background-size: 30px 30px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-grid {\\r\\n  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),\\r\\n    linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Reveal animation classes */\\r\\n.reveal {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n  transition: opacity 0.5s ease, transform 0.5s ease;\\r\\n}\\r\\n\\r\\n.reveal.active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n/* Button shine effect */\\r\\n.btn-shine {\\r\\n  position: relative;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.btn-shine::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: -100%;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-image: linear-gradient(\\r\\n    90deg,\\r\\n    rgba(255, 255, 255, 0) 0%,\\r\\n    rgba(255, 255, 255, 0.05) 50%,\\r\\n    rgba(255, 255, 255, 0) 100%\\r\\n  );\\r\\n  transition: left 0.5s ease;\\r\\n}\\r\\n\\r\\n.btn-shine:hover::after {\\r\\n  left: 100%;\\r\\n}\\r\\n\\r\\n/* Dot pattern */\\r\\n.dot-pattern {\\r\\n  background-image: radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .dot-pattern {\\r\\n  background-image: radial-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Flow border effect */\\r\\n.flow-border {\\r\\n  position: relative;\\r\\n  z-index: 0;\\r\\n}\\r\\n\\r\\n.flow-border::before {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: -2px;\\r\\n  left: -2px;\\r\\n  right: -2px;\\r\\n  bottom: -2px;\\r\\n  background: var(\\r\\n    --bg-flow-border,\\r\\n    linear-gradient(\\r\\n      90deg,\\r\\n      var(--border-start-color),\\r\\n      var(--border-mid-color),\\r\\n      var(--border-end-color),\\r\\n      var(--border-start-color)\\r\\n    )\\r\\n  );\\r\\n  border-radius: inherit;\\r\\n  z-index: -1;\\r\\n  background-size: 300% 100%;\\r\\n  animation: border-flow 3s ease infinite;\\r\\n  opacity: 0;\\r\\n  transition: opacity 0.3s ease;\\r\\n}\\r\\n\\r\\n.flow-border:hover::before {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n/* Glow effect */\\r\\n.glow-effect {\\r\\n  transition: box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\n.glow-effect:hover {\\r\\n  --glow-color: var(--theme-blue-rgb, 33, 150, 243);\\r\\n  box-shadow: 0 0 10px 2px rgba(var(--glow-color), 0.3);\\r\\n}\\r\\n\\r\\n.glow-effect.purple:hover {\\r\\n  --glow-color: var(--theme-purple-rgb, 156, 39, 176);\\r\\n}\\r\\n\\r\\n.glow-effect.teal:hover {\\r\\n  --glow-color: var(--theme-teal-rgb, 0, 188, 212);\\r\\n}\\r\\n\\r\\n/* Theme transition */\\r\\n.theme-transition {\\r\\n  transition: all var(--theme-transition);\\r\\n}\\r\\n\\r\\n/* Page transition */\\r\\n.page-transition-enter {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n}\\r\\n\\r\\n.page-transition-enter-active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n.page-transition-exit {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n.page-transition-exit-active {\\r\\n  opacity: 0;\\r\\n  transform: translateY(-10px);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n/* Nav link with indicator */\\r\\n.nav-link {\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.nav-link::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  bottom: -4px;\\r\\n  left: 0;\\r\\n  width: 0;\\r\\n  height: 2px;\\r\\n  background-color: hsl(var(--theme-blue));\\r\\n  transition: width 0.3s ease;\\r\\n}\\r\\n\\r\\n.nav-link:hover::after,\\r\\n.nav-link.active::after {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n  height: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: transparent;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: rgba(255, 255, 255, 0.1);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(255, 255, 255, 0.2);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb {\\r\\n  background: rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(0, 0, 0, 0.2);\\r\\n}\\r\\n\\r\\n/* Animation for loading spinner */\\r\\n@keyframes border-flow {\\r\\n  0% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n  100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes subtle-pulse {\\r\\n  0% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 1;\\r\\n  }\\r\\n  100% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-subtle-pulse {\\r\\n  animation: subtle-pulse 2s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-rotate-loader {\\r\\n  animation: spin 1s linear infinite;\\r\\n}\\r\\n\\r\\n@keyframes spin {\\r\\n  from {\\r\\n    transform: rotate(0deg);\\r\\n  }\\r\\n  to {\\r\\n    transform: rotate(360deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});