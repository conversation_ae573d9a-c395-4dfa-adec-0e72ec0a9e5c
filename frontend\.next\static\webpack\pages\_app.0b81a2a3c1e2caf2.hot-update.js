"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\r\\n  :root {\\r\\n    --background: 0 0% 100%;\\r\\n    --foreground: 240 10% 3.9%;\\r\\n    --card: 0 0% 100%;\\r\\n    --card-foreground: 240 10% 3.9%;\\r\\n    --popover: 0 0% 100%;\\r\\n    --popover-foreground: 240 10% 3.9%;\\r\\n    --primary: 240 5.9% 10%;\\r\\n    --primary-foreground: 0 0% 98%;\\r\\n    --secondary: 240 4.8% 95.9%;\\r\\n    --secondary-foreground: 240 5.9% 10%;\\r\\n    --muted: 240 4.8% 95.9%;\\r\\n    --muted-foreground: 240 3.8% 46.1%;\\r\\n    --accent: 240 4.8% 95.9%;\\r\\n    --accent-foreground: 240 5.9% 10%;\\r\\n    --destructive: 0 84.2% 60.2%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 5.9% 90%;\\r\\n    --input: 240 5.9% 90%;\\r\\n    --ring: 240 5.9% 10%;\\r\\n    --radius: 0.5rem;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 50%;\\r\\n    --theme-purple: 260 100% 60%;\\r\\n    --theme-teal: 180 100% 40%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 50%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.5);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.5);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.5);\\r\\n\\r\\n    /* Theme transition */\\r\\n    --theme-transition: 0.3s ease;\\r\\n  }\\r\\n\\r\\n  .dark {\\r\\n    --background: 240 10% 3.9%;\\r\\n    --foreground: 0 0% 98%;\\r\\n    --card: 240 10% 3.9%;\\r\\n    --card-foreground: 0 0% 98%;\\r\\n    --popover: 240 10% 3.9%;\\r\\n    --popover-foreground: 0 0% 98%;\\r\\n    --primary: 0 0% 98%;\\r\\n    --primary-foreground: 240 5.9% 10%;\\r\\n    --secondary: 240 3.7% 15.9%;\\r\\n    --secondary-foreground: 0 0% 98%;\\r\\n    --muted: 240 3.7% 15.9%;\\r\\n    --muted-foreground: 240 5% 64.9%;\\r\\n    --accent: 240 3.7% 15.9%;\\r\\n    --accent-foreground: 0 0% 98%;\\r\\n    --destructive: 0 62.8% 30.6%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 3.7% 15.9%;\\r\\n    --input: 240 3.7% 15.9%;\\r\\n    --ring: 240 4.9% 83.9%;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 60%;\\r\\n    --theme-purple: 260 100% 70%;\\r\\n    --theme-teal: 180 100% 50%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 60%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.7);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.7);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.7);\\r\\n  }\\r\\n  * {\\n  border-color: hsl(var(--border));\\n}\\r\\n  body {\\n  background-color: hsl(var(--background));\\n  color: hsl(var(--foreground));\\r\\n    font-feature-settings: \\\"rlig\\\" 1, \\\"calt\\\" 1;\\r\\n    transition: background-color var(--theme-transition), color var(--theme-transition);\\n}\\r\\n.container {\\n  width: 100%;\\n  margin-right: auto;\\n  margin-left: auto;\\n  padding-right: 2rem;\\n  padding-left: 2rem;\\n}\\r\\n@media (min-width: 1400px) {\\n\\n  .container {\\n    max-width: 1400px;\\n  }\\n}\\r\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\r\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\r\\n.visible {\\n  visibility: visible;\\n}\\r\\n.fixed {\\n  position: fixed;\\n}\\r\\n.absolute {\\n  position: absolute;\\n}\\r\\n.relative {\\n  position: relative;\\n}\\r\\n.inset-0 {\\n  inset: 0px;\\n}\\r\\n.-top-1 {\\n  top: -0.25rem;\\n}\\r\\n.bottom-2 {\\n  bottom: 0.5rem;\\n}\\r\\n.bottom-20 {\\n  bottom: 5rem;\\n}\\r\\n.bottom-40 {\\n  bottom: 10rem;\\n}\\r\\n.bottom-full {\\n  bottom: 100%;\\n}\\r\\n.left-0 {\\n  left: 0px;\\n}\\r\\n.left-1\\\\/2 {\\n  left: 50%;\\n}\\r\\n.left-1\\\\/4 {\\n  left: 25%;\\n}\\r\\n.left-10 {\\n  left: 2.5rem;\\n}\\r\\n.left-2 {\\n  left: 0.5rem;\\n}\\r\\n.left-\\\\[50\\\\%\\\\] {\\n  left: 50%;\\n}\\r\\n.right-0 {\\n  right: 0px;\\n}\\r\\n.right-10 {\\n  right: 2.5rem;\\n}\\r\\n.right-2 {\\n  right: 0.5rem;\\n}\\r\\n.right-20 {\\n  right: 5rem;\\n}\\r\\n.right-4 {\\n  right: 1rem;\\n}\\r\\n.top-0 {\\n  top: 0px;\\n}\\r\\n.top-20 {\\n  top: 5rem;\\n}\\r\\n.top-4 {\\n  top: 1rem;\\n}\\r\\n.top-40 {\\n  top: 10rem;\\n}\\r\\n.top-8 {\\n  top: 2rem;\\n}\\r\\n.top-\\\\[50\\\\%\\\\] {\\n  top: 50%;\\n}\\r\\n.-top-3 {\\n  top: -0.75rem;\\n}\\r\\n.-z-10 {\\n  z-index: -10;\\n}\\r\\n.z-0 {\\n  z-index: 0;\\n}\\r\\n.z-10 {\\n  z-index: 10;\\n}\\r\\n.z-40 {\\n  z-index: 40;\\n}\\r\\n.z-50 {\\n  z-index: 50;\\n}\\r\\n.col-span-1 {\\n  grid-column: span 1 / span 1;\\n}\\r\\n.-mx-1 {\\n  margin-left: -0.25rem;\\n  margin-right: -0.25rem;\\n}\\r\\n.mx-1 {\\n  margin-left: 0.25rem;\\n  margin-right: 0.25rem;\\n}\\r\\n.mx-2 {\\n  margin-left: 0.5rem;\\n  margin-right: 0.5rem;\\n}\\r\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\r\\n.my-1 {\\n  margin-top: 0.25rem;\\n  margin-bottom: 0.25rem;\\n}\\r\\n.my-4 {\\n  margin-top: 1rem;\\n  margin-bottom: 1rem;\\n}\\r\\n.my-6 {\\n  margin-top: 1.5rem;\\n  margin-bottom: 1.5rem;\\n}\\r\\n.-ml-1 {\\n  margin-left: -0.25rem;\\n}\\r\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\r\\n.mb-12 {\\n  margin-bottom: 3rem;\\n}\\r\\n.mb-16 {\\n  margin-bottom: 4rem;\\n}\\r\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\r\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\r\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\r\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\r\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\r\\n.ml-1 {\\n  margin-left: 0.25rem;\\n}\\r\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\r\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\r\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\r\\n.mt-0 {\\n  margin-top: 0px;\\n}\\r\\n.mt-0\\\\.5 {\\n  margin-top: 0.125rem;\\n}\\r\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\r\\n.mt-12 {\\n  margin-top: 3rem;\\n}\\r\\n.mt-16 {\\n  margin-top: 4rem;\\n}\\r\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\r\\n.mt-24 {\\n  margin-top: 6rem;\\n}\\r\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\r\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\r\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\r\\n.mt-\\\\[-150px\\\\] {\\n  margin-top: -150px;\\n}\\r\\n.mt-auto {\\n  margin-top: auto;\\n}\\r\\n.block {\\n  display: block;\\n}\\r\\n.inline-block {\\n  display: inline-block;\\n}\\r\\n.flex {\\n  display: flex;\\n}\\r\\n.inline-flex {\\n  display: inline-flex;\\n}\\r\\n.table {\\n  display: table;\\n}\\r\\n.grid {\\n  display: grid;\\n}\\r\\n.hidden {\\n  display: none;\\n}\\r\\n.aspect-square {\\n  aspect-ratio: 1 / 1;\\n}\\r\\n.aspect-video {\\n  aspect-ratio: 16 / 9;\\n}\\r\\n.h-0 {\\n  height: 0px;\\n}\\r\\n.h-1 {\\n  height: 0.25rem;\\n}\\r\\n.h-10 {\\n  height: 2.5rem;\\n}\\r\\n.h-11 {\\n  height: 2.75rem;\\n}\\r\\n.h-12 {\\n  height: 3rem;\\n}\\r\\n.h-16 {\\n  height: 4rem;\\n}\\r\\n.h-2 {\\n  height: 0.5rem;\\n}\\r\\n.h-2\\\\.5 {\\n  height: 0.625rem;\\n}\\r\\n.h-20 {\\n  height: 5rem;\\n}\\r\\n.h-24 {\\n  height: 6rem;\\n}\\r\\n.h-3 {\\n  height: 0.75rem;\\n}\\r\\n.h-3\\\\.5 {\\n  height: 0.875rem;\\n}\\r\\n.h-32 {\\n  height: 8rem;\\n}\\r\\n.h-4 {\\n  height: 1rem;\\n}\\r\\n.h-40 {\\n  height: 10rem;\\n}\\r\\n.h-5 {\\n  height: 1.25rem;\\n}\\r\\n.h-6 {\\n  height: 1.5rem;\\n}\\r\\n.h-7 {\\n  height: 1.75rem;\\n}\\r\\n.h-8 {\\n  height: 2rem;\\n}\\r\\n.h-9 {\\n  height: 2.25rem;\\n}\\r\\n.h-\\\\[1px\\\\] {\\n  height: 1px;\\n}\\r\\n.h-\\\\[var\\\\(--radix-select-trigger-height\\\\)\\\\] {\\n  height: var(--radix-select-trigger-height);\\n}\\r\\n.h-auto {\\n  height: auto;\\n}\\r\\n.h-full {\\n  height: 100%;\\n}\\r\\n.h-px {\\n  height: 1px;\\n}\\r\\n.min-h-\\\\[100px\\\\] {\\n  min-height: 100px;\\n}\\r\\n.min-h-\\\\[150px\\\\] {\\n  min-height: 150px;\\n}\\r\\n.min-h-\\\\[80px\\\\] {\\n  min-height: 80px;\\n}\\r\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\r\\n.w-0 {\\n  width: 0px;\\n}\\r\\n.w-10 {\\n  width: 2.5rem;\\n}\\r\\n.w-12 {\\n  width: 3rem;\\n}\\r\\n.w-16 {\\n  width: 4rem;\\n}\\r\\n.w-2 {\\n  width: 0.5rem;\\n}\\r\\n.w-2\\\\.5 {\\n  width: 0.625rem;\\n}\\r\\n.w-20 {\\n  width: 5rem;\\n}\\r\\n.w-24 {\\n  width: 6rem;\\n}\\r\\n.w-3 {\\n  width: 0.75rem;\\n}\\r\\n.w-3\\\\.5 {\\n  width: 0.875rem;\\n}\\r\\n.w-32 {\\n  width: 8rem;\\n}\\r\\n.w-4 {\\n  width: 1rem;\\n}\\r\\n.w-48 {\\n  width: 12rem;\\n}\\r\\n.w-5 {\\n  width: 1.25rem;\\n}\\r\\n.w-6 {\\n  width: 1.5rem;\\n}\\r\\n.w-8 {\\n  width: 2rem;\\n}\\r\\n.w-\\\\[1px\\\\] {\\n  width: 1px;\\n}\\r\\n.w-fit {\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\r\\n.w-full {\\n  width: 100%;\\n}\\r\\n.min-w-\\\\[8rem\\\\] {\\n  min-width: 8rem;\\n}\\r\\n.min-w-\\\\[var\\\\(--radix-select-trigger-width\\\\)\\\\] {\\n  min-width: var(--radix-select-trigger-width);\\n}\\r\\n.max-w-2xl {\\n  max-width: 42rem;\\n}\\r\\n.max-w-3xl {\\n  max-width: 48rem;\\n}\\r\\n.max-w-4xl {\\n  max-width: 56rem;\\n}\\r\\n.max-w-5xl {\\n  max-width: 64rem;\\n}\\r\\n.max-w-lg {\\n  max-width: 32rem;\\n}\\r\\n.max-w-md {\\n  max-width: 28rem;\\n}\\r\\n.max-w-xs {\\n  max-width: 20rem;\\n}\\r\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\r\\n.shrink-0 {\\n  flex-shrink: 0;\\n}\\r\\n.flex-grow {\\n  flex-grow: 1;\\n}\\r\\n.grow {\\n  flex-grow: 1;\\n}\\r\\n.border-collapse {\\n  border-collapse: collapse;\\n}\\r\\n.-translate-x-1\\\\/2 {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-\\\\[-50\\\\%\\\\] {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-1 {\\n  --tw-translate-y: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-\\\\[-50\\\\%\\\\] {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-0 {\\n  --tw-rotate: 0deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-105 {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n@keyframes gradient-x {\\n\\n  0%, 100% {\\n    background-position: 0% 50%;\\n  }\\n\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n}\\r\\n.animate-gradient-x {\\n  animation: gradient-x 3s ease infinite;\\n}\\r\\n@keyframes pulse {\\n\\n  50% {\\n    opacity: .5;\\n  }\\n}\\r\\n.animate-pulse {\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\r\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\r\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\r\\n.cursor-default {\\n  cursor: default;\\n}\\r\\n.cursor-help {\\n  cursor: help;\\n}\\r\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\r\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\r\\n.touch-none {\\n  touch-action: none;\\n}\\r\\n.select-none {\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n}\\r\\n.resize-none {\\n  resize: none;\\n}\\r\\n.resize {\\n  resize: both;\\n}\\r\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\r\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\r\\n.flex-col {\\n  flex-direction: column;\\n}\\r\\n.flex-col-reverse {\\n  flex-direction: column-reverse;\\n}\\r\\n.flex-wrap {\\n  flex-wrap: wrap;\\n}\\r\\n.items-start {\\n  align-items: flex-start;\\n}\\r\\n.items-end {\\n  align-items: flex-end;\\n}\\r\\n.items-center {\\n  align-items: center;\\n}\\r\\n.justify-center {\\n  justify-content: center;\\n}\\r\\n.justify-between {\\n  justify-content: space-between;\\n}\\r\\n.gap-1 {\\n  gap: 0.25rem;\\n}\\r\\n.gap-2 {\\n  gap: 0.5rem;\\n}\\r\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\r\\n.gap-4 {\\n  gap: 1rem;\\n}\\r\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\r\\n.gap-8 {\\n  gap: 2rem;\\n}\\r\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-1\\\\.5 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\r\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\r\\n.overflow-x-auto {\\n  overflow-x: auto;\\n}\\r\\n.truncate {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\r\\n.whitespace-nowrap {\\n  white-space: nowrap;\\n}\\r\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\r\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\r\\n.rounded-lg {\\n  border-radius: var(--radius);\\n}\\r\\n.rounded-md {\\n  border-radius: calc(var(--radius) - 2px);\\n}\\r\\n.rounded-sm {\\n  border-radius: calc(var(--radius) - 4px);\\n}\\r\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\r\\n.border {\\n  border-width: 1px;\\n}\\r\\n.border-2 {\\n  border-width: 2px;\\n}\\r\\n.border-8 {\\n  border-width: 8px;\\n}\\r\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\r\\n.border-b-2 {\\n  border-bottom-width: 2px;\\n}\\r\\n.border-b-\\\\[86px\\\\] {\\n  border-bottom-width: 86px;\\n}\\r\\n.border-l-\\\\[50px\\\\] {\\n  border-left-width: 50px;\\n}\\r\\n.border-r-\\\\[50px\\\\] {\\n  border-right-width: 50px;\\n}\\r\\n.border-t {\\n  border-top-width: 1px;\\n}\\r\\n.border-t-2 {\\n  border-top-width: 2px;\\n}\\r\\n.border-dashed {\\n  border-style: dashed;\\n}\\r\\n.border-amber-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-amber-900\\\\/50 {\\n  border-color: rgb(120 53 15 / 0.5);\\n}\\r\\n.border-black\\\\/50 {\\n  border-color: rgb(0 0 0 / 0.5);\\n}\\r\\n.border-border {\\n  border-color: hsl(var(--border));\\n}\\r\\n.border-destructive {\\n  border-color: hsl(var(--destructive));\\n}\\r\\n.border-destructive\\\\/50 {\\n  border-color: hsl(var(--destructive) / 0.5);\\n}\\r\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-gray-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-gray-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-green-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-green-900\\\\/50 {\\n  border-color: rgb(20 83 45 / 0.5);\\n}\\r\\n.border-input {\\n  border-color: hsl(var(--input));\\n}\\r\\n.border-muted-foreground {\\n  border-color: hsl(var(--muted-foreground));\\n}\\r\\n.border-muted-foreground\\\\/25 {\\n  border-color: hsl(var(--muted-foreground) / 0.25);\\n}\\r\\n.border-primary {\\n  border-color: hsl(var(--primary));\\n}\\r\\n.border-red-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-red-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-red-900\\\\/50 {\\n  border-color: rgb(127 29 29 / 0.5);\\n}\\r\\n.border-theme-blue {\\n  border-color: hsl(var(--theme-blue));\\n}\\r\\n.border-theme-blue\\\\/20 {\\n  border-color: hsl(var(--theme-blue) / 0.2);\\n}\\r\\n.border-theme-purple\\\\/30 {\\n  border-color: hsl(var(--theme-purple) / 0.3);\\n}\\r\\n.border-theme-teal\\\\/30 {\\n  border-color: hsl(var(--theme-teal) / 0.3);\\n}\\r\\n.border-white\\\\/10 {\\n  border-color: rgb(255 255 255 / 0.1);\\n}\\r\\n.border-white\\\\/20 {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n.border-white\\\\/50 {\\n  border-color: rgb(255 255 255 / 0.5);\\n}\\r\\n.border-theme-purple\\\\/50 {\\n  border-color: hsl(var(--theme-purple) / 0.5);\\n}\\r\\n.border-b-theme-teal\\\\/30 {\\n  border-bottom-color: hsl(var(--theme-teal) / 0.3);\\n}\\r\\n.border-l-transparent {\\n  border-left-color: transparent;\\n}\\r\\n.border-r-transparent {\\n  border-right-color: transparent;\\n}\\r\\n.bg-amber-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-amber-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-amber-500\\\\/10 {\\n  background-color: rgb(245 158 11 / 0.1);\\n}\\r\\n.bg-amber-900\\\\/20 {\\n  background-color: rgb(120 53 15 / 0.2);\\n}\\r\\n.bg-background {\\n  background-color: hsl(var(--background));\\n}\\r\\n.bg-background\\\\/50 {\\n  background-color: hsl(var(--background) / 0.5);\\n}\\r\\n.bg-background\\\\/80 {\\n  background-color: hsl(var(--background) / 0.8);\\n}\\r\\n.bg-background\\\\/95 {\\n  background-color: hsl(var(--background) / 0.95);\\n}\\r\\n.bg-black\\\\/20 {\\n  background-color: rgb(0 0 0 / 0.2);\\n}\\r\\n.bg-black\\\\/80 {\\n  background-color: rgb(0 0 0 / 0.8);\\n}\\r\\n.bg-border {\\n  background-color: hsl(var(--border));\\n}\\r\\n.bg-card {\\n  background-color: hsl(var(--card));\\n}\\r\\n.bg-destructive {\\n  background-color: hsl(var(--destructive));\\n}\\r\\n.bg-destructive\\\\/10 {\\n  background-color: hsl(var(--destructive) / 0.1);\\n}\\r\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-300 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-700 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-800 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-green-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-green-500\\\\/20 {\\n  background-color: rgb(34 197 94 / 0.2);\\n}\\r\\n.bg-green-900\\\\/20 {\\n  background-color: rgb(20 83 45 / 0.2);\\n}\\r\\n.bg-muted {\\n  background-color: hsl(var(--muted));\\n}\\r\\n.bg-popover {\\n  background-color: hsl(var(--popover));\\n}\\r\\n.bg-primary {\\n  background-color: hsl(var(--primary));\\n}\\r\\n.bg-primary\\\\/20 {\\n  background-color: hsl(var(--primary) / 0.2);\\n}\\r\\n.bg-red-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-red-500\\\\/10 {\\n  background-color: rgb(239 68 68 / 0.1);\\n}\\r\\n.bg-red-500\\\\/20 {\\n  background-color: rgb(239 68 68 / 0.2);\\n}\\r\\n.bg-red-900\\\\/20 {\\n  background-color: rgb(127 29 29 / 0.2);\\n}\\r\\n.bg-red-950\\\\/20 {\\n  background-color: rgb(69 10 10 / 0.2);\\n}\\r\\n.bg-red-950\\\\/80 {\\n  background-color: rgb(69 10 10 / 0.8);\\n}\\r\\n.bg-secondary {\\n  background-color: hsl(var(--secondary));\\n}\\r\\n.bg-theme-blue {\\n  background-color: hsl(var(--theme-blue));\\n}\\r\\n.bg-theme-blue\\\\/10 {\\n  background-color: hsl(var(--theme-blue) / 0.1);\\n}\\r\\n.bg-theme-blue\\\\/20 {\\n  background-color: hsl(var(--theme-blue) / 0.2);\\n}\\r\\n.bg-theme-blue\\\\/5 {\\n  background-color: hsl(var(--theme-blue) / 0.05);\\n}\\r\\n.bg-theme-purple {\\n  background-color: hsl(var(--theme-purple));\\n}\\r\\n.bg-theme-purple\\\\/10 {\\n  background-color: hsl(var(--theme-purple) / 0.1);\\n}\\r\\n.bg-theme-purple\\\\/20 {\\n  background-color: hsl(var(--theme-purple) / 0.2);\\n}\\r\\n.bg-theme-teal\\\\/10 {\\n  background-color: hsl(var(--theme-teal) / 0.1);\\n}\\r\\n.bg-transparent {\\n  background-color: transparent;\\n}\\r\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-white\\\\/10 {\\n  background-color: rgb(255 255 255 / 0.1);\\n}\\r\\n.bg-white\\\\/5 {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\r\\n.bg-gradient-to-b {\\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\\n}\\r\\n.bg-gradient-to-br {\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\r\\n.bg-gradient-to-r {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\r\\n.from-amber-500 {\\n  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-gray-50 {\\n  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-gray-900 {\\n  --tw-gradient-from: #111827 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-green-500 {\\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-blue {\\n  --tw-gradient-from: hsl(var(--theme-blue)) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-blue) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-blue\\\\/20 {\\n  --tw-gradient-from: hsl(var(--theme-blue) / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-blue) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-purple {\\n  --tw-gradient-from: hsl(var(--theme-purple)) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-purple) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-purple\\\\/5 {\\n  --tw-gradient-from: hsl(var(--theme-purple) / 0.05) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-purple) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.via-purple-600 {\\n  --tw-gradient-to: rgb(147 51 234 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #9333ea var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.to-black {\\n  --tw-gradient-to: #000 var(--tw-gradient-to-position);\\n}\\r\\n.to-emerald-600 {\\n  --tw-gradient-to: #059669 var(--tw-gradient-to-position);\\n}\\r\\n.to-gray-800 {\\n  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);\\n}\\r\\n.to-orange-600 {\\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-500 {\\n  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-500\\\\/20 {\\n  --tw-gradient-to: rgb(168 85 247 / 0.2) var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-600 {\\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\\n}\\r\\n.to-theme-blue {\\n  --tw-gradient-to: hsl(var(--theme-blue)) var(--tw-gradient-to-position);\\n}\\r\\n.to-theme-purple {\\n  --tw-gradient-to: hsl(var(--theme-purple)) var(--tw-gradient-to-position);\\n}\\r\\n.to-white {\\n  --tw-gradient-to: #fff var(--tw-gradient-to-position);\\n}\\r\\n.to-theme-blue\\\\/5 {\\n  --tw-gradient-to: hsl(var(--theme-blue) / 0.05) var(--tw-gradient-to-position);\\n}\\r\\n.bg-\\\\[length\\\\:200\\\\%_100\\\\%\\\\] {\\n  background-size: 200% 100%;\\n}\\r\\n.bg-clip-text {\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n}\\r\\n.fill-current {\\n  fill: currentColor;\\n}\\r\\n.object-cover {\\n  -o-object-fit: cover;\\n     object-fit: cover;\\n}\\r\\n.p-1 {\\n  padding: 0.25rem;\\n}\\r\\n.p-2 {\\n  padding: 0.5rem;\\n}\\r\\n.p-3 {\\n  padding: 0.75rem;\\n}\\r\\n.p-4 {\\n  padding: 1rem;\\n}\\r\\n.p-5 {\\n  padding: 1.25rem;\\n}\\r\\n.p-6 {\\n  padding: 1.5rem;\\n}\\r\\n.p-8 {\\n  padding: 2rem;\\n}\\r\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\r\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\r\\n.px-8 {\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\r\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\r\\n.py-1\\\\.5 {\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\r\\n.py-10 {\\n  padding-top: 2.5rem;\\n  padding-bottom: 2.5rem;\\n}\\r\\n.py-12 {\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\r\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\r\\n.py-20 {\\n  padding-top: 5rem;\\n  padding-bottom: 5rem;\\n}\\r\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\r\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\r\\n.py-6 {\\n  padding-top: 1.5rem;\\n  padding-bottom: 1.5rem;\\n}\\r\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\r\\n.pb-2 {\\n  padding-bottom: 0.5rem;\\n}\\r\\n.pb-24 {\\n  padding-bottom: 6rem;\\n}\\r\\n.pl-8 {\\n  padding-left: 2rem;\\n}\\r\\n.pr-16 {\\n  padding-right: 4rem;\\n}\\r\\n.pr-2 {\\n  padding-right: 0.5rem;\\n}\\r\\n.pt-0 {\\n  padding-top: 0px;\\n}\\r\\n.pt-2 {\\n  padding-top: 0.5rem;\\n}\\r\\n.pt-6 {\\n  padding-top: 1.5rem;\\n}\\r\\n.text-left {\\n  text-align: left;\\n}\\r\\n.text-center {\\n  text-align: center;\\n}\\r\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\r\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\r\\n.text-4xl {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\r\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\r\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\r\\n.font-bold {\\n  font-weight: 700;\\n}\\r\\n.font-medium {\\n  font-weight: 500;\\n}\\r\\n.font-semibold {\\n  font-weight: 600;\\n}\\r\\n.leading-none {\\n  line-height: 1;\\n}\\r\\n.tracking-tight {\\n  letter-spacing: -0.025em;\\n}\\r\\n.text-amber-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 191 36 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-amber-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(245 158 11 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-amber-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(217 119 6 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-black {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-card-foreground {\\n  color: hsl(var(--card-foreground));\\n}\\r\\n.text-current {\\n  color: currentColor;\\n}\\r\\n.text-destructive {\\n  color: hsl(var(--destructive));\\n}\\r\\n.text-destructive-foreground {\\n  color: hsl(var(--destructive-foreground));\\n}\\r\\n.text-foreground {\\n  color: hsl(var(--foreground));\\n}\\r\\n.text-gray-200 {\\n  --tw-text-opacity: 1;\\n  color: rgb(229 231 235 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-green-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-green-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-muted-foreground {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n.text-popover-foreground {\\n  color: hsl(var(--popover-foreground));\\n}\\r\\n.text-primary {\\n  color: hsl(var(--primary));\\n}\\r\\n.text-primary-foreground {\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n.text-red-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-red-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-secondary-foreground {\\n  color: hsl(var(--secondary-foreground));\\n}\\r\\n.text-theme-blue {\\n  color: hsl(var(--theme-blue));\\n}\\r\\n.text-theme-purple {\\n  color: hsl(var(--theme-purple));\\n}\\r\\n.text-theme-teal {\\n  color: hsl(var(--theme-teal));\\n}\\r\\n.text-transparent {\\n  color: transparent;\\n}\\r\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n.underline-offset-4 {\\n  text-underline-offset: 4px;\\n}\\r\\n.opacity-0 {\\n  opacity: 0;\\n}\\r\\n.opacity-25 {\\n  opacity: 0.25;\\n}\\r\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\r\\n.opacity-70 {\\n  opacity: 0.7;\\n}\\r\\n.opacity-75 {\\n  opacity: 0.75;\\n}\\r\\n.opacity-80 {\\n  opacity: 0.8;\\n}\\r\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-md {\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-xl {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-2xl {\\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-theme-purple\\\\/10 {\\n  --tw-shadow-color: hsl(var(--theme-purple) / 0.1);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\r\\n.shadow-theme-purple\\\\/20 {\\n  --tw-shadow-color: hsl(var(--theme-purple) / 0.2);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\r\\n.outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n.outline {\\n  outline-style: solid;\\n}\\r\\n.ring-offset-background {\\n  --tw-ring-offset-color: hsl(var(--background));\\n}\\r\\n.blur {\\n  --tw-blur: blur(8px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.backdrop-blur-md {\\n  --tw-backdrop-blur: blur(12px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-opacity {\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-transform {\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\r\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\r\\n.duration-500 {\\n  transition-duration: 500ms;\\n}\\r\\n.will-change-\\\\[transform\\\\2c filter\\\\2c opacity\\\\] {\\n  will-change: transform,filter,opacity;\\n}\\r\\n@keyframes enter {\\n\\n  from {\\n    opacity: var(--tw-enter-opacity, 1);\\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\\n  }\\n}\\r\\n@keyframes exit {\\n\\n  to {\\n    opacity: var(--tw-exit-opacity, 1);\\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\\n  }\\n}\\r\\n.animate-in {\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n.fade-in-80 {\\n  --tw-enter-opacity: 0.8;\\n}\\r\\n.duration-200 {\\n  animation-duration: 200ms;\\n}\\r\\n.duration-300 {\\n  animation-duration: 300ms;\\n}\\r\\n.duration-500 {\\n  animation-duration: 500ms;\\n}\\r\\n\\r\\n/* Subtle glass effect */\\r\\n.subtle-glass {\\r\\n  background: rgba(255, 255, 255, 0.03);\\r\\n  backdrop-filter: blur(8px);\\r\\n  -webkit-backdrop-filter: blur(8px);\\r\\n  border: 1px solid rgba(255, 255, 255, 0.05);\\r\\n  transition: background-color var(--theme-transition), border-color var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-glass {\\r\\n  background: rgba(0, 0, 0, 0.03);\\r\\n  border: 1px solid rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle border glow */\\r\\n.subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05);\\r\\n  transition: box-shadow var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle hover effect */\\r\\n.subtle-hover {\\r\\n  transition: all 0.2s ease;\\r\\n}\\r\\n\\r\\n.subtle-hover:hover {\\r\\n  background-color: rgba(255, 255, 255, 0.05);\\r\\n}\\r\\n\\r\\n.light .subtle-hover:hover {\\r\\n  background-color: rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Gradient text */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n  transition: background var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .gradient-text {\\r\\n  background: linear-gradient(to right, #000, rgba(0, 0, 0, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n}\\r\\n\\r\\n/* Subtle grid background */\\r\\n.subtle-grid {\\r\\n  background-size: 30px 30px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-grid {\\r\\n  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),\\r\\n    linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Reveal animation classes */\\r\\n.reveal {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n  transition: opacity 0.5s ease, transform 0.5s ease;\\r\\n}\\r\\n\\r\\n.reveal.active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n/* Button shine effect */\\r\\n.btn-shine {\\r\\n  position: relative;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.btn-shine::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: -100%;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-image: linear-gradient(\\r\\n    90deg,\\r\\n    rgba(255, 255, 255, 0) 0%,\\r\\n    rgba(255, 255, 255, 0.05) 50%,\\r\\n    rgba(255, 255, 255, 0) 100%\\r\\n  );\\r\\n  transition: left 0.5s ease;\\r\\n}\\r\\n\\r\\n.btn-shine:hover::after {\\r\\n  left: 100%;\\r\\n}\\r\\n\\r\\n/* Dot pattern */\\r\\n.dot-pattern {\\r\\n  background-image: radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .dot-pattern {\\r\\n  background-image: radial-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Flow border effect */\\r\\n.flow-border {\\r\\n  position: relative;\\r\\n  z-index: 0;\\r\\n}\\r\\n\\r\\n.flow-border::before {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: -2px;\\r\\n  left: -2px;\\r\\n  right: -2px;\\r\\n  bottom: -2px;\\r\\n  background: var(\\r\\n    --bg-flow-border,\\r\\n    linear-gradient(\\r\\n      90deg,\\r\\n      var(--border-start-color),\\r\\n      var(--border-mid-color),\\r\\n      var(--border-end-color),\\r\\n      var(--border-start-color)\\r\\n    )\\r\\n  );\\r\\n  border-radius: inherit;\\r\\n  z-index: -1;\\r\\n  background-size: 300% 100%;\\r\\n  animation: border-flow 3s ease infinite;\\r\\n  opacity: 0;\\r\\n  transition: opacity 0.3s ease;\\r\\n}\\r\\n\\r\\n.flow-border:hover::before {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n/* Glow effect */\\r\\n.glow-effect {\\r\\n  transition: box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\n.glow-effect:hover {\\r\\n  --glow-color: var(--theme-blue-rgb, 33, 150, 243);\\r\\n  box-shadow: 0 0 10px 2px rgba(var(--glow-color), 0.3);\\r\\n}\\r\\n\\r\\n.glow-effect.purple:hover {\\r\\n  --glow-color: var(--theme-purple-rgb, 156, 39, 176);\\r\\n}\\r\\n\\r\\n.glow-effect.teal:hover {\\r\\n  --glow-color: var(--theme-teal-rgb, 0, 188, 212);\\r\\n}\\r\\n\\r\\n/* Theme transition */\\r\\n.theme-transition {\\r\\n  transition: all var(--theme-transition);\\r\\n}\\r\\n\\r\\n/* Page transition */\\r\\n.page-transition-enter {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n}\\r\\n\\r\\n.page-transition-enter-active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n.page-transition-exit {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n.page-transition-exit-active {\\r\\n  opacity: 0;\\r\\n  transform: translateY(-10px);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n/* Nav link with indicator */\\r\\n.nav-link {\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.nav-link::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  bottom: -4px;\\r\\n  left: 0;\\r\\n  width: 0;\\r\\n  height: 2px;\\r\\n  background-color: hsl(var(--theme-blue));\\r\\n  transition: width 0.3s ease;\\r\\n}\\r\\n\\r\\n.nav-link:hover::after,\\r\\n.nav-link.active::after {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n  height: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: transparent;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: rgba(255, 255, 255, 0.1);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(255, 255, 255, 0.2);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb {\\r\\n  background: rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(0, 0, 0, 0.2);\\r\\n}\\r\\n\\r\\n/* Animation for loading spinner */\\r\\n@keyframes border-flow {\\r\\n  0% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n  100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes subtle-pulse {\\r\\n  0% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 1;\\r\\n  }\\r\\n  100% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-subtle-pulse {\\r\\n  animation: subtle-pulse 2s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-rotate-loader {\\r\\n  animation: spin 1s linear infinite;\\r\\n}\\r\\n\\r\\n@keyframes spin {\\r\\n  from {\\r\\n    transform: rotate(0deg);\\r\\n  }\\r\\n  to {\\r\\n    transform: rotate(360deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n.placeholder\\\\:text-muted-foreground::-moz-placeholder {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.placeholder\\\\:text-muted-foreground::placeholder {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.hover\\\\:scale-105:hover {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.hover\\\\:border-black\\\\/20:hover {\\n  border-color: rgb(0 0 0 / 0.2);\\n}\\r\\n\\r\\n.hover\\\\:border-black\\\\/70:hover {\\n  border-color: rgb(0 0 0 / 0.7);\\n}\\r\\n\\r\\n.hover\\\\:border-gray-400:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:border-theme-blue\\\\/50:hover {\\n  border-color: hsl(var(--theme-blue) / 0.5);\\n}\\r\\n\\r\\n.hover\\\\:border-white\\\\/20:hover {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n\\r\\n.hover\\\\:border-white\\\\/30:hover {\\n  border-color: rgb(255 255 255 / 0.3);\\n}\\r\\n\\r\\n.hover\\\\:bg-accent:hover {\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.hover\\\\:bg-black\\\\/5:hover {\\n  background-color: rgb(0 0 0 / 0.05);\\n}\\r\\n\\r\\n.hover\\\\:bg-destructive\\\\/10:hover {\\n  background-color: hsl(var(--destructive) / 0.1);\\n}\\r\\n\\r\\n.hover\\\\:bg-destructive\\\\/90:hover {\\n  background-color: hsl(var(--destructive) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-primary\\\\/10:hover {\\n  background-color: hsl(var(--primary) / 0.1);\\n}\\r\\n\\r\\n.hover\\\\:bg-primary\\\\/90:hover {\\n  background-color: hsl(var(--primary) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-secondary\\\\/80:hover {\\n  background-color: hsl(var(--secondary) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:bg-theme-blue\\\\/10:hover {\\n  background-color: hsl(var(--theme-blue) / 0.1);\\n}\\r\\n\\r\\n.hover\\\\:bg-theme-purple\\\\/90:hover {\\n  background-color: hsl(var(--theme-purple) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-white\\\\/5:hover {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\r\\n\\r\\n.hover\\\\:bg-white\\\\/90:hover {\\n  background-color: rgb(255 255 255 / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:text-accent-foreground:hover {\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-black:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:text-foreground:hover {\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-primary\\\\/80:hover {\\n  color: hsl(var(--primary) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:text-theme-blue:hover {\\n  color: hsl(var(--theme-blue));\\n}\\r\\n\\r\\n.hover\\\\:text-white:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:underline:hover {\\n  text-decoration-line: underline;\\n}\\r\\n\\r\\n.hover\\\\:opacity-100:hover {\\n  opacity: 1;\\n}\\r\\n\\r\\n.hover\\\\:opacity-90:hover {\\n  opacity: 0.9;\\n}\\r\\n\\r\\n.hover\\\\:shadow-lg:hover {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.focus\\\\:border-primary:focus {\\n  border-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.focus\\\\:border-theme-blue:focus {\\n  border-color: hsl(var(--theme-blue));\\n}\\r\\n\\r\\n.focus\\\\:bg-accent:focus {\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.focus\\\\:text-accent-foreground:focus {\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus\\\\:ring-primary:focus {\\n  --tw-ring-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.focus\\\\:ring-ring:focus {\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n\\r\\n.focus\\\\:ring-theme-blue\\\\/50:focus {\\n  --tw-ring-color: hsl(var(--theme-blue) / 0.5);\\n}\\r\\n\\r\\n.focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:outline-none:focus-visible {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-2:focus-visible {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-ring:focus-visible {\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-offset-2:focus-visible {\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n\\r\\n.disabled\\\\:pointer-events-none:disabled {\\n  pointer-events: none;\\n}\\r\\n\\r\\n.disabled\\\\:cursor-not-allowed:disabled {\\n  cursor: not-allowed;\\n}\\r\\n\\r\\n.disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:opacity-100 {\\n  opacity: 1;\\n}\\r\\n\\r\\n.peer:disabled ~ .peer-disabled\\\\:cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\r\\n\\r\\n.peer:disabled ~ .peer-disabled\\\\:opacity-70 {\\n  opacity: 0.7;\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\]\\\\:pointer-events-none[data-disabled] {\\n  pointer-events: none;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:bg-background[data-state=\\\"active\\\"] {\\n  background-color: hsl(var(--background));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:bg-theme-blue[data-state=\\\"active\\\"] {\\n  background-color: hsl(var(--theme-blue));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:bg-accent[data-state=\\\"open\\\"] {\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:text-foreground[data-state=\\\"active\\\"] {\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:text-white[data-state=\\\"active\\\"] {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:text-muted-foreground[data-state=\\\"open\\\"] {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\]\\\\:opacity-50[data-disabled] {\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:shadow-sm[data-state=\\\"active\\\"] {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:animate-in[data-state=\\\"open\\\"] {\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:animate-out[data-state=\\\"closed\\\"] {\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-0[data-state=\\\"closed\\\"] {\\n  --tw-exit-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:fade-in-0[data-state=\\\"open\\\"] {\\n  --tw-enter-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:zoom-out-95[data-state=\\\"closed\\\"] {\\n  --tw-exit-scale: .95;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-95[data-state=\\\"open\\\"] {\\n  --tw-enter-scale: .95;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left-1\\\\/2[data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-x: -50%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top-\\\\[48\\\\%\\\\][data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-y: -48%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left-1\\\\/2[data-state=\\\"open\\\"] {\\n  --tw-enter-translate-x: -50%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top-\\\\[48\\\\%\\\\][data-state=\\\"open\\\"] {\\n  --tw-enter-translate-y: -48%;\\n}\\r\\n\\r\\n.dark\\\\:border-destructive:is(.dark *) {\\n  border-color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.dark\\\\:border-white\\\\/20:is(.dark *) {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n\\r\\n.dark\\\\:text-white:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:border-white\\\\/20:hover:is(.dark *) {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:border-white\\\\/40:hover:is(.dark *) {\\n  border-color: rgb(255 255 255 / 0.4);\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:bg-white\\\\/5:hover:is(.dark *) {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:text-white:hover:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .sm\\\\:max-w-\\\\[600px\\\\] {\\n    max-width: 600px;\\n  }\\n\\n  .sm\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:justify-end {\\n    justify-content: flex-end;\\n  }\\n\\n  .sm\\\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\n\\n  .sm\\\\:rounded-lg {\\n    border-radius: var(--radius);\\n  }\\n\\n  .sm\\\\:text-left {\\n    text-align: left;\\n  }\\n}\\r\\n\\r\\n@media (min-width: 768px) {\\n\\n  .md\\\\:mb-0 {\\n    margin-bottom: 0px;\\n  }\\n\\n  .md\\\\:mt-\\\\[-180px\\\\] {\\n    margin-top: -180px;\\n  }\\n\\n  .md\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .md\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .md\\\\:p-6 {\\n    padding: 1.5rem;\\n  }\\n\\n  .md\\\\:py-12 {\\n    padding-top: 3rem;\\n    padding-bottom: 3rem;\\n  }\\n\\n  .md\\\\:py-16 {\\n    padding-top: 4rem;\\n    padding-bottom: 4rem;\\n  }\\n\\n  .md\\\\:py-24 {\\n    padding-top: 6rem;\\n    padding-bottom: 6rem;\\n  }\\n\\n  .md\\\\:py-32 {\\n    padding-top: 8rem;\\n    padding-bottom: 8rem;\\n  }\\n\\n  .md\\\\:text-2xl {\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n\\n  .md\\\\:text-3xl {\\n    font-size: 1.875rem;\\n    line-height: 2.25rem;\\n  }\\n\\n  .md\\\\:text-4xl {\\n    font-size: 2.25rem;\\n    line-height: 2.5rem;\\n  }\\n\\n  .md\\\\:text-5xl {\\n    font-size: 3rem;\\n    line-height: 1;\\n  }\\n\\n  .md\\\\:text-6xl {\\n    font-size: 3.75rem;\\n    line-height: 1;\\n  }\\n\\n  .md\\\\:text-xl {\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n}\\r\\n\\r\\n@media (min-width: 1024px) {\\n\\n  .lg\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-5 {\\n    grid-template-columns: repeat(5, minmax(0, 1fr));\\n  }\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\+div\\\\]\\\\:translate-y-\\\\[-3px\\\\]>svg+div {\\n  --tw-translate-y: -3px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:absolute>svg {\\n  position: absolute;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:left-4>svg {\\n  left: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:top-4>svg {\\n  top: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-destructive>svg {\\n  color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-foreground>svg {\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\~\\\\*\\\\]\\\\:pl-7>svg~* {\\n  padding-left: 1.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_p\\\\]\\\\:leading-relaxed p {\\n  line-height: 1.625;\\n}\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;EAAd;IAAA,uBAAc;IAAd,0BAAc;IAAd,iBAAc;IAAd,+BAAc;IAAd,oBAAc;IAAd,kCAAc;IAAd,uBAAc;IAAd,8BAAc;IAAd,2BAAc;IAAd,oCAAc;IAAd,uBAAc;IAAd,kCAAc;IAAd,wBAAc;IAAd,iCAAc;IAAd,4BAAc;IAAd,kCAAc;IAAd,sBAAc;IAAd,qBAAc;IAAd,oBAAc;IAAd,gBAAc;;IAAd,iBAAc;IAAd,0BAAc;IAAd,4BAAc;IAAd,0BAAc;;IAAd,gBAAc;IAAd,4BAAc;;IAAd,uBAAc;IAAd,6CAAc;IAAd,2CAAc;IAAd,0CAAc;;IAAd,qBAAc;IAAd,6BAAc;EAAA;;EAAd;IAAA,0BAAc;IAAd,sBAAc;IAAd,oBAAc;IAAd,2BAAc;IAAd,uBAAc;IAAd,8BAAc;IAAd,mBAAc;IAAd,kCAAc;IAAd,2BAAc;IAAd,gCAAc;IAAd,uBAAc;IAAd,gCAAc;IAAd,wBAAc;IAAd,6BAAc;IAAd,4BAAc;IAAd,kCAAc;IAAd,wBAAc;IAAd,uBAAc;IAAd,sBAAc;;IAAd,iBAAc;IAAd,0BAAc;IAAd,4BAAc;IAAd,0BAAc;;IAAd,gBAAc;IAAd,4BAAc;;IAAd,uBAAc;IAAd,6CAAc;IAAd,2CAAc;IAAd,0CAAc;EAAA;EAAd;EAAA;AAAc;EAAd;EAAA,wCAAc;EAAd,6BAAc;IAAd,yCAAc;IAAd;AAAc;AACd;EAAA,WAAoB;EAApB,kBAAoB;EAApB,iBAAoB;EAApB,mBAAoB;EAApB;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AACpB;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,yBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;;EAAnB;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yBAAmB;KAAnB,sBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,gEAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,2EAAmB;EAAnB;AAAmB;AAAnB;EAAA,iFAAmB;EAAnB,2EAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,6EAAmB;EAAnB;AAAmB;AAAnB;EAAA,oFAAmB;EAAnB,6EAAmB;EAAnB;AAAmB;AAAnB;EAAA,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;KAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,iDAAmB;EAAnB;AAAmB;AAAnB;EAAA,iDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA,mCAAmB;IAAnB;EAAmB;AAAA;AAAnB;;EAAA;IAAA,kCAAmB;IAAnB;EAAmB;AAAA;AAAnB;EAAA,qBAAmB;EAAnB,yBAAmB;EAAnB,2BAAmB;EAAnB,yBAAmB;EAAnB,0BAAmB;EAAnB,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAyFnB,wBAAwB;AACxB;EACE,qCAAqC;EACrC,0BAA0B;EAC1B,kCAAkC;EAClC,2CAA2C;EAC3C,0FAA0F;AAC5F;;AAEA;EACE,+BAA+B;EAC/B,qCAAqC;AACvC;;AAEA,uBAAuB;AACvB;EACE,+CAA+C;EAC/C,8CAA8C;AAChD;;AAEA;EACE,yCAAyC;AAC3C;;AAEA,wBAAwB;AACxB;EACE,yBAAyB;AAC3B;;AAEA;EACE,2CAA2C;AAC7C;;AAEA;EACE,qCAAqC;AACvC;;AAEA,kBAAkB;AAClB;EACE,sEAAsE;EACtE,6BAA6B;EAC7B,oCAAoC;EACpC,qBAAqB;EACrB,8CAA8C;AAChD;;AAEA;EACE,gEAAgE;EAChE,6BAA6B;EAC7B,oCAAoC;EACpC,qBAAqB;AACvB;;AAEA,2BAA2B;AAC3B;EACE,0BAA0B;EAC1B,oDAAoD;AACtD;;AAEA;EACE;wEACsE;AACxE;;AAEA,6BAA6B;AAC7B;EACE,UAAU;EACV,2BAA2B;EAC3B,kDAAkD;AACpD;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA,wBAAwB;AACxB;EACE,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,WAAW;EACX,WAAW;EACX,YAAY;EACZ;;;;;GAKC;EACD,0BAA0B;AAC5B;;AAEA;EACE,UAAU;AACZ;;AAEA,gBAAgB;AAChB;EACE,gFAAgF;EAChF,0BAA0B;EAC1B,oDAAoD;AACtD;;AAEA;EACE,0EAA0E;AAC5E;;AAEA,uBAAuB;AACvB;EACE,kBAAkB;EAClB,UAAU;AACZ;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,SAAS;EACT,UAAU;EACV,WAAW;EACX,YAAY;EACZ;;;;;;;;;GASC;EACD,sBAAsB;EACtB,WAAW;EACX,0BAA0B;EAC1B,uCAAuC;EACvC,UAAU;EACV,6BAA6B;AAC/B;;AAEA;EACE,UAAU;AACZ;;AAEA,gBAAgB;AAChB;EACE,gCAAgC;AAClC;;AAEA;EACE,iDAAiD;EACjD,qDAAqD;AACvD;;AAEA;EACE,mDAAmD;AACrD;;AAEA;EACE,gDAAgD;AAClD;;AAEA,qBAAqB;AACrB;EACE,uCAAuC;AACzC;;AAEA,oBAAoB;AACpB;EACE,UAAU;EACV,2BAA2B;AAC7B;;AAEA;EACE,UAAU;EACV,wBAAwB;EACxB,wCAAwC;AAC1C;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA;EACE,UAAU;EACV,4BAA4B;EAC5B,wCAAwC;AAC1C;;AAEA,4BAA4B;AAC5B;EACE,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,YAAY;EACZ,OAAO;EACP,QAAQ;EACR,WAAW;EACX,wCAAwC;EACxC,2BAA2B;AAC7B;;AAEA;;EAEE,WAAW;AACb;;AAEA,qBAAqB;AACrB;EACE,UAAU;EACV,WAAW;AACb;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,8BAA8B;AAChC;;AAEA,kCAAkC;AAClC;EACE;IACE,2BAA2B;EAC7B;EACA;IACE,6BAA6B;EAC/B;EACA;IACE,2BAA2B;EAC7B;AACF;;AAEA;EACE;IACE,YAAY;EACd;EACA;IACE,UAAU;EACZ;EACA;IACE,YAAY;EACd;AACF;;AAEA;EACE,+CAA+C;AACjD;;AAEA;EACE,kCAAkC;AACpC;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,yBAAyB;EAC3B;AACF;;AArXA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,kBA6XA;EA7XA,kBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,sBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,kBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,+EA6XA;EA7XA,mGA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,8BA6XA;EA7XA;AA6XA;;AA7XA;EAAA,2GA6XA;EA7XA,yGA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,8BA6XA;EA7XA;AA6XA;;AA7XA;EAAA,2GA6XA;EA7XA,yGA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,0CA6XA;EA7XA,uDA6XA;EA7XA;AA6XA;;AA7XA;EAAA,qBA6XA;EA7XA,yBA6XA;EA7XA,2BA6XA;EA7XA,yBA6XA;EA7XA,0BA6XA;EA7XA,+BA6XA;EA7XA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA,yBA6XA;EA7XA,0BA6XA;EA7XA,wBA6XA;EA7XA,yBA6XA;EA7XA,8BA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;;EAAA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA,uBA6XA;IA7XA,sDA6XA;IA7XA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;AAAA;;AA7XA;;EAAA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,mBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,kBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,eA6XA;IA7XA;EA6XA;;EA7XA;IAAA,kBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,kBA6XA;IA7XA;EA6XA;AAAA;;AA7XA;;EAAA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;AAAA;;AA7XA;EAAA,sBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA\",\"sourcesContent\":[\"@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n\\r\\n@layer base {\\r\\n  :root {\\r\\n    --background: 0 0% 100%;\\r\\n    --foreground: 240 10% 3.9%;\\r\\n    --card: 0 0% 100%;\\r\\n    --card-foreground: 240 10% 3.9%;\\r\\n    --popover: 0 0% 100%;\\r\\n    --popover-foreground: 240 10% 3.9%;\\r\\n    --primary: 240 5.9% 10%;\\r\\n    --primary-foreground: 0 0% 98%;\\r\\n    --secondary: 240 4.8% 95.9%;\\r\\n    --secondary-foreground: 240 5.9% 10%;\\r\\n    --muted: 240 4.8% 95.9%;\\r\\n    --muted-foreground: 240 3.8% 46.1%;\\r\\n    --accent: 240 4.8% 95.9%;\\r\\n    --accent-foreground: 240 5.9% 10%;\\r\\n    --destructive: 0 84.2% 60.2%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 5.9% 90%;\\r\\n    --input: 240 5.9% 90%;\\r\\n    --ring: 240 5.9% 10%;\\r\\n    --radius: 0.5rem;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 50%;\\r\\n    --theme-purple: 260 100% 60%;\\r\\n    --theme-teal: 180 100% 40%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 50%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.5);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.5);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.5);\\r\\n\\r\\n    /* Theme transition */\\r\\n    --theme-transition: 0.3s ease;\\r\\n  }\\r\\n\\r\\n  .dark {\\r\\n    --background: 240 10% 3.9%;\\r\\n    --foreground: 0 0% 98%;\\r\\n    --card: 240 10% 3.9%;\\r\\n    --card-foreground: 0 0% 98%;\\r\\n    --popover: 240 10% 3.9%;\\r\\n    --popover-foreground: 0 0% 98%;\\r\\n    --primary: 0 0% 98%;\\r\\n    --primary-foreground: 240 5.9% 10%;\\r\\n    --secondary: 240 3.7% 15.9%;\\r\\n    --secondary-foreground: 0 0% 98%;\\r\\n    --muted: 240 3.7% 15.9%;\\r\\n    --muted-foreground: 240 5% 64.9%;\\r\\n    --accent: 240 3.7% 15.9%;\\r\\n    --accent-foreground: 0 0% 98%;\\r\\n    --destructive: 0 62.8% 30.6%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 3.7% 15.9%;\\r\\n    --input: 240 3.7% 15.9%;\\r\\n    --ring: 240 4.9% 83.9%;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 60%;\\r\\n    --theme-purple: 260 100% 70%;\\r\\n    --theme-teal: 180 100% 50%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 60%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.7);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.7);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.7);\\r\\n  }\\r\\n}\\r\\n\\r\\n@layer base {\\r\\n  * {\\r\\n    @apply border-border;\\r\\n  }\\r\\n  body {\\r\\n    @apply bg-background text-foreground;\\r\\n    font-feature-settings: \\\"rlig\\\" 1, \\\"calt\\\" 1;\\r\\n    transition: background-color var(--theme-transition), color var(--theme-transition);\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Subtle glass effect */\\r\\n.subtle-glass {\\r\\n  background: rgba(255, 255, 255, 0.03);\\r\\n  backdrop-filter: blur(8px);\\r\\n  -webkit-backdrop-filter: blur(8px);\\r\\n  border: 1px solid rgba(255, 255, 255, 0.05);\\r\\n  transition: background-color var(--theme-transition), border-color var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-glass {\\r\\n  background: rgba(0, 0, 0, 0.03);\\r\\n  border: 1px solid rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle border glow */\\r\\n.subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05);\\r\\n  transition: box-shadow var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle hover effect */\\r\\n.subtle-hover {\\r\\n  transition: all 0.2s ease;\\r\\n}\\r\\n\\r\\n.subtle-hover:hover {\\r\\n  background-color: rgba(255, 255, 255, 0.05);\\r\\n}\\r\\n\\r\\n.light .subtle-hover:hover {\\r\\n  background-color: rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Gradient text */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n  transition: background var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .gradient-text {\\r\\n  background: linear-gradient(to right, #000, rgba(0, 0, 0, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n}\\r\\n\\r\\n/* Subtle grid background */\\r\\n.subtle-grid {\\r\\n  background-size: 30px 30px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-grid {\\r\\n  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),\\r\\n    linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Reveal animation classes */\\r\\n.reveal {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n  transition: opacity 0.5s ease, transform 0.5s ease;\\r\\n}\\r\\n\\r\\n.reveal.active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n/* Button shine effect */\\r\\n.btn-shine {\\r\\n  position: relative;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.btn-shine::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: -100%;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-image: linear-gradient(\\r\\n    90deg,\\r\\n    rgba(255, 255, 255, 0) 0%,\\r\\n    rgba(255, 255, 255, 0.05) 50%,\\r\\n    rgba(255, 255, 255, 0) 100%\\r\\n  );\\r\\n  transition: left 0.5s ease;\\r\\n}\\r\\n\\r\\n.btn-shine:hover::after {\\r\\n  left: 100%;\\r\\n}\\r\\n\\r\\n/* Dot pattern */\\r\\n.dot-pattern {\\r\\n  background-image: radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .dot-pattern {\\r\\n  background-image: radial-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Flow border effect */\\r\\n.flow-border {\\r\\n  position: relative;\\r\\n  z-index: 0;\\r\\n}\\r\\n\\r\\n.flow-border::before {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: -2px;\\r\\n  left: -2px;\\r\\n  right: -2px;\\r\\n  bottom: -2px;\\r\\n  background: var(\\r\\n    --bg-flow-border,\\r\\n    linear-gradient(\\r\\n      90deg,\\r\\n      var(--border-start-color),\\r\\n      var(--border-mid-color),\\r\\n      var(--border-end-color),\\r\\n      var(--border-start-color)\\r\\n    )\\r\\n  );\\r\\n  border-radius: inherit;\\r\\n  z-index: -1;\\r\\n  background-size: 300% 100%;\\r\\n  animation: border-flow 3s ease infinite;\\r\\n  opacity: 0;\\r\\n  transition: opacity 0.3s ease;\\r\\n}\\r\\n\\r\\n.flow-border:hover::before {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n/* Glow effect */\\r\\n.glow-effect {\\r\\n  transition: box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\n.glow-effect:hover {\\r\\n  --glow-color: var(--theme-blue-rgb, 33, 150, 243);\\r\\n  box-shadow: 0 0 10px 2px rgba(var(--glow-color), 0.3);\\r\\n}\\r\\n\\r\\n.glow-effect.purple:hover {\\r\\n  --glow-color: var(--theme-purple-rgb, 156, 39, 176);\\r\\n}\\r\\n\\r\\n.glow-effect.teal:hover {\\r\\n  --glow-color: var(--theme-teal-rgb, 0, 188, 212);\\r\\n}\\r\\n\\r\\n/* Theme transition */\\r\\n.theme-transition {\\r\\n  transition: all var(--theme-transition);\\r\\n}\\r\\n\\r\\n/* Page transition */\\r\\n.page-transition-enter {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n}\\r\\n\\r\\n.page-transition-enter-active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n.page-transition-exit {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n.page-transition-exit-active {\\r\\n  opacity: 0;\\r\\n  transform: translateY(-10px);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n/* Nav link with indicator */\\r\\n.nav-link {\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.nav-link::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  bottom: -4px;\\r\\n  left: 0;\\r\\n  width: 0;\\r\\n  height: 2px;\\r\\n  background-color: hsl(var(--theme-blue));\\r\\n  transition: width 0.3s ease;\\r\\n}\\r\\n\\r\\n.nav-link:hover::after,\\r\\n.nav-link.active::after {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n  height: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: transparent;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: rgba(255, 255, 255, 0.1);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(255, 255, 255, 0.2);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb {\\r\\n  background: rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(0, 0, 0, 0.2);\\r\\n}\\r\\n\\r\\n/* Animation for loading spinner */\\r\\n@keyframes border-flow {\\r\\n  0% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n  100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes subtle-pulse {\\r\\n  0% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 1;\\r\\n  }\\r\\n  100% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-subtle-pulse {\\r\\n  animation: subtle-pulse 2s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-rotate-loader {\\r\\n  animation: spin 1s linear infinite;\\r\\n}\\r\\n\\r\\n@keyframes spin {\\r\\n  from {\\r\\n    transform: rotate(0deg);\\r\\n  }\\r\\n  to {\\r\\n    transform: rotate(360deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});