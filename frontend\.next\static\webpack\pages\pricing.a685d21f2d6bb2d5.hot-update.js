"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/pricing",{

/***/ "./components/ui/pixel-card.js":
/*!*************************************!*\
  !*** ./components/ui/pixel-card.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PixelCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nclass Pixel {\n    getRandomValue(min, max) {\n        return Math.random() * (max - min) + min;\n    }\n    draw() {\n        const centerOffset = this.maxSizeInteger * 0.5 - this.size * 0.5;\n        this.ctx.fillStyle = this.color;\n        this.ctx.fillRect(this.x + centerOffset, this.y + centerOffset, this.size, this.size);\n    }\n    appear() {\n        this.isIdle = false;\n        if (this.counter <= this.delay) {\n            this.counter += this.counterStep;\n            return;\n        }\n        if (this.size >= this.maxSize) {\n            this.isShimmer = true;\n        }\n        if (this.isShimmer) {\n            this.shimmer();\n        } else {\n            this.size += this.sizeStep;\n        }\n        this.draw();\n    }\n    disappear() {\n        this.isShimmer = false;\n        this.counter = 0;\n        if (this.size <= 0) {\n            this.isIdle = true;\n            return;\n        } else {\n            this.size -= 0.1;\n        }\n        this.draw();\n    }\n    shimmer() {\n        if (this.size >= this.maxSize) {\n            this.isReverse = true;\n        } else if (this.size <= this.minSize) {\n            this.isReverse = false;\n        }\n        if (this.isReverse) {\n            this.size -= this.speed;\n        } else {\n            this.size += this.speed;\n        }\n    }\n    constructor(canvas, context, x, y, color, speed, delay){\n        this.width = canvas.width;\n        this.height = canvas.height;\n        this.ctx = context;\n        this.x = x;\n        this.y = y;\n        this.color = color;\n        this.speed = this.getRandomValue(0.1, 0.9) * speed;\n        this.size = 0;\n        this.sizeStep = Math.random() * 0.6 + 0.2;\n        this.minSize = 1;\n        this.maxSizeInteger = 4;\n        this.maxSize = this.getRandomValue(this.minSize, this.maxSizeInteger);\n        this.delay = delay;\n        this.counter = 0;\n        this.counterStep = Math.random() * 4 + (this.width + this.height) * 0.01;\n        this.isIdle = false;\n        this.isReverse = false;\n        this.isShimmer = false;\n    }\n}\nfunction getEffectiveSpeed(value, reducedMotion) {\n    const min = 0;\n    const max = 100;\n    const throttle = 0.001;\n    const parsed = parseInt(value, 10);\n    if (parsed <= min || reducedMotion) {\n        return min;\n    } else if (parsed >= max) {\n        return max * throttle;\n    } else {\n        return parsed * throttle;\n    }\n}\n/**\n * Variants adapted for the pricing theme\n */ const VARIANTS = {\n    default: {\n        activeColor: null,\n        gap: 4,\n        speed: 50,\n        colors: \"#e2e8f0,#cbd5e1,#94a3b8,#64748b\",\n        noFocus: false\n    },\n    purple: {\n        activeColor: \"#e9d5ff\",\n        gap: 4,\n        speed: 60,\n        colors: \"#e9d5ff,#c4b5fd,#8b5cf6,#7c3aed\",\n        noFocus: false\n    },\n    blue: {\n        activeColor: \"#dbeafe\",\n        gap: 4,\n        speed: 55,\n        colors: \"#dbeafe,#93c5fd,#3b82f6,#1d4ed8\",\n        noFocus: false\n    },\n    highlighted: {\n        activeColor: \"#e9d5ff\",\n        gap: 3,\n        speed: 80,\n        colors: \"#e9d5ff,#c4b5fd,#8b5cf6,#7c3aed,#dbeafe,#93c5fd,#3b82f6\",\n        noFocus: false\n    }\n};\nfunction PixelCard(param) {\n    let { variant = \"default\", gap, speed, colors, noFocus, className = \"\", children } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pixelsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const timePreviousRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(typeof performance !== \"undefined\" ? performance.now() : Date.now());\n    const [reducedMotion, setReducedMotion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) {\n            setReducedMotion(window.matchMedia(\"(prefers-reduced-motion: reduce)\").matches);\n        }\n    }, []);\n    const variantCfg = VARIANTS[variant] || VARIANTS.default;\n    const finalGap = gap !== null && gap !== void 0 ? gap : variantCfg.gap;\n    const finalSpeed = speed !== null && speed !== void 0 ? speed : variantCfg.speed;\n    const finalColors = colors !== null && colors !== void 0 ? colors : variantCfg.colors;\n    const finalNoFocus = noFocus !== null && noFocus !== void 0 ? noFocus : variantCfg.noFocus;\n    const initPixels = ()=>{\n        if (!containerRef.current || !canvasRef.current) return;\n        const rect = containerRef.current.getBoundingClientRect();\n        const width = Math.floor(rect.width);\n        const height = Math.floor(rect.height);\n        const ctx = canvasRef.current.getContext(\"2d\");\n        canvasRef.current.width = width;\n        canvasRef.current.height = height;\n        canvasRef.current.style.width = \"\".concat(width, \"px\");\n        canvasRef.current.style.height = \"\".concat(height, \"px\");\n        const colorsArray = finalColors.split(\",\");\n        const pxs = [];\n        for(let x = 0; x < width; x += parseInt(finalGap, 10)){\n            for(let y = 0; y < height; y += parseInt(finalGap, 10)){\n                const color = colorsArray[Math.floor(Math.random() * colorsArray.length)];\n                const dx = x - width / 2;\n                const dy = y - height / 2;\n                const distance = Math.sqrt(dx * dx + dy * dy);\n                const delay = reducedMotion ? 0 : distance;\n                pxs.push(new Pixel(canvasRef.current, ctx, x, y, color, getEffectiveSpeed(finalSpeed, reducedMotion), delay));\n            }\n        }\n        pixelsRef.current = pxs;\n    };\n    const doAnimate = (fnName)=>{\n        var _canvasRef_current;\n        animationRef.current = requestAnimationFrame(()=>doAnimate(fnName));\n        const timeNow = typeof performance !== \"undefined\" ? performance.now() : Date.now();\n        const timePassed = timeNow - timePreviousRef.current;\n        const timeInterval = 1000 / 60; // ~60 FPS\n        if (timePassed < timeInterval) return;\n        timePreviousRef.current = timeNow - timePassed % timeInterval;\n        const ctx = (_canvasRef_current = canvasRef.current) === null || _canvasRef_current === void 0 ? void 0 : _canvasRef_current.getContext(\"2d\");\n        if (!ctx || !canvasRef.current) return;\n        ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);\n        let allIdle = true;\n        for(let i = 0; i < pixelsRef.current.length; i++){\n            const pixel = pixelsRef.current[i];\n            pixel[fnName]();\n            if (!pixel.isIdle) {\n                allIdle = false;\n            }\n        }\n        if (allIdle) {\n            cancelAnimationFrame(animationRef.current);\n        }\n    };\n    const handleAnimation = (name)=>{\n        cancelAnimationFrame(animationRef.current);\n        animationRef.current = requestAnimationFrame(()=>doAnimate(name));\n    };\n    const onMouseEnter = ()=>handleAnimation(\"appear\");\n    const onMouseLeave = ()=>handleAnimation(\"disappear\");\n    const onFocus = (e)=>{\n        if (e.currentTarget.contains(e.relatedTarget)) return;\n        handleAnimation(\"appear\");\n    };\n    const onBlur = (e)=>{\n        if (e.currentTarget.contains(e.relatedTarget)) return;\n        handleAnimation(\"disappear\");\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initPixels();\n        const observer = new ResizeObserver(()=>{\n            initPixels();\n        });\n        if (containerRef.current) {\n            observer.observe(containerRef.current);\n        }\n        return ()=>{\n            observer.disconnect();\n            cancelAnimationFrame(animationRef.current);\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        finalGap,\n        finalSpeed,\n        finalColors,\n        finalNoFocus\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"relative overflow-hidden rounded-xl border border-white/10 isolate transition-colors duration-200 ease-[cubic-bezier(0.5,1,0.89,1)] select-none min-h-[400px] \".concat(className),\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onFocus: finalNoFocus ? undefined : onFocus,\n        onBlur: finalNoFocus ? undefined : onBlur,\n        tabIndex: finalNoFocus ? -1 : 0,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                className: \"absolute inset-0 w-full h-full block pointer-events-none\",\n                ref: canvasRef\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\pixel-card.js\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\pixel-card.js\",\n        lineNumber: 260,\n        columnNumber: 5\n    }, this);\n}\n_s(PixelCard, \"HeekizPLUKtvxmyXJ0VDaWjZs9A=\");\n_c = PixelCard;\nvar _c;\n$RefreshReg$(_c, \"PixelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3VpL3BpeGVsLWNhcmQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFvRDtBQUVwRCxNQUFNRztJQXNCSkMsZUFBZUMsR0FBRyxFQUFFQyxHQUFHLEVBQUU7UUFDdkIsT0FBT0MsS0FBS0MsTUFBTSxLQUFNRixDQUFBQSxNQUFNRCxHQUFFLElBQUtBO0lBQ3ZDO0lBRUFJLE9BQU87UUFDTCxNQUFNQyxlQUFlLElBQUksQ0FBQ0MsY0FBYyxHQUFHLE1BQU0sSUFBSSxDQUFDQyxJQUFJLEdBQUc7UUFDN0QsSUFBSSxDQUFDQyxHQUFHLENBQUNDLFNBQVMsR0FBRyxJQUFJLENBQUNDLEtBQUs7UUFDL0IsSUFBSSxDQUFDRixHQUFHLENBQUNHLFFBQVEsQ0FDZixJQUFJLENBQUNDLENBQUMsR0FBR1AsY0FDVCxJQUFJLENBQUNRLENBQUMsR0FBR1IsY0FDVCxJQUFJLENBQUNFLElBQUksRUFDVCxJQUFJLENBQUNBLElBQUk7SUFFYjtJQUVBTyxTQUFTO1FBQ1AsSUFBSSxDQUFDQyxNQUFNLEdBQUc7UUFDZCxJQUFJLElBQUksQ0FBQ0MsT0FBTyxJQUFJLElBQUksQ0FBQ0MsS0FBSyxFQUFFO1lBQzlCLElBQUksQ0FBQ0QsT0FBTyxJQUFJLElBQUksQ0FBQ0UsV0FBVztZQUNoQztRQUNGO1FBQ0EsSUFBSSxJQUFJLENBQUNYLElBQUksSUFBSSxJQUFJLENBQUNZLE9BQU8sRUFBRTtZQUM3QixJQUFJLENBQUNDLFNBQVMsR0FBRztRQUNuQjtRQUNBLElBQUksSUFBSSxDQUFDQSxTQUFTLEVBQUU7WUFDbEIsSUFBSSxDQUFDQyxPQUFPO1FBQ2QsT0FBTztZQUNMLElBQUksQ0FBQ2QsSUFBSSxJQUFJLElBQUksQ0FBQ2UsUUFBUTtRQUM1QjtRQUNBLElBQUksQ0FBQ2xCLElBQUk7SUFDWDtJQUVBbUIsWUFBWTtRQUNWLElBQUksQ0FBQ0gsU0FBUyxHQUFHO1FBQ2pCLElBQUksQ0FBQ0osT0FBTyxHQUFHO1FBQ2YsSUFBSSxJQUFJLENBQUNULElBQUksSUFBSSxHQUFHO1lBQ2xCLElBQUksQ0FBQ1EsTUFBTSxHQUFHO1lBQ2Q7UUFDRixPQUFPO1lBQ0wsSUFBSSxDQUFDUixJQUFJLElBQUk7UUFDZjtRQUNBLElBQUksQ0FBQ0gsSUFBSTtJQUNYO0lBRUFpQixVQUFVO1FBQ1IsSUFBSSxJQUFJLENBQUNkLElBQUksSUFBSSxJQUFJLENBQUNZLE9BQU8sRUFBRTtZQUM3QixJQUFJLENBQUNLLFNBQVMsR0FBRztRQUNuQixPQUFPLElBQUksSUFBSSxDQUFDakIsSUFBSSxJQUFJLElBQUksQ0FBQ2tCLE9BQU8sRUFBRTtZQUNwQyxJQUFJLENBQUNELFNBQVMsR0FBRztRQUNuQjtRQUNBLElBQUksSUFBSSxDQUFDQSxTQUFTLEVBQUU7WUFDbEIsSUFBSSxDQUFDakIsSUFBSSxJQUFJLElBQUksQ0FBQ21CLEtBQUs7UUFDekIsT0FBTztZQUNMLElBQUksQ0FBQ25CLElBQUksSUFBSSxJQUFJLENBQUNtQixLQUFLO1FBQ3pCO0lBQ0Y7SUE1RUFDLFlBQVlDLE1BQU0sRUFBRUMsT0FBTyxFQUFFakIsQ0FBQyxFQUFFQyxDQUFDLEVBQUVILEtBQUssRUFBRWdCLEtBQUssRUFBRVQsS0FBSyxDQUFFO1FBQ3RELElBQUksQ0FBQ2EsS0FBSyxHQUFHRixPQUFPRSxLQUFLO1FBQ3pCLElBQUksQ0FBQ0MsTUFBTSxHQUFHSCxPQUFPRyxNQUFNO1FBQzNCLElBQUksQ0FBQ3ZCLEdBQUcsR0FBR3FCO1FBQ1gsSUFBSSxDQUFDakIsQ0FBQyxHQUFHQTtRQUNULElBQUksQ0FBQ0MsQ0FBQyxHQUFHQTtRQUNULElBQUksQ0FBQ0gsS0FBSyxHQUFHQTtRQUNiLElBQUksQ0FBQ2dCLEtBQUssR0FBRyxJQUFJLENBQUMzQixjQUFjLENBQUMsS0FBSyxPQUFPMkI7UUFDN0MsSUFBSSxDQUFDbkIsSUFBSSxHQUFHO1FBQ1osSUFBSSxDQUFDZSxRQUFRLEdBQUdwQixLQUFLQyxNQUFNLEtBQUssTUFBTTtRQUN0QyxJQUFJLENBQUNzQixPQUFPLEdBQUc7UUFDZixJQUFJLENBQUNuQixjQUFjLEdBQUc7UUFDdEIsSUFBSSxDQUFDYSxPQUFPLEdBQUcsSUFBSSxDQUFDcEIsY0FBYyxDQUFDLElBQUksQ0FBQzBCLE9BQU8sRUFBRSxJQUFJLENBQUNuQixjQUFjO1FBQ3BFLElBQUksQ0FBQ1csS0FBSyxHQUFHQTtRQUNiLElBQUksQ0FBQ0QsT0FBTyxHQUFHO1FBQ2YsSUFBSSxDQUFDRSxXQUFXLEdBQUdoQixLQUFLQyxNQUFNLEtBQUssSUFBSSxDQUFDLElBQUksQ0FBQzJCLEtBQUssR0FBRyxJQUFJLENBQUNDLE1BQU0sSUFBSTtRQUNwRSxJQUFJLENBQUNoQixNQUFNLEdBQUc7UUFDZCxJQUFJLENBQUNTLFNBQVMsR0FBRztRQUNqQixJQUFJLENBQUNKLFNBQVMsR0FBRztJQUNuQjtBQTBERjtBQUVBLFNBQVNZLGtCQUFrQkMsS0FBSyxFQUFFQyxhQUFhO0lBQzdDLE1BQU1sQyxNQUFNO0lBQ1osTUFBTUMsTUFBTTtJQUNaLE1BQU1rQyxXQUFXO0lBQ2pCLE1BQU1DLFNBQVNDLFNBQVNKLE9BQU87SUFFL0IsSUFBSUcsVUFBVXBDLE9BQU9rQyxlQUFlO1FBQ2xDLE9BQU9sQztJQUNULE9BQU8sSUFBSW9DLFVBQVVuQyxLQUFLO1FBQ3hCLE9BQU9BLE1BQU1rQztJQUNmLE9BQU87UUFDTCxPQUFPQyxTQUFTRDtJQUNsQjtBQUNGO0FBRUE7O0NBRUMsR0FDRCxNQUFNRyxXQUFXO0lBQ2ZDLFNBQVM7UUFDUEMsYUFBYTtRQUNiQyxLQUFLO1FBQ0xmLE9BQU87UUFDUGdCLFFBQVE7UUFDUkMsU0FBUztJQUNYO0lBQ0FDLFFBQVE7UUFDTkosYUFBYTtRQUNiQyxLQUFLO1FBQ0xmLE9BQU87UUFDUGdCLFFBQVE7UUFDUkMsU0FBUztJQUNYO0lBQ0FFLE1BQU07UUFDSkwsYUFBYTtRQUNiQyxLQUFLO1FBQ0xmLE9BQU87UUFDUGdCLFFBQVE7UUFDUkMsU0FBUztJQUNYO0lBQ0FHLGFBQWE7UUFDWE4sYUFBYTtRQUNiQyxLQUFLO1FBQ0xmLE9BQU87UUFDUGdCLFFBQVE7UUFDUkMsU0FBUztJQUNYO0FBQ0Y7QUFFZSxTQUFTSSxVQUFVLEtBUWpDO1FBUmlDLEVBQ2hDQyxVQUFVLFNBQVMsRUFDbkJQLEdBQUcsRUFDSGYsS0FBSyxFQUNMZ0IsTUFBTSxFQUNOQyxPQUFPLEVBQ1BNLFlBQVksRUFBRSxFQUNkQyxRQUFRLEVBQ1QsR0FSaUM7O0lBU2hDLE1BQU1DLGVBQWV2RCw2Q0FBTUEsQ0FBQztJQUM1QixNQUFNd0QsWUFBWXhELDZDQUFNQSxDQUFDO0lBQ3pCLE1BQU15RCxZQUFZekQsNkNBQU1BLENBQUMsRUFBRTtJQUMzQixNQUFNMEQsZUFBZTFELDZDQUFNQSxDQUFDO0lBQzVCLE1BQU0yRCxrQkFBa0IzRCw2Q0FBTUEsQ0FBQyxPQUFPNEQsZ0JBQWdCLGNBQWNBLFlBQVlDLEdBQUcsS0FBS0MsS0FBS0QsR0FBRztJQUNoRyxNQUFNLENBQUN2QixlQUFleUIsaUJBQWlCLEdBQUc5RCwrQ0FBUUEsQ0FBQztJQUVuREYsZ0RBQVNBLENBQUM7UUFDUixJQUFJLElBQWtCLEVBQWE7WUFDakNnRSxpQkFBaUJDLE9BQU9DLFVBQVUsQ0FBQyxvQ0FBb0NDLE9BQU87UUFDaEY7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNQyxhQUFhekIsUUFBUSxDQUFDVSxRQUFRLElBQUlWLFNBQVNDLE9BQU87SUFDeEQsTUFBTXlCLFdBQVd2QixnQkFBQUEsaUJBQUFBLE1BQU9zQixXQUFXdEIsR0FBRztJQUN0QyxNQUFNd0IsYUFBYXZDLGtCQUFBQSxtQkFBQUEsUUFBU3FDLFdBQVdyQyxLQUFLO0lBQzVDLE1BQU13QyxjQUFjeEIsbUJBQUFBLG9CQUFBQSxTQUFVcUIsV0FBV3JCLE1BQU07SUFDL0MsTUFBTXlCLGVBQWV4QixvQkFBQUEscUJBQUFBLFVBQVdvQixXQUFXcEIsT0FBTztJQUVsRCxNQUFNeUIsYUFBYTtRQUNqQixJQUFJLENBQUNqQixhQUFha0IsT0FBTyxJQUFJLENBQUNqQixVQUFVaUIsT0FBTyxFQUFFO1FBRWpELE1BQU1DLE9BQU9uQixhQUFha0IsT0FBTyxDQUFDRSxxQkFBcUI7UUFDdkQsTUFBTXpDLFFBQVE1QixLQUFLc0UsS0FBSyxDQUFDRixLQUFLeEMsS0FBSztRQUNuQyxNQUFNQyxTQUFTN0IsS0FBS3NFLEtBQUssQ0FBQ0YsS0FBS3ZDLE1BQU07UUFDckMsTUFBTXZCLE1BQU00QyxVQUFVaUIsT0FBTyxDQUFDSSxVQUFVLENBQUM7UUFFekNyQixVQUFVaUIsT0FBTyxDQUFDdkMsS0FBSyxHQUFHQTtRQUMxQnNCLFVBQVVpQixPQUFPLENBQUN0QyxNQUFNLEdBQUdBO1FBQzNCcUIsVUFBVWlCLE9BQU8sQ0FBQ0ssS0FBSyxDQUFDNUMsS0FBSyxHQUFHLEdBQVMsT0FBTkEsT0FBTTtRQUN6Q3NCLFVBQVVpQixPQUFPLENBQUNLLEtBQUssQ0FBQzNDLE1BQU0sR0FBRyxHQUFVLE9BQVBBLFFBQU87UUFFM0MsTUFBTTRDLGNBQWNULFlBQVlVLEtBQUssQ0FBQztRQUN0QyxNQUFNQyxNQUFNLEVBQUU7UUFDZCxJQUFLLElBQUlqRSxJQUFJLEdBQUdBLElBQUlrQixPQUFPbEIsS0FBS3lCLFNBQVMyQixVQUFVLElBQUs7WUFDdEQsSUFBSyxJQUFJbkQsSUFBSSxHQUFHQSxJQUFJa0IsUUFBUWxCLEtBQUt3QixTQUFTMkIsVUFBVSxJQUFLO2dCQUN2RCxNQUFNdEQsUUFDSmlFLFdBQVcsQ0FBQ3pFLEtBQUtzRSxLQUFLLENBQUN0RSxLQUFLQyxNQUFNLEtBQUt3RSxZQUFZRyxNQUFNLEVBQUU7Z0JBRTdELE1BQU1DLEtBQUtuRSxJQUFJa0IsUUFBUTtnQkFDdkIsTUFBTWtELEtBQUtuRSxJQUFJa0IsU0FBUztnQkFDeEIsTUFBTWtELFdBQVcvRSxLQUFLZ0YsSUFBSSxDQUFDSCxLQUFLQSxLQUFLQyxLQUFLQTtnQkFDMUMsTUFBTS9ELFFBQVFpQixnQkFBZ0IsSUFBSStDO2dCQUVsQ0osSUFBSU0sSUFBSSxDQUNOLElBQUlyRixNQUNGc0QsVUFBVWlCLE9BQU8sRUFDakI3RCxLQUNBSSxHQUNBQyxHQUNBSCxPQUNBc0Isa0JBQWtCaUMsWUFBWS9CLGdCQUM5QmpCO1lBR047UUFDRjtRQUNBb0MsVUFBVWdCLE9BQU8sR0FBR1E7SUFDdEI7SUFFQSxNQUFNTyxZQUFZLENBQUNDO1lBU0xqQztRQVJaRSxhQUFhZSxPQUFPLEdBQUdpQixzQkFBc0IsSUFBTUYsVUFBVUM7UUFDN0QsTUFBTUUsVUFBVSxPQUFPL0IsZ0JBQWdCLGNBQWNBLFlBQVlDLEdBQUcsS0FBS0MsS0FBS0QsR0FBRztRQUNqRixNQUFNK0IsYUFBYUQsVUFBVWhDLGdCQUFnQmMsT0FBTztRQUNwRCxNQUFNb0IsZUFBZSxPQUFPLElBQUksVUFBVTtRQUUxQyxJQUFJRCxhQUFhQyxjQUFjO1FBQy9CbEMsZ0JBQWdCYyxPQUFPLEdBQUdrQixVQUFXQyxhQUFhQztRQUVsRCxNQUFNakYsT0FBTTRDLHFCQUFBQSxVQUFVaUIsT0FBTyxjQUFqQmpCLHlDQUFBQSxtQkFBbUJxQixVQUFVLENBQUM7UUFDMUMsSUFBSSxDQUFDakUsT0FBTyxDQUFDNEMsVUFBVWlCLE9BQU8sRUFBRTtRQUVoQzdELElBQUlrRixTQUFTLENBQUMsR0FBRyxHQUFHdEMsVUFBVWlCLE9BQU8sQ0FBQ3ZDLEtBQUssRUFBRXNCLFVBQVVpQixPQUFPLENBQUN0QyxNQUFNO1FBRXJFLElBQUk0RCxVQUFVO1FBQ2QsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUl2QyxVQUFVZ0IsT0FBTyxDQUFDUyxNQUFNLEVBQUVjLElBQUs7WUFDakQsTUFBTUMsUUFBUXhDLFVBQVVnQixPQUFPLENBQUN1QixFQUFFO1lBQ2xDQyxLQUFLLENBQUNSLE9BQU87WUFDYixJQUFJLENBQUNRLE1BQU05RSxNQUFNLEVBQUU7Z0JBQ2pCNEUsVUFBVTtZQUNaO1FBQ0Y7UUFDQSxJQUFJQSxTQUFTO1lBQ1hHLHFCQUFxQnhDLGFBQWFlLE9BQU87UUFDM0M7SUFDRjtJQUVBLE1BQU0wQixrQkFBa0IsQ0FBQ0M7UUFDdkJGLHFCQUFxQnhDLGFBQWFlLE9BQU87UUFDekNmLGFBQWFlLE9BQU8sR0FBR2lCLHNCQUFzQixJQUFNRixVQUFVWTtJQUMvRDtJQUVBLE1BQU1DLGVBQWUsSUFBTUYsZ0JBQWdCO0lBQzNDLE1BQU1HLGVBQWUsSUFBTUgsZ0JBQWdCO0lBQzNDLE1BQU1JLFVBQVUsQ0FBQ0M7UUFDZixJQUFJQSxFQUFFQyxhQUFhLENBQUNDLFFBQVEsQ0FBQ0YsRUFBRUcsYUFBYSxHQUFHO1FBQy9DUixnQkFBZ0I7SUFDbEI7SUFDQSxNQUFNUyxTQUFTLENBQUNKO1FBQ2QsSUFBSUEsRUFBRUMsYUFBYSxDQUFDQyxRQUFRLENBQUNGLEVBQUVHLGFBQWEsR0FBRztRQUMvQ1IsZ0JBQWdCO0lBQ2xCO0lBRUFwRyxnREFBU0EsQ0FBQztRQUNSeUU7UUFDQSxNQUFNcUMsV0FBVyxJQUFJQyxlQUFlO1lBQ2xDdEM7UUFDRjtRQUNBLElBQUlqQixhQUFha0IsT0FBTyxFQUFFO1lBQ3hCb0MsU0FBU0UsT0FBTyxDQUFDeEQsYUFBYWtCLE9BQU87UUFDdkM7UUFDQSxPQUFPO1lBQ0xvQyxTQUFTRyxVQUFVO1lBQ25CZCxxQkFBcUJ4QyxhQUFhZSxPQUFPO1FBQzNDO0lBQ0EsdURBQXVEO0lBQ3pELEdBQUc7UUFBQ0w7UUFBVUM7UUFBWUM7UUFBYUM7S0FBYTtJQUVwRCxxQkFDRSw4REFBQzBDO1FBQ0NDLEtBQUszRDtRQUNMRixXQUFXLGlLQUEySyxPQUFWQTtRQUM1S2dELGNBQWNBO1FBQ2RDLGNBQWNBO1FBQ2RDLFNBQVNoQyxlQUFlNEMsWUFBWVo7UUFDcENLLFFBQVFyQyxlQUFlNEMsWUFBWVA7UUFDbkNRLFVBQVU3QyxlQUFlLENBQUMsSUFBSTs7MEJBRTlCLDhEQUFDdkM7Z0JBQ0NxQixXQUFVO2dCQUNWNkQsS0FBSzFEOzs7Ozs7WUFFTkY7Ozs7Ozs7QUFHUDtHQWhKd0JIO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvdWkvcGl4ZWwtY2FyZC5qcz9jZjE0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuXG5jbGFzcyBQaXhlbCB7XG4gIGNvbnN0cnVjdG9yKGNhbnZhcywgY29udGV4dCwgeCwgeSwgY29sb3IsIHNwZWVkLCBkZWxheSkge1xuICAgIHRoaXMud2lkdGggPSBjYW52YXMud2lkdGg7XG4gICAgdGhpcy5oZWlnaHQgPSBjYW52YXMuaGVpZ2h0O1xuICAgIHRoaXMuY3R4ID0gY29udGV4dDtcbiAgICB0aGlzLnggPSB4O1xuICAgIHRoaXMueSA9IHk7XG4gICAgdGhpcy5jb2xvciA9IGNvbG9yO1xuICAgIHRoaXMuc3BlZWQgPSB0aGlzLmdldFJhbmRvbVZhbHVlKDAuMSwgMC45KSAqIHNwZWVkO1xuICAgIHRoaXMuc2l6ZSA9IDA7XG4gICAgdGhpcy5zaXplU3RlcCA9IE1hdGgucmFuZG9tKCkgKiAwLjYgKyAwLjI7XG4gICAgdGhpcy5taW5TaXplID0gMTtcbiAgICB0aGlzLm1heFNpemVJbnRlZ2VyID0gNDtcbiAgICB0aGlzLm1heFNpemUgPSB0aGlzLmdldFJhbmRvbVZhbHVlKHRoaXMubWluU2l6ZSwgdGhpcy5tYXhTaXplSW50ZWdlcik7XG4gICAgdGhpcy5kZWxheSA9IGRlbGF5O1xuICAgIHRoaXMuY291bnRlciA9IDA7XG4gICAgdGhpcy5jb3VudGVyU3RlcCA9IE1hdGgucmFuZG9tKCkgKiA0ICsgKHRoaXMud2lkdGggKyB0aGlzLmhlaWdodCkgKiAwLjAxO1xuICAgIHRoaXMuaXNJZGxlID0gZmFsc2U7XG4gICAgdGhpcy5pc1JldmVyc2UgPSBmYWxzZTtcbiAgICB0aGlzLmlzU2hpbW1lciA9IGZhbHNlO1xuICB9XG5cbiAgZ2V0UmFuZG9tVmFsdWUobWluLCBtYXgpIHtcbiAgICByZXR1cm4gTWF0aC5yYW5kb20oKSAqIChtYXggLSBtaW4pICsgbWluO1xuICB9XG5cbiAgZHJhdygpIHtcbiAgICBjb25zdCBjZW50ZXJPZmZzZXQgPSB0aGlzLm1heFNpemVJbnRlZ2VyICogMC41IC0gdGhpcy5zaXplICogMC41O1xuICAgIHRoaXMuY3R4LmZpbGxTdHlsZSA9IHRoaXMuY29sb3I7XG4gICAgdGhpcy5jdHguZmlsbFJlY3QoXG4gICAgICB0aGlzLnggKyBjZW50ZXJPZmZzZXQsXG4gICAgICB0aGlzLnkgKyBjZW50ZXJPZmZzZXQsXG4gICAgICB0aGlzLnNpemUsXG4gICAgICB0aGlzLnNpemVcbiAgICApO1xuICB9XG5cbiAgYXBwZWFyKCkge1xuICAgIHRoaXMuaXNJZGxlID0gZmFsc2U7XG4gICAgaWYgKHRoaXMuY291bnRlciA8PSB0aGlzLmRlbGF5KSB7XG4gICAgICB0aGlzLmNvdW50ZXIgKz0gdGhpcy5jb3VudGVyU3RlcDtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgaWYgKHRoaXMuc2l6ZSA+PSB0aGlzLm1heFNpemUpIHtcbiAgICAgIHRoaXMuaXNTaGltbWVyID0gdHJ1ZTtcbiAgICB9XG4gICAgaWYgKHRoaXMuaXNTaGltbWVyKSB7XG4gICAgICB0aGlzLnNoaW1tZXIoKTtcbiAgICB9IGVsc2Uge1xuICAgICAgdGhpcy5zaXplICs9IHRoaXMuc2l6ZVN0ZXA7XG4gICAgfVxuICAgIHRoaXMuZHJhdygpO1xuICB9XG5cbiAgZGlzYXBwZWFyKCkge1xuICAgIHRoaXMuaXNTaGltbWVyID0gZmFsc2U7XG4gICAgdGhpcy5jb3VudGVyID0gMDtcbiAgICBpZiAodGhpcy5zaXplIDw9IDApIHtcbiAgICAgIHRoaXMuaXNJZGxlID0gdHJ1ZTtcbiAgICAgIHJldHVybjtcbiAgICB9IGVsc2Uge1xuICAgICAgdGhpcy5zaXplIC09IDAuMTtcbiAgICB9XG4gICAgdGhpcy5kcmF3KCk7XG4gIH1cblxuICBzaGltbWVyKCkge1xuICAgIGlmICh0aGlzLnNpemUgPj0gdGhpcy5tYXhTaXplKSB7XG4gICAgICB0aGlzLmlzUmV2ZXJzZSA9IHRydWU7XG4gICAgfSBlbHNlIGlmICh0aGlzLnNpemUgPD0gdGhpcy5taW5TaXplKSB7XG4gICAgICB0aGlzLmlzUmV2ZXJzZSA9IGZhbHNlO1xuICAgIH1cbiAgICBpZiAodGhpcy5pc1JldmVyc2UpIHtcbiAgICAgIHRoaXMuc2l6ZSAtPSB0aGlzLnNwZWVkO1xuICAgIH0gZWxzZSB7XG4gICAgICB0aGlzLnNpemUgKz0gdGhpcy5zcGVlZDtcbiAgICB9XG4gIH1cbn1cblxuZnVuY3Rpb24gZ2V0RWZmZWN0aXZlU3BlZWQodmFsdWUsIHJlZHVjZWRNb3Rpb24pIHtcbiAgY29uc3QgbWluID0gMDtcbiAgY29uc3QgbWF4ID0gMTAwO1xuICBjb25zdCB0aHJvdHRsZSA9IDAuMDAxO1xuICBjb25zdCBwYXJzZWQgPSBwYXJzZUludCh2YWx1ZSwgMTApO1xuXG4gIGlmIChwYXJzZWQgPD0gbWluIHx8IHJlZHVjZWRNb3Rpb24pIHtcbiAgICByZXR1cm4gbWluO1xuICB9IGVsc2UgaWYgKHBhcnNlZCA+PSBtYXgpIHtcbiAgICByZXR1cm4gbWF4ICogdGhyb3R0bGU7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIHBhcnNlZCAqIHRocm90dGxlO1xuICB9XG59XG5cbi8qKlxuICogVmFyaWFudHMgYWRhcHRlZCBmb3IgdGhlIHByaWNpbmcgdGhlbWVcbiAqL1xuY29uc3QgVkFSSUFOVFMgPSB7XG4gIGRlZmF1bHQ6IHtcbiAgICBhY3RpdmVDb2xvcjogbnVsbCxcbiAgICBnYXA6IDQsXG4gICAgc3BlZWQ6IDUwLFxuICAgIGNvbG9yczogXCIjZTJlOGYwLCNjYmQ1ZTEsIzk0YTNiOCwjNjQ3NDhiXCIsXG4gICAgbm9Gb2N1czogZmFsc2VcbiAgfSxcbiAgcHVycGxlOiB7XG4gICAgYWN0aXZlQ29sb3I6IFwiI2U5ZDVmZlwiLFxuICAgIGdhcDogNCxcbiAgICBzcGVlZDogNjAsXG4gICAgY29sb3JzOiBcIiNlOWQ1ZmYsI2M0YjVmZCwjOGI1Y2Y2LCM3YzNhZWRcIixcbiAgICBub0ZvY3VzOiBmYWxzZVxuICB9LFxuICBibHVlOiB7XG4gICAgYWN0aXZlQ29sb3I6IFwiI2RiZWFmZVwiLFxuICAgIGdhcDogNCxcbiAgICBzcGVlZDogNTUsXG4gICAgY29sb3JzOiBcIiNkYmVhZmUsIzkzYzVmZCwjM2I4MmY2LCMxZDRlZDhcIixcbiAgICBub0ZvY3VzOiBmYWxzZVxuICB9LFxuICBoaWdobGlnaHRlZDoge1xuICAgIGFjdGl2ZUNvbG9yOiBcIiNlOWQ1ZmZcIixcbiAgICBnYXA6IDMsXG4gICAgc3BlZWQ6IDgwLFxuICAgIGNvbG9yczogXCIjZTlkNWZmLCNjNGI1ZmQsIzhiNWNmNiwjN2MzYWVkLCNkYmVhZmUsIzkzYzVmZCwjM2I4MmY2XCIsXG4gICAgbm9Gb2N1czogZmFsc2VcbiAgfVxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGl4ZWxDYXJkKHtcbiAgdmFyaWFudCA9IFwiZGVmYXVsdFwiLFxuICBnYXAsXG4gIHNwZWVkLFxuICBjb2xvcnMsXG4gIG5vRm9jdXMsXG4gIGNsYXNzTmFtZSA9IFwiXCIsXG4gIGNoaWxkcmVuXG59KSB7XG4gIGNvbnN0IGNvbnRhaW5lclJlZiA9IHVzZVJlZihudWxsKTtcbiAgY29uc3QgY2FudmFzUmVmID0gdXNlUmVmKG51bGwpO1xuICBjb25zdCBwaXhlbHNSZWYgPSB1c2VSZWYoW10pO1xuICBjb25zdCBhbmltYXRpb25SZWYgPSB1c2VSZWYobnVsbCk7XG4gIGNvbnN0IHRpbWVQcmV2aW91c1JlZiA9IHVzZVJlZih0eXBlb2YgcGVyZm9ybWFuY2UgIT09ICd1bmRlZmluZWQnID8gcGVyZm9ybWFuY2Uubm93KCkgOiBEYXRlLm5vdygpKTtcbiAgY29uc3QgW3JlZHVjZWRNb3Rpb24sIHNldFJlZHVjZWRNb3Rpb25dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBzZXRSZWR1Y2VkTW90aW9uKHdpbmRvdy5tYXRjaE1lZGlhKFwiKHByZWZlcnMtcmVkdWNlZC1tb3Rpb246IHJlZHVjZSlcIikubWF0Y2hlcyk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgY29uc3QgdmFyaWFudENmZyA9IFZBUklBTlRTW3ZhcmlhbnRdIHx8IFZBUklBTlRTLmRlZmF1bHQ7XG4gIGNvbnN0IGZpbmFsR2FwID0gZ2FwID8/IHZhcmlhbnRDZmcuZ2FwO1xuICBjb25zdCBmaW5hbFNwZWVkID0gc3BlZWQgPz8gdmFyaWFudENmZy5zcGVlZDtcbiAgY29uc3QgZmluYWxDb2xvcnMgPSBjb2xvcnMgPz8gdmFyaWFudENmZy5jb2xvcnM7XG4gIGNvbnN0IGZpbmFsTm9Gb2N1cyA9IG5vRm9jdXMgPz8gdmFyaWFudENmZy5ub0ZvY3VzO1xuXG4gIGNvbnN0IGluaXRQaXhlbHMgPSAoKSA9PiB7XG4gICAgaWYgKCFjb250YWluZXJSZWYuY3VycmVudCB8fCAhY2FudmFzUmVmLmN1cnJlbnQpIHJldHVybjtcblxuICAgIGNvbnN0IHJlY3QgPSBjb250YWluZXJSZWYuY3VycmVudC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcbiAgICBjb25zdCB3aWR0aCA9IE1hdGguZmxvb3IocmVjdC53aWR0aCk7XG4gICAgY29uc3QgaGVpZ2h0ID0gTWF0aC5mbG9vcihyZWN0LmhlaWdodCk7XG4gICAgY29uc3QgY3R4ID0gY2FudmFzUmVmLmN1cnJlbnQuZ2V0Q29udGV4dChcIjJkXCIpO1xuXG4gICAgY2FudmFzUmVmLmN1cnJlbnQud2lkdGggPSB3aWR0aDtcbiAgICBjYW52YXNSZWYuY3VycmVudC5oZWlnaHQgPSBoZWlnaHQ7XG4gICAgY2FudmFzUmVmLmN1cnJlbnQuc3R5bGUud2lkdGggPSBgJHt3aWR0aH1weGA7XG4gICAgY2FudmFzUmVmLmN1cnJlbnQuc3R5bGUuaGVpZ2h0ID0gYCR7aGVpZ2h0fXB4YDtcblxuICAgIGNvbnN0IGNvbG9yc0FycmF5ID0gZmluYWxDb2xvcnMuc3BsaXQoXCIsXCIpO1xuICAgIGNvbnN0IHB4cyA9IFtdO1xuICAgIGZvciAobGV0IHggPSAwOyB4IDwgd2lkdGg7IHggKz0gcGFyc2VJbnQoZmluYWxHYXAsIDEwKSkge1xuICAgICAgZm9yIChsZXQgeSA9IDA7IHkgPCBoZWlnaHQ7IHkgKz0gcGFyc2VJbnQoZmluYWxHYXAsIDEwKSkge1xuICAgICAgICBjb25zdCBjb2xvciA9XG4gICAgICAgICAgY29sb3JzQXJyYXlbTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogY29sb3JzQXJyYXkubGVuZ3RoKV07XG5cbiAgICAgICAgY29uc3QgZHggPSB4IC0gd2lkdGggLyAyO1xuICAgICAgICBjb25zdCBkeSA9IHkgLSBoZWlnaHQgLyAyO1xuICAgICAgICBjb25zdCBkaXN0YW5jZSA9IE1hdGguc3FydChkeCAqIGR4ICsgZHkgKiBkeSk7XG4gICAgICAgIGNvbnN0IGRlbGF5ID0gcmVkdWNlZE1vdGlvbiA/IDAgOiBkaXN0YW5jZTtcblxuICAgICAgICBweHMucHVzaChcbiAgICAgICAgICBuZXcgUGl4ZWwoXG4gICAgICAgICAgICBjYW52YXNSZWYuY3VycmVudCxcbiAgICAgICAgICAgIGN0eCxcbiAgICAgICAgICAgIHgsXG4gICAgICAgICAgICB5LFxuICAgICAgICAgICAgY29sb3IsXG4gICAgICAgICAgICBnZXRFZmZlY3RpdmVTcGVlZChmaW5hbFNwZWVkLCByZWR1Y2VkTW90aW9uKSxcbiAgICAgICAgICAgIGRlbGF5XG4gICAgICAgICAgKVxuICAgICAgICApO1xuICAgICAgfVxuICAgIH1cbiAgICBwaXhlbHNSZWYuY3VycmVudCA9IHB4cztcbiAgfTtcblxuICBjb25zdCBkb0FuaW1hdGUgPSAoZm5OYW1lKSA9PiB7XG4gICAgYW5pbWF0aW9uUmVmLmN1cnJlbnQgPSByZXF1ZXN0QW5pbWF0aW9uRnJhbWUoKCkgPT4gZG9BbmltYXRlKGZuTmFtZSkpO1xuICAgIGNvbnN0IHRpbWVOb3cgPSB0eXBlb2YgcGVyZm9ybWFuY2UgIT09ICd1bmRlZmluZWQnID8gcGVyZm9ybWFuY2Uubm93KCkgOiBEYXRlLm5vdygpO1xuICAgIGNvbnN0IHRpbWVQYXNzZWQgPSB0aW1lTm93IC0gdGltZVByZXZpb3VzUmVmLmN1cnJlbnQ7XG4gICAgY29uc3QgdGltZUludGVydmFsID0gMTAwMCAvIDYwOyAvLyB+NjAgRlBTXG5cbiAgICBpZiAodGltZVBhc3NlZCA8IHRpbWVJbnRlcnZhbCkgcmV0dXJuO1xuICAgIHRpbWVQcmV2aW91c1JlZi5jdXJyZW50ID0gdGltZU5vdyAtICh0aW1lUGFzc2VkICUgdGltZUludGVydmFsKTtcblxuICAgIGNvbnN0IGN0eCA9IGNhbnZhc1JlZi5jdXJyZW50Py5nZXRDb250ZXh0KFwiMmRcIik7XG4gICAgaWYgKCFjdHggfHwgIWNhbnZhc1JlZi5jdXJyZW50KSByZXR1cm47XG5cbiAgICBjdHguY2xlYXJSZWN0KDAsIDAsIGNhbnZhc1JlZi5jdXJyZW50LndpZHRoLCBjYW52YXNSZWYuY3VycmVudC5oZWlnaHQpO1xuXG4gICAgbGV0IGFsbElkbGUgPSB0cnVlO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcGl4ZWxzUmVmLmN1cnJlbnQubGVuZ3RoOyBpKyspIHtcbiAgICAgIGNvbnN0IHBpeGVsID0gcGl4ZWxzUmVmLmN1cnJlbnRbaV07XG4gICAgICBwaXhlbFtmbk5hbWVdKCk7XG4gICAgICBpZiAoIXBpeGVsLmlzSWRsZSkge1xuICAgICAgICBhbGxJZGxlID0gZmFsc2U7XG4gICAgICB9XG4gICAgfVxuICAgIGlmIChhbGxJZGxlKSB7XG4gICAgICBjYW5jZWxBbmltYXRpb25GcmFtZShhbmltYXRpb25SZWYuY3VycmVudCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUFuaW1hdGlvbiA9IChuYW1lKSA9PiB7XG4gICAgY2FuY2VsQW5pbWF0aW9uRnJhbWUoYW5pbWF0aW9uUmVmLmN1cnJlbnQpO1xuICAgIGFuaW1hdGlvblJlZi5jdXJyZW50ID0gcmVxdWVzdEFuaW1hdGlvbkZyYW1lKCgpID0+IGRvQW5pbWF0ZShuYW1lKSk7XG4gIH07XG5cbiAgY29uc3Qgb25Nb3VzZUVudGVyID0gKCkgPT4gaGFuZGxlQW5pbWF0aW9uKFwiYXBwZWFyXCIpO1xuICBjb25zdCBvbk1vdXNlTGVhdmUgPSAoKSA9PiBoYW5kbGVBbmltYXRpb24oXCJkaXNhcHBlYXJcIik7XG4gIGNvbnN0IG9uRm9jdXMgPSAoZSkgPT4ge1xuICAgIGlmIChlLmN1cnJlbnRUYXJnZXQuY29udGFpbnMoZS5yZWxhdGVkVGFyZ2V0KSkgcmV0dXJuO1xuICAgIGhhbmRsZUFuaW1hdGlvbihcImFwcGVhclwiKTtcbiAgfTtcbiAgY29uc3Qgb25CbHVyID0gKGUpID0+IHtcbiAgICBpZiAoZS5jdXJyZW50VGFyZ2V0LmNvbnRhaW5zKGUucmVsYXRlZFRhcmdldCkpIHJldHVybjtcbiAgICBoYW5kbGVBbmltYXRpb24oXCJkaXNhcHBlYXJcIik7XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpbml0UGl4ZWxzKCk7XG4gICAgY29uc3Qgb2JzZXJ2ZXIgPSBuZXcgUmVzaXplT2JzZXJ2ZXIoKCkgPT4ge1xuICAgICAgaW5pdFBpeGVscygpO1xuICAgIH0pO1xuICAgIGlmIChjb250YWluZXJSZWYuY3VycmVudCkge1xuICAgICAgb2JzZXJ2ZXIub2JzZXJ2ZShjb250YWluZXJSZWYuY3VycmVudCk7XG4gICAgfVxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBvYnNlcnZlci5kaXNjb25uZWN0KCk7XG4gICAgICBjYW5jZWxBbmltYXRpb25GcmFtZShhbmltYXRpb25SZWYuY3VycmVudCk7XG4gICAgfTtcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXG4gIH0sIFtmaW5hbEdhcCwgZmluYWxTcGVlZCwgZmluYWxDb2xvcnMsIGZpbmFsTm9Gb2N1c10pO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgcmVmPXtjb250YWluZXJSZWZ9XG4gICAgICBjbGFzc05hbWU9e2ByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIGlzb2xhdGUgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGVhc2UtW2N1YmljLWJlemllcigwLjUsMSwwLjg5LDEpXSBzZWxlY3Qtbm9uZSBtaW4taC1bNDAwcHhdICR7Y2xhc3NOYW1lfWB9XG4gICAgICBvbk1vdXNlRW50ZXI9e29uTW91c2VFbnRlcn1cbiAgICAgIG9uTW91c2VMZWF2ZT17b25Nb3VzZUxlYXZlfVxuICAgICAgb25Gb2N1cz17ZmluYWxOb0ZvY3VzID8gdW5kZWZpbmVkIDogb25Gb2N1c31cbiAgICAgIG9uQmx1cj17ZmluYWxOb0ZvY3VzID8gdW5kZWZpbmVkIDogb25CbHVyfVxuICAgICAgdGFiSW5kZXg9e2ZpbmFsTm9Gb2N1cyA/IC0xIDogMH1cbiAgICA+XG4gICAgICA8Y2FudmFzXG4gICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgdy1mdWxsIGgtZnVsbCBibG9jayBwb2ludGVyLWV2ZW50cy1ub25lXCJcbiAgICAgICAgcmVmPXtjYW52YXNSZWZ9XG4gICAgICAvPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVN0YXRlIiwiUGl4ZWwiLCJnZXRSYW5kb21WYWx1ZSIsIm1pbiIsIm1heCIsIk1hdGgiLCJyYW5kb20iLCJkcmF3IiwiY2VudGVyT2Zmc2V0IiwibWF4U2l6ZUludGVnZXIiLCJzaXplIiwiY3R4IiwiZmlsbFN0eWxlIiwiY29sb3IiLCJmaWxsUmVjdCIsIngiLCJ5IiwiYXBwZWFyIiwiaXNJZGxlIiwiY291bnRlciIsImRlbGF5IiwiY291bnRlclN0ZXAiLCJtYXhTaXplIiwiaXNTaGltbWVyIiwic2hpbW1lciIsInNpemVTdGVwIiwiZGlzYXBwZWFyIiwiaXNSZXZlcnNlIiwibWluU2l6ZSIsInNwZWVkIiwiY29uc3RydWN0b3IiLCJjYW52YXMiLCJjb250ZXh0Iiwid2lkdGgiLCJoZWlnaHQiLCJnZXRFZmZlY3RpdmVTcGVlZCIsInZhbHVlIiwicmVkdWNlZE1vdGlvbiIsInRocm90dGxlIiwicGFyc2VkIiwicGFyc2VJbnQiLCJWQVJJQU5UUyIsImRlZmF1bHQiLCJhY3RpdmVDb2xvciIsImdhcCIsImNvbG9ycyIsIm5vRm9jdXMiLCJwdXJwbGUiLCJibHVlIiwiaGlnaGxpZ2h0ZWQiLCJQaXhlbENhcmQiLCJ2YXJpYW50IiwiY2xhc3NOYW1lIiwiY2hpbGRyZW4iLCJjb250YWluZXJSZWYiLCJjYW52YXNSZWYiLCJwaXhlbHNSZWYiLCJhbmltYXRpb25SZWYiLCJ0aW1lUHJldmlvdXNSZWYiLCJwZXJmb3JtYW5jZSIsIm5vdyIsIkRhdGUiLCJzZXRSZWR1Y2VkTW90aW9uIiwid2luZG93IiwibWF0Y2hNZWRpYSIsIm1hdGNoZXMiLCJ2YXJpYW50Q2ZnIiwiZmluYWxHYXAiLCJmaW5hbFNwZWVkIiwiZmluYWxDb2xvcnMiLCJmaW5hbE5vRm9jdXMiLCJpbml0UGl4ZWxzIiwiY3VycmVudCIsInJlY3QiLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJmbG9vciIsImdldENvbnRleHQiLCJzdHlsZSIsImNvbG9yc0FycmF5Iiwic3BsaXQiLCJweHMiLCJsZW5ndGgiLCJkeCIsImR5IiwiZGlzdGFuY2UiLCJzcXJ0IiwicHVzaCIsImRvQW5pbWF0ZSIsImZuTmFtZSIsInJlcXVlc3RBbmltYXRpb25GcmFtZSIsInRpbWVOb3ciLCJ0aW1lUGFzc2VkIiwidGltZUludGVydmFsIiwiY2xlYXJSZWN0IiwiYWxsSWRsZSIsImkiLCJwaXhlbCIsImNhbmNlbEFuaW1hdGlvbkZyYW1lIiwiaGFuZGxlQW5pbWF0aW9uIiwibmFtZSIsIm9uTW91c2VFbnRlciIsIm9uTW91c2VMZWF2ZSIsIm9uRm9jdXMiLCJlIiwiY3VycmVudFRhcmdldCIsImNvbnRhaW5zIiwicmVsYXRlZFRhcmdldCIsIm9uQmx1ciIsIm9ic2VydmVyIiwiUmVzaXplT2JzZXJ2ZXIiLCJvYnNlcnZlIiwiZGlzY29ubmVjdCIsImRpdiIsInJlZiIsInVuZGVmaW5lZCIsInRhYkluZGV4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/ui/pixel-card.js\n"));

/***/ })

});