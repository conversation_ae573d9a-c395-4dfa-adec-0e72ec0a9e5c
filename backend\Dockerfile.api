FROM python:3.10-slim

WORKDIR /app

# 安装系统依赖（API服务只需要基本依赖）
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 创建必要的目录
RUN mkdir -p /app/data/pdf_upload /app/data/script_upload /app/data/results

# 复制应用代码
COPY app /app/app

# 创建一个空的.env文件，确保环境变量文件存在
RUN touch /app/.env

# 注意：实际的.env文件应该通过flyctl secrets set命令设置
# 或者在部署前手动创建

# 设置工作目录
WORKDIR /app/app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PDF_UPLOAD_FOLDER=/app/data/pdf_upload
ENV SCRIPT_UPLOAD_FOLDER=/app/data/script_upload
ENV RESULT_FOLDER=/app/data/results
ENV PORT=8000

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
