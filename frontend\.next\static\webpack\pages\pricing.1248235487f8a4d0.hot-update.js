"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/pricing",{

/***/ "./components/ui/pixel-card.js":
/*!*************************************!*\
  !*** ./components/ui/pixel-card.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PixelCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nclass Pixel {\n    getRandomValue(min, max) {\n        return Math.random() * (max - min) + min;\n    }\n    draw() {\n        const centerOffset = this.maxSizeInteger * 0.5 - this.size * 0.5;\n        this.ctx.fillStyle = this.color;\n        this.ctx.fillRect(this.x + centerOffset, this.y + centerOffset, this.size, this.size);\n    }\n    appear() {\n        this.isIdle = false;\n        if (this.counter <= this.delay) {\n            this.counter += this.counterStep;\n            return;\n        }\n        if (this.size >= this.maxSize) {\n            this.isShimmer = true;\n        }\n        if (this.isShimmer) {\n            this.shimmer();\n        } else {\n            this.size += this.sizeStep;\n        }\n        this.draw();\n    }\n    disappear() {\n        this.isShimmer = false;\n        this.counter = 0;\n        if (this.size <= 0) {\n            this.isIdle = true;\n            return;\n        } else {\n            this.size -= 0.1;\n        }\n        this.draw();\n    }\n    shimmer() {\n        if (this.size >= this.maxSize) {\n            this.isReverse = true;\n        } else if (this.size <= this.minSize) {\n            this.isReverse = false;\n        }\n        if (this.isReverse) {\n            this.size -= this.speed;\n        } else {\n            this.size += this.speed;\n        }\n    }\n    constructor(canvas, context, x, y, color, speed, delay){\n        this.width = canvas.width;\n        this.height = canvas.height;\n        this.ctx = context;\n        this.x = x;\n        this.y = y;\n        this.color = color;\n        this.speed = this.getRandomValue(0.1, 0.9) * speed;\n        this.size = 0;\n        this.sizeStep = Math.random() * 0.4;\n        this.minSize = 0.5;\n        this.maxSizeInteger = 2;\n        this.maxSize = this.getRandomValue(this.minSize, this.maxSizeInteger);\n        this.delay = delay;\n        this.counter = 0;\n        this.counterStep = Math.random() * 4 + (this.width + this.height) * 0.01;\n        this.isIdle = false;\n        this.isReverse = false;\n        this.isShimmer = false;\n    }\n}\nfunction getEffectiveSpeed(value, reducedMotion) {\n    const min = 0;\n    const max = 100;\n    const throttle = 0.001;\n    const parsed = parseInt(value, 10);\n    if (parsed <= min || reducedMotion) {\n        return min;\n    } else if (parsed >= max) {\n        return max * throttle;\n    } else {\n        return parsed * throttle;\n    }\n}\n/**\n * Variants adapted for the pricing theme\n */ const VARIANTS = {\n    default: {\n        activeColor: null,\n        gap: 5,\n        speed: 35,\n        colors: \"#f8fafc,#f1f5f9,#cbd5e1\",\n        noFocus: false\n    },\n    purple: {\n        activeColor: \"#e9d5ff\",\n        gap: 6,\n        speed: 40,\n        colors: \"#e9d5ff,#c4b5fd,#8b5cf6\",\n        noFocus: false\n    },\n    blue: {\n        activeColor: \"#dbeafe\",\n        gap: 5,\n        speed: 30,\n        colors: \"#dbeafe,#93c5fd,#3b82f6\",\n        noFocus: false\n    },\n    highlighted: {\n        activeColor: \"#e9d5ff\",\n        gap: 4,\n        speed: 60,\n        colors: \"#e9d5ff,#c4b5fd,#8b5cf6,#dbeafe,#93c5fd\",\n        noFocus: false\n    }\n};\nfunction PixelCard(param) {\n    let { variant = \"default\", gap, speed, colors, noFocus, className = \"\", children } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pixelsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const timePreviousRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(performance.now());\n    const reducedMotion = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)( true ? window.matchMedia(\"(prefers-reduced-motion: reduce)\").matches : 0).current;\n    const variantCfg = VARIANTS[variant] || VARIANTS.default;\n    const finalGap = gap !== null && gap !== void 0 ? gap : variantCfg.gap;\n    const finalSpeed = speed !== null && speed !== void 0 ? speed : variantCfg.speed;\n    const finalColors = colors !== null && colors !== void 0 ? colors : variantCfg.colors;\n    const finalNoFocus = noFocus !== null && noFocus !== void 0 ? noFocus : variantCfg.noFocus;\n    const initPixels = ()=>{\n        if (!containerRef.current || !canvasRef.current) return;\n        const rect = containerRef.current.getBoundingClientRect();\n        const width = Math.floor(rect.width);\n        const height = Math.floor(rect.height);\n        const ctx = canvasRef.current.getContext(\"2d\");\n        canvasRef.current.width = width;\n        canvasRef.current.height = height;\n        canvasRef.current.style.width = \"\".concat(width, \"px\");\n        canvasRef.current.style.height = \"\".concat(height, \"px\");\n        const colorsArray = finalColors.split(\",\");\n        const pxs = [];\n        for(let x = 0; x < width; x += parseInt(finalGap, 10)){\n            for(let y = 0; y < height; y += parseInt(finalGap, 10)){\n                const color = colorsArray[Math.floor(Math.random() * colorsArray.length)];\n                const dx = x - width / 2;\n                const dy = y - height / 2;\n                const distance = Math.sqrt(dx * dx + dy * dy);\n                const delay = reducedMotion ? 0 : distance;\n                pxs.push(new Pixel(canvasRef.current, ctx, x, y, color, getEffectiveSpeed(finalSpeed, reducedMotion), delay));\n            }\n        }\n        pixelsRef.current = pxs;\n    };\n    const doAnimate = (fnName)=>{\n        var _canvasRef_current;\n        animationRef.current = requestAnimationFrame(()=>doAnimate(fnName));\n        const timeNow = performance.now();\n        const timePassed = timeNow - timePreviousRef.current;\n        const timeInterval = 1000 / 60; // ~60 FPS\n        if (timePassed < timeInterval) return;\n        timePreviousRef.current = timeNow - timePassed % timeInterval;\n        const ctx = (_canvasRef_current = canvasRef.current) === null || _canvasRef_current === void 0 ? void 0 : _canvasRef_current.getContext(\"2d\");\n        if (!ctx || !canvasRef.current) return;\n        ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);\n        let allIdle = true;\n        for(let i = 0; i < pixelsRef.current.length; i++){\n            const pixel = pixelsRef.current[i];\n            pixel[fnName]();\n            if (!pixel.isIdle) {\n                allIdle = false;\n            }\n        }\n        if (allIdle) {\n            cancelAnimationFrame(animationRef.current);\n        }\n    };\n    const handleAnimation = (name)=>{\n        cancelAnimationFrame(animationRef.current);\n        animationRef.current = requestAnimationFrame(()=>doAnimate(name));\n    };\n    const onMouseEnter = ()=>handleAnimation(\"appear\");\n    const onMouseLeave = ()=>handleAnimation(\"disappear\");\n    const onFocus = (e)=>{\n        if (e.currentTarget.contains(e.relatedTarget)) return;\n        handleAnimation(\"appear\");\n    };\n    const onBlur = (e)=>{\n        if (e.currentTarget.contains(e.relatedTarget)) return;\n        handleAnimation(\"disappear\");\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initPixels();\n        const observer = new ResizeObserver(()=>{\n            initPixels();\n        });\n        if (containerRef.current) {\n            observer.observe(containerRef.current);\n        }\n        return ()=>{\n            observer.disconnect();\n            cancelAnimationFrame(animationRef.current);\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        finalGap,\n        finalSpeed,\n        finalColors,\n        finalNoFocus\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"relative overflow-hidden rounded-xl border border-white/10 isolate transition-colors duration-200 ease-[cubic-bezier(0.5,1,0.89,1)] select-none min-h-[400px] \".concat(className),\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onFocus: finalNoFocus ? undefined : onFocus,\n        onBlur: finalNoFocus ? undefined : onBlur,\n        tabIndex: finalNoFocus ? -1 : 0,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                className: \"absolute inset-0 w-full h-full block pointer-events-none\",\n                ref: canvasRef\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\pixel-card.js\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\pixel-card.js\",\n        lineNumber: 257,\n        columnNumber: 5\n    }, this);\n}\n_s(PixelCard, \"1g2TH4O5gnWsyabujaCI4QYsjeI=\");\n_c = PixelCard;\nvar _c;\n$RefreshReg$(_c, \"PixelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3VpL3BpeGVsLWNhcmQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEwQztBQUUxQyxNQUFNRTtJQXNCSkMsZUFBZUMsR0FBRyxFQUFFQyxHQUFHLEVBQUU7UUFDdkIsT0FBT0MsS0FBS0MsTUFBTSxLQUFNRixDQUFBQSxNQUFNRCxHQUFFLElBQUtBO0lBQ3ZDO0lBRUFJLE9BQU87UUFDTCxNQUFNQyxlQUFlLElBQUksQ0FBQ0MsY0FBYyxHQUFHLE1BQU0sSUFBSSxDQUFDQyxJQUFJLEdBQUc7UUFDN0QsSUFBSSxDQUFDQyxHQUFHLENBQUNDLFNBQVMsR0FBRyxJQUFJLENBQUNDLEtBQUs7UUFDL0IsSUFBSSxDQUFDRixHQUFHLENBQUNHLFFBQVEsQ0FDZixJQUFJLENBQUNDLENBQUMsR0FBR1AsY0FDVCxJQUFJLENBQUNRLENBQUMsR0FBR1IsY0FDVCxJQUFJLENBQUNFLElBQUksRUFDVCxJQUFJLENBQUNBLElBQUk7SUFFYjtJQUVBTyxTQUFTO1FBQ1AsSUFBSSxDQUFDQyxNQUFNLEdBQUc7UUFDZCxJQUFJLElBQUksQ0FBQ0MsT0FBTyxJQUFJLElBQUksQ0FBQ0MsS0FBSyxFQUFFO1lBQzlCLElBQUksQ0FBQ0QsT0FBTyxJQUFJLElBQUksQ0FBQ0UsV0FBVztZQUNoQztRQUNGO1FBQ0EsSUFBSSxJQUFJLENBQUNYLElBQUksSUFBSSxJQUFJLENBQUNZLE9BQU8sRUFBRTtZQUM3QixJQUFJLENBQUNDLFNBQVMsR0FBRztRQUNuQjtRQUNBLElBQUksSUFBSSxDQUFDQSxTQUFTLEVBQUU7WUFDbEIsSUFBSSxDQUFDQyxPQUFPO1FBQ2QsT0FBTztZQUNMLElBQUksQ0FBQ2QsSUFBSSxJQUFJLElBQUksQ0FBQ2UsUUFBUTtRQUM1QjtRQUNBLElBQUksQ0FBQ2xCLElBQUk7SUFDWDtJQUVBbUIsWUFBWTtRQUNWLElBQUksQ0FBQ0gsU0FBUyxHQUFHO1FBQ2pCLElBQUksQ0FBQ0osT0FBTyxHQUFHO1FBQ2YsSUFBSSxJQUFJLENBQUNULElBQUksSUFBSSxHQUFHO1lBQ2xCLElBQUksQ0FBQ1EsTUFBTSxHQUFHO1lBQ2Q7UUFDRixPQUFPO1lBQ0wsSUFBSSxDQUFDUixJQUFJLElBQUk7UUFDZjtRQUNBLElBQUksQ0FBQ0gsSUFBSTtJQUNYO0lBRUFpQixVQUFVO1FBQ1IsSUFBSSxJQUFJLENBQUNkLElBQUksSUFBSSxJQUFJLENBQUNZLE9BQU8sRUFBRTtZQUM3QixJQUFJLENBQUNLLFNBQVMsR0FBRztRQUNuQixPQUFPLElBQUksSUFBSSxDQUFDakIsSUFBSSxJQUFJLElBQUksQ0FBQ2tCLE9BQU8sRUFBRTtZQUNwQyxJQUFJLENBQUNELFNBQVMsR0FBRztRQUNuQjtRQUNBLElBQUksSUFBSSxDQUFDQSxTQUFTLEVBQUU7WUFDbEIsSUFBSSxDQUFDakIsSUFBSSxJQUFJLElBQUksQ0FBQ21CLEtBQUs7UUFDekIsT0FBTztZQUNMLElBQUksQ0FBQ25CLElBQUksSUFBSSxJQUFJLENBQUNtQixLQUFLO1FBQ3pCO0lBQ0Y7SUE1RUFDLFlBQVlDLE1BQU0sRUFBRUMsT0FBTyxFQUFFakIsQ0FBQyxFQUFFQyxDQUFDLEVBQUVILEtBQUssRUFBRWdCLEtBQUssRUFBRVQsS0FBSyxDQUFFO1FBQ3RELElBQUksQ0FBQ2EsS0FBSyxHQUFHRixPQUFPRSxLQUFLO1FBQ3pCLElBQUksQ0FBQ0MsTUFBTSxHQUFHSCxPQUFPRyxNQUFNO1FBQzNCLElBQUksQ0FBQ3ZCLEdBQUcsR0FBR3FCO1FBQ1gsSUFBSSxDQUFDakIsQ0FBQyxHQUFHQTtRQUNULElBQUksQ0FBQ0MsQ0FBQyxHQUFHQTtRQUNULElBQUksQ0FBQ0gsS0FBSyxHQUFHQTtRQUNiLElBQUksQ0FBQ2dCLEtBQUssR0FBRyxJQUFJLENBQUMzQixjQUFjLENBQUMsS0FBSyxPQUFPMkI7UUFDN0MsSUFBSSxDQUFDbkIsSUFBSSxHQUFHO1FBQ1osSUFBSSxDQUFDZSxRQUFRLEdBQUdwQixLQUFLQyxNQUFNLEtBQUs7UUFDaEMsSUFBSSxDQUFDc0IsT0FBTyxHQUFHO1FBQ2YsSUFBSSxDQUFDbkIsY0FBYyxHQUFHO1FBQ3RCLElBQUksQ0FBQ2EsT0FBTyxHQUFHLElBQUksQ0FBQ3BCLGNBQWMsQ0FBQyxJQUFJLENBQUMwQixPQUFPLEVBQUUsSUFBSSxDQUFDbkIsY0FBYztRQUNwRSxJQUFJLENBQUNXLEtBQUssR0FBR0E7UUFDYixJQUFJLENBQUNELE9BQU8sR0FBRztRQUNmLElBQUksQ0FBQ0UsV0FBVyxHQUFHaEIsS0FBS0MsTUFBTSxLQUFLLElBQUksQ0FBQyxJQUFJLENBQUMyQixLQUFLLEdBQUcsSUFBSSxDQUFDQyxNQUFNLElBQUk7UUFDcEUsSUFBSSxDQUFDaEIsTUFBTSxHQUFHO1FBQ2QsSUFBSSxDQUFDUyxTQUFTLEdBQUc7UUFDakIsSUFBSSxDQUFDSixTQUFTLEdBQUc7SUFDbkI7QUEwREY7QUFFQSxTQUFTWSxrQkFBa0JDLEtBQUssRUFBRUMsYUFBYTtJQUM3QyxNQUFNbEMsTUFBTTtJQUNaLE1BQU1DLE1BQU07SUFDWixNQUFNa0MsV0FBVztJQUNqQixNQUFNQyxTQUFTQyxTQUFTSixPQUFPO0lBRS9CLElBQUlHLFVBQVVwQyxPQUFPa0MsZUFBZTtRQUNsQyxPQUFPbEM7SUFDVCxPQUFPLElBQUlvQyxVQUFVbkMsS0FBSztRQUN4QixPQUFPQSxNQUFNa0M7SUFDZixPQUFPO1FBQ0wsT0FBT0MsU0FBU0Q7SUFDbEI7QUFDRjtBQUVBOztDQUVDLEdBQ0QsTUFBTUcsV0FBVztJQUNmQyxTQUFTO1FBQ1BDLGFBQWE7UUFDYkMsS0FBSztRQUNMZixPQUFPO1FBQ1BnQixRQUFRO1FBQ1JDLFNBQVM7SUFDWDtJQUNBQyxRQUFRO1FBQ05KLGFBQWE7UUFDYkMsS0FBSztRQUNMZixPQUFPO1FBQ1BnQixRQUFRO1FBQ1JDLFNBQVM7SUFDWDtJQUNBRSxNQUFNO1FBQ0pMLGFBQWE7UUFDYkMsS0FBSztRQUNMZixPQUFPO1FBQ1BnQixRQUFRO1FBQ1JDLFNBQVM7SUFDWDtJQUNBRyxhQUFhO1FBQ1hOLGFBQWE7UUFDYkMsS0FBSztRQUNMZixPQUFPO1FBQ1BnQixRQUFRO1FBQ1JDLFNBQVM7SUFDWDtBQUNGO0FBRWUsU0FBU0ksVUFBVSxLQVFqQztRQVJpQyxFQUNoQ0MsVUFBVSxTQUFTLEVBQ25CUCxHQUFHLEVBQ0hmLEtBQUssRUFDTGdCLE1BQU0sRUFDTkMsT0FBTyxFQUNQTSxZQUFZLEVBQUUsRUFDZEMsUUFBUSxFQUNULEdBUmlDOztJQVNoQyxNQUFNQyxlQUFldEQsNkNBQU1BLENBQUM7SUFDNUIsTUFBTXVELFlBQVl2RCw2Q0FBTUEsQ0FBQztJQUN6QixNQUFNd0QsWUFBWXhELDZDQUFNQSxDQUFDLEVBQUU7SUFDM0IsTUFBTXlELGVBQWV6RCw2Q0FBTUEsQ0FBQztJQUM1QixNQUFNMEQsa0JBQWtCMUQsNkNBQU1BLENBQUMyRCxZQUFZQyxHQUFHO0lBQzlDLE1BQU12QixnQkFBZ0JyQyw2Q0FBTUEsQ0FDMUIsS0FBa0IsR0FDaEI2RCxPQUFPQyxVQUFVLENBQUMsb0NBQW9DQyxPQUFPLEdBQUcsQ0FBSyxFQUN2RUMsT0FBTztJQUVULE1BQU1DLGFBQWF4QixRQUFRLENBQUNVLFFBQVEsSUFBSVYsU0FBU0MsT0FBTztJQUN4RCxNQUFNd0IsV0FBV3RCLGdCQUFBQSxpQkFBQUEsTUFBT3FCLFdBQVdyQixHQUFHO0lBQ3RDLE1BQU11QixhQUFhdEMsa0JBQUFBLG1CQUFBQSxRQUFTb0MsV0FBV3BDLEtBQUs7SUFDNUMsTUFBTXVDLGNBQWN2QixtQkFBQUEsb0JBQUFBLFNBQVVvQixXQUFXcEIsTUFBTTtJQUMvQyxNQUFNd0IsZUFBZXZCLG9CQUFBQSxxQkFBQUEsVUFBV21CLFdBQVduQixPQUFPO0lBRWxELE1BQU13QixhQUFhO1FBQ2pCLElBQUksQ0FBQ2hCLGFBQWFVLE9BQU8sSUFBSSxDQUFDVCxVQUFVUyxPQUFPLEVBQUU7UUFFakQsTUFBTU8sT0FBT2pCLGFBQWFVLE9BQU8sQ0FBQ1EscUJBQXFCO1FBQ3ZELE1BQU12QyxRQUFRNUIsS0FBS29FLEtBQUssQ0FBQ0YsS0FBS3RDLEtBQUs7UUFDbkMsTUFBTUMsU0FBUzdCLEtBQUtvRSxLQUFLLENBQUNGLEtBQUtyQyxNQUFNO1FBQ3JDLE1BQU12QixNQUFNNEMsVUFBVVMsT0FBTyxDQUFDVSxVQUFVLENBQUM7UUFFekNuQixVQUFVUyxPQUFPLENBQUMvQixLQUFLLEdBQUdBO1FBQzFCc0IsVUFBVVMsT0FBTyxDQUFDOUIsTUFBTSxHQUFHQTtRQUMzQnFCLFVBQVVTLE9BQU8sQ0FBQ1csS0FBSyxDQUFDMUMsS0FBSyxHQUFHLEdBQVMsT0FBTkEsT0FBTTtRQUN6Q3NCLFVBQVVTLE9BQU8sQ0FBQ1csS0FBSyxDQUFDekMsTUFBTSxHQUFHLEdBQVUsT0FBUEEsUUFBTztRQUUzQyxNQUFNMEMsY0FBY1IsWUFBWVMsS0FBSyxDQUFDO1FBQ3RDLE1BQU1DLE1BQU0sRUFBRTtRQUNkLElBQUssSUFBSS9ELElBQUksR0FBR0EsSUFBSWtCLE9BQU9sQixLQUFLeUIsU0FBUzBCLFVBQVUsSUFBSztZQUN0RCxJQUFLLElBQUlsRCxJQUFJLEdBQUdBLElBQUlrQixRQUFRbEIsS0FBS3dCLFNBQVMwQixVQUFVLElBQUs7Z0JBQ3ZELE1BQU1yRCxRQUNKK0QsV0FBVyxDQUFDdkUsS0FBS29FLEtBQUssQ0FBQ3BFLEtBQUtDLE1BQU0sS0FBS3NFLFlBQVlHLE1BQU0sRUFBRTtnQkFFN0QsTUFBTUMsS0FBS2pFLElBQUlrQixRQUFRO2dCQUN2QixNQUFNZ0QsS0FBS2pFLElBQUlrQixTQUFTO2dCQUN4QixNQUFNZ0QsV0FBVzdFLEtBQUs4RSxJQUFJLENBQUNILEtBQUtBLEtBQUtDLEtBQUtBO2dCQUMxQyxNQUFNN0QsUUFBUWlCLGdCQUFnQixJQUFJNkM7Z0JBRWxDSixJQUFJTSxJQUFJLENBQ04sSUFBSW5GLE1BQ0ZzRCxVQUFVUyxPQUFPLEVBQ2pCckQsS0FDQUksR0FDQUMsR0FDQUgsT0FDQXNCLGtCQUFrQmdDLFlBQVk5QixnQkFDOUJqQjtZQUdOO1FBQ0Y7UUFDQW9DLFVBQVVRLE9BQU8sR0FBR2M7SUFDdEI7SUFFQSxNQUFNTyxZQUFZLENBQUNDO1lBU0wvQjtRQVJaRSxhQUFhTyxPQUFPLEdBQUd1QixzQkFBc0IsSUFBTUYsVUFBVUM7UUFDN0QsTUFBTUUsVUFBVTdCLFlBQVlDLEdBQUc7UUFDL0IsTUFBTTZCLGFBQWFELFVBQVU5QixnQkFBZ0JNLE9BQU87UUFDcEQsTUFBTTBCLGVBQWUsT0FBTyxJQUFJLFVBQVU7UUFFMUMsSUFBSUQsYUFBYUMsY0FBYztRQUMvQmhDLGdCQUFnQk0sT0FBTyxHQUFHd0IsVUFBV0MsYUFBYUM7UUFFbEQsTUFBTS9FLE9BQU00QyxxQkFBQUEsVUFBVVMsT0FBTyxjQUFqQlQseUNBQUFBLG1CQUFtQm1CLFVBQVUsQ0FBQztRQUMxQyxJQUFJLENBQUMvRCxPQUFPLENBQUM0QyxVQUFVUyxPQUFPLEVBQUU7UUFFaENyRCxJQUFJZ0YsU0FBUyxDQUFDLEdBQUcsR0FBR3BDLFVBQVVTLE9BQU8sQ0FBQy9CLEtBQUssRUFBRXNCLFVBQVVTLE9BQU8sQ0FBQzlCLE1BQU07UUFFckUsSUFBSTBELFVBQVU7UUFDZCxJQUFLLElBQUlDLElBQUksR0FBR0EsSUFBSXJDLFVBQVVRLE9BQU8sQ0FBQ2UsTUFBTSxFQUFFYyxJQUFLO1lBQ2pELE1BQU1DLFFBQVF0QyxVQUFVUSxPQUFPLENBQUM2QixFQUFFO1lBQ2xDQyxLQUFLLENBQUNSLE9BQU87WUFDYixJQUFJLENBQUNRLE1BQU01RSxNQUFNLEVBQUU7Z0JBQ2pCMEUsVUFBVTtZQUNaO1FBQ0Y7UUFDQSxJQUFJQSxTQUFTO1lBQ1hHLHFCQUFxQnRDLGFBQWFPLE9BQU87UUFDM0M7SUFDRjtJQUVBLE1BQU1nQyxrQkFBa0IsQ0FBQ0M7UUFDdkJGLHFCQUFxQnRDLGFBQWFPLE9BQU87UUFDekNQLGFBQWFPLE9BQU8sR0FBR3VCLHNCQUFzQixJQUFNRixVQUFVWTtJQUMvRDtJQUVBLE1BQU1DLGVBQWUsSUFBTUYsZ0JBQWdCO0lBQzNDLE1BQU1HLGVBQWUsSUFBTUgsZ0JBQWdCO0lBQzNDLE1BQU1JLFVBQVUsQ0FBQ0M7UUFDZixJQUFJQSxFQUFFQyxhQUFhLENBQUNDLFFBQVEsQ0FBQ0YsRUFBRUcsYUFBYSxHQUFHO1FBQy9DUixnQkFBZ0I7SUFDbEI7SUFDQSxNQUFNUyxTQUFTLENBQUNKO1FBQ2QsSUFBSUEsRUFBRUMsYUFBYSxDQUFDQyxRQUFRLENBQUNGLEVBQUVHLGFBQWEsR0FBRztRQUMvQ1IsZ0JBQWdCO0lBQ2xCO0lBRUFqRyxnREFBU0EsQ0FBQztRQUNSdUU7UUFDQSxNQUFNb0MsV0FBVyxJQUFJQyxlQUFlO1lBQ2xDckM7UUFDRjtRQUNBLElBQUloQixhQUFhVSxPQUFPLEVBQUU7WUFDeEIwQyxTQUFTRSxPQUFPLENBQUN0RCxhQUFhVSxPQUFPO1FBQ3ZDO1FBQ0EsT0FBTztZQUNMMEMsU0FBU0csVUFBVTtZQUNuQmQscUJBQXFCdEMsYUFBYU8sT0FBTztRQUMzQztJQUNBLHVEQUF1RDtJQUN6RCxHQUFHO1FBQUNFO1FBQVVDO1FBQVlDO1FBQWFDO0tBQWE7SUFFcEQscUJBQ0UsOERBQUN5QztRQUNDQyxLQUFLekQ7UUFDTEYsV0FBVyxpS0FBMkssT0FBVkE7UUFDNUs4QyxjQUFjQTtRQUNkQyxjQUFjQTtRQUNkQyxTQUFTL0IsZUFBZTJDLFlBQVlaO1FBQ3BDSyxRQUFRcEMsZUFBZTJDLFlBQVlQO1FBQ25DUSxVQUFVNUMsZUFBZSxDQUFDLElBQUk7OzBCQUU5Qiw4REFBQ3RDO2dCQUNDcUIsV0FBVTtnQkFDVjJELEtBQUt4RDs7Ozs7O1lBRU5GOzs7Ozs7O0FBR1A7R0E3SXdCSDtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL3VpL3BpeGVsLWNhcmQuanM/Y2YxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiB9IGZyb20gXCJyZWFjdFwiO1xuXG5jbGFzcyBQaXhlbCB7XG4gIGNvbnN0cnVjdG9yKGNhbnZhcywgY29udGV4dCwgeCwgeSwgY29sb3IsIHNwZWVkLCBkZWxheSkge1xuICAgIHRoaXMud2lkdGggPSBjYW52YXMud2lkdGg7XG4gICAgdGhpcy5oZWlnaHQgPSBjYW52YXMuaGVpZ2h0O1xuICAgIHRoaXMuY3R4ID0gY29udGV4dDtcbiAgICB0aGlzLnggPSB4O1xuICAgIHRoaXMueSA9IHk7XG4gICAgdGhpcy5jb2xvciA9IGNvbG9yO1xuICAgIHRoaXMuc3BlZWQgPSB0aGlzLmdldFJhbmRvbVZhbHVlKDAuMSwgMC45KSAqIHNwZWVkO1xuICAgIHRoaXMuc2l6ZSA9IDA7XG4gICAgdGhpcy5zaXplU3RlcCA9IE1hdGgucmFuZG9tKCkgKiAwLjQ7XG4gICAgdGhpcy5taW5TaXplID0gMC41O1xuICAgIHRoaXMubWF4U2l6ZUludGVnZXIgPSAyO1xuICAgIHRoaXMubWF4U2l6ZSA9IHRoaXMuZ2V0UmFuZG9tVmFsdWUodGhpcy5taW5TaXplLCB0aGlzLm1heFNpemVJbnRlZ2VyKTtcbiAgICB0aGlzLmRlbGF5ID0gZGVsYXk7XG4gICAgdGhpcy5jb3VudGVyID0gMDtcbiAgICB0aGlzLmNvdW50ZXJTdGVwID0gTWF0aC5yYW5kb20oKSAqIDQgKyAodGhpcy53aWR0aCArIHRoaXMuaGVpZ2h0KSAqIDAuMDE7XG4gICAgdGhpcy5pc0lkbGUgPSBmYWxzZTtcbiAgICB0aGlzLmlzUmV2ZXJzZSA9IGZhbHNlO1xuICAgIHRoaXMuaXNTaGltbWVyID0gZmFsc2U7XG4gIH1cblxuICBnZXRSYW5kb21WYWx1ZShtaW4sIG1heCkge1xuICAgIHJldHVybiBNYXRoLnJhbmRvbSgpICogKG1heCAtIG1pbikgKyBtaW47XG4gIH1cblxuICBkcmF3KCkge1xuICAgIGNvbnN0IGNlbnRlck9mZnNldCA9IHRoaXMubWF4U2l6ZUludGVnZXIgKiAwLjUgLSB0aGlzLnNpemUgKiAwLjU7XG4gICAgdGhpcy5jdHguZmlsbFN0eWxlID0gdGhpcy5jb2xvcjtcbiAgICB0aGlzLmN0eC5maWxsUmVjdChcbiAgICAgIHRoaXMueCArIGNlbnRlck9mZnNldCxcbiAgICAgIHRoaXMueSArIGNlbnRlck9mZnNldCxcbiAgICAgIHRoaXMuc2l6ZSxcbiAgICAgIHRoaXMuc2l6ZVxuICAgICk7XG4gIH1cblxuICBhcHBlYXIoKSB7XG4gICAgdGhpcy5pc0lkbGUgPSBmYWxzZTtcbiAgICBpZiAodGhpcy5jb3VudGVyIDw9IHRoaXMuZGVsYXkpIHtcbiAgICAgIHRoaXMuY291bnRlciArPSB0aGlzLmNvdW50ZXJTdGVwO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBpZiAodGhpcy5zaXplID49IHRoaXMubWF4U2l6ZSkge1xuICAgICAgdGhpcy5pc1NoaW1tZXIgPSB0cnVlO1xuICAgIH1cbiAgICBpZiAodGhpcy5pc1NoaW1tZXIpIHtcbiAgICAgIHRoaXMuc2hpbW1lcigpO1xuICAgIH0gZWxzZSB7XG4gICAgICB0aGlzLnNpemUgKz0gdGhpcy5zaXplU3RlcDtcbiAgICB9XG4gICAgdGhpcy5kcmF3KCk7XG4gIH1cblxuICBkaXNhcHBlYXIoKSB7XG4gICAgdGhpcy5pc1NoaW1tZXIgPSBmYWxzZTtcbiAgICB0aGlzLmNvdW50ZXIgPSAwO1xuICAgIGlmICh0aGlzLnNpemUgPD0gMCkge1xuICAgICAgdGhpcy5pc0lkbGUgPSB0cnVlO1xuICAgICAgcmV0dXJuO1xuICAgIH0gZWxzZSB7XG4gICAgICB0aGlzLnNpemUgLT0gMC4xO1xuICAgIH1cbiAgICB0aGlzLmRyYXcoKTtcbiAgfVxuXG4gIHNoaW1tZXIoKSB7XG4gICAgaWYgKHRoaXMuc2l6ZSA+PSB0aGlzLm1heFNpemUpIHtcbiAgICAgIHRoaXMuaXNSZXZlcnNlID0gdHJ1ZTtcbiAgICB9IGVsc2UgaWYgKHRoaXMuc2l6ZSA8PSB0aGlzLm1pblNpemUpIHtcbiAgICAgIHRoaXMuaXNSZXZlcnNlID0gZmFsc2U7XG4gICAgfVxuICAgIGlmICh0aGlzLmlzUmV2ZXJzZSkge1xuICAgICAgdGhpcy5zaXplIC09IHRoaXMuc3BlZWQ7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRoaXMuc2l6ZSArPSB0aGlzLnNwZWVkO1xuICAgIH1cbiAgfVxufVxuXG5mdW5jdGlvbiBnZXRFZmZlY3RpdmVTcGVlZCh2YWx1ZSwgcmVkdWNlZE1vdGlvbikge1xuICBjb25zdCBtaW4gPSAwO1xuICBjb25zdCBtYXggPSAxMDA7XG4gIGNvbnN0IHRocm90dGxlID0gMC4wMDE7XG4gIGNvbnN0IHBhcnNlZCA9IHBhcnNlSW50KHZhbHVlLCAxMCk7XG5cbiAgaWYgKHBhcnNlZCA8PSBtaW4gfHwgcmVkdWNlZE1vdGlvbikge1xuICAgIHJldHVybiBtaW47XG4gIH0gZWxzZSBpZiAocGFyc2VkID49IG1heCkge1xuICAgIHJldHVybiBtYXggKiB0aHJvdHRsZTtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gcGFyc2VkICogdGhyb3R0bGU7XG4gIH1cbn1cblxuLyoqXG4gKiBWYXJpYW50cyBhZGFwdGVkIGZvciB0aGUgcHJpY2luZyB0aGVtZVxuICovXG5jb25zdCBWQVJJQU5UUyA9IHtcbiAgZGVmYXVsdDoge1xuICAgIGFjdGl2ZUNvbG9yOiBudWxsLFxuICAgIGdhcDogNSxcbiAgICBzcGVlZDogMzUsXG4gICAgY29sb3JzOiBcIiNmOGZhZmMsI2YxZjVmOSwjY2JkNWUxXCIsXG4gICAgbm9Gb2N1czogZmFsc2VcbiAgfSxcbiAgcHVycGxlOiB7XG4gICAgYWN0aXZlQ29sb3I6IFwiI2U5ZDVmZlwiLFxuICAgIGdhcDogNixcbiAgICBzcGVlZDogNDAsXG4gICAgY29sb3JzOiBcIiNlOWQ1ZmYsI2M0YjVmZCwjOGI1Y2Y2XCIsXG4gICAgbm9Gb2N1czogZmFsc2VcbiAgfSxcbiAgYmx1ZToge1xuICAgIGFjdGl2ZUNvbG9yOiBcIiNkYmVhZmVcIixcbiAgICBnYXA6IDUsXG4gICAgc3BlZWQ6IDMwLFxuICAgIGNvbG9yczogXCIjZGJlYWZlLCM5M2M1ZmQsIzNiODJmNlwiLFxuICAgIG5vRm9jdXM6IGZhbHNlXG4gIH0sXG4gIGhpZ2hsaWdodGVkOiB7XG4gICAgYWN0aXZlQ29sb3I6IFwiI2U5ZDVmZlwiLFxuICAgIGdhcDogNCxcbiAgICBzcGVlZDogNjAsXG4gICAgY29sb3JzOiBcIiNlOWQ1ZmYsI2M0YjVmZCwjOGI1Y2Y2LCNkYmVhZmUsIzkzYzVmZFwiLFxuICAgIG5vRm9jdXM6IGZhbHNlXG4gIH1cbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBpeGVsQ2FyZCh7XG4gIHZhcmlhbnQgPSBcImRlZmF1bHRcIixcbiAgZ2FwLFxuICBzcGVlZCxcbiAgY29sb3JzLFxuICBub0ZvY3VzLFxuICBjbGFzc05hbWUgPSBcIlwiLFxuICBjaGlsZHJlblxufSkge1xuICBjb25zdCBjb250YWluZXJSZWYgPSB1c2VSZWYobnVsbCk7XG4gIGNvbnN0IGNhbnZhc1JlZiA9IHVzZVJlZihudWxsKTtcbiAgY29uc3QgcGl4ZWxzUmVmID0gdXNlUmVmKFtdKTtcbiAgY29uc3QgYW5pbWF0aW9uUmVmID0gdXNlUmVmKG51bGwpO1xuICBjb25zdCB0aW1lUHJldmlvdXNSZWYgPSB1c2VSZWYocGVyZm9ybWFuY2Uubm93KCkpO1xuICBjb25zdCByZWR1Y2VkTW90aW9uID0gdXNlUmVmKFxuICAgIHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID9cbiAgICAgIHdpbmRvdy5tYXRjaE1lZGlhKFwiKHByZWZlcnMtcmVkdWNlZC1tb3Rpb246IHJlZHVjZSlcIikubWF0Y2hlcyA6IGZhbHNlXG4gICkuY3VycmVudDtcblxuICBjb25zdCB2YXJpYW50Q2ZnID0gVkFSSUFOVFNbdmFyaWFudF0gfHwgVkFSSUFOVFMuZGVmYXVsdDtcbiAgY29uc3QgZmluYWxHYXAgPSBnYXAgPz8gdmFyaWFudENmZy5nYXA7XG4gIGNvbnN0IGZpbmFsU3BlZWQgPSBzcGVlZCA/PyB2YXJpYW50Q2ZnLnNwZWVkO1xuICBjb25zdCBmaW5hbENvbG9ycyA9IGNvbG9ycyA/PyB2YXJpYW50Q2ZnLmNvbG9ycztcbiAgY29uc3QgZmluYWxOb0ZvY3VzID0gbm9Gb2N1cyA/PyB2YXJpYW50Q2ZnLm5vRm9jdXM7XG5cbiAgY29uc3QgaW5pdFBpeGVscyA9ICgpID0+IHtcbiAgICBpZiAoIWNvbnRhaW5lclJlZi5jdXJyZW50IHx8ICFjYW52YXNSZWYuY3VycmVudCkgcmV0dXJuO1xuXG4gICAgY29uc3QgcmVjdCA9IGNvbnRhaW5lclJlZi5jdXJyZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xuICAgIGNvbnN0IHdpZHRoID0gTWF0aC5mbG9vcihyZWN0LndpZHRoKTtcbiAgICBjb25zdCBoZWlnaHQgPSBNYXRoLmZsb29yKHJlY3QuaGVpZ2h0KTtcbiAgICBjb25zdCBjdHggPSBjYW52YXNSZWYuY3VycmVudC5nZXRDb250ZXh0KFwiMmRcIik7XG5cbiAgICBjYW52YXNSZWYuY3VycmVudC53aWR0aCA9IHdpZHRoO1xuICAgIGNhbnZhc1JlZi5jdXJyZW50LmhlaWdodCA9IGhlaWdodDtcbiAgICBjYW52YXNSZWYuY3VycmVudC5zdHlsZS53aWR0aCA9IGAke3dpZHRofXB4YDtcbiAgICBjYW52YXNSZWYuY3VycmVudC5zdHlsZS5oZWlnaHQgPSBgJHtoZWlnaHR9cHhgO1xuXG4gICAgY29uc3QgY29sb3JzQXJyYXkgPSBmaW5hbENvbG9ycy5zcGxpdChcIixcIik7XG4gICAgY29uc3QgcHhzID0gW107XG4gICAgZm9yIChsZXQgeCA9IDA7IHggPCB3aWR0aDsgeCArPSBwYXJzZUludChmaW5hbEdhcCwgMTApKSB7XG4gICAgICBmb3IgKGxldCB5ID0gMDsgeSA8IGhlaWdodDsgeSArPSBwYXJzZUludChmaW5hbEdhcCwgMTApKSB7XG4gICAgICAgIGNvbnN0IGNvbG9yID1cbiAgICAgICAgICBjb2xvcnNBcnJheVtNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBjb2xvcnNBcnJheS5sZW5ndGgpXTtcblxuICAgICAgICBjb25zdCBkeCA9IHggLSB3aWR0aCAvIDI7XG4gICAgICAgIGNvbnN0IGR5ID0geSAtIGhlaWdodCAvIDI7XG4gICAgICAgIGNvbnN0IGRpc3RhbmNlID0gTWF0aC5zcXJ0KGR4ICogZHggKyBkeSAqIGR5KTtcbiAgICAgICAgY29uc3QgZGVsYXkgPSByZWR1Y2VkTW90aW9uID8gMCA6IGRpc3RhbmNlO1xuXG4gICAgICAgIHB4cy5wdXNoKFxuICAgICAgICAgIG5ldyBQaXhlbChcbiAgICAgICAgICAgIGNhbnZhc1JlZi5jdXJyZW50LFxuICAgICAgICAgICAgY3R4LFxuICAgICAgICAgICAgeCxcbiAgICAgICAgICAgIHksXG4gICAgICAgICAgICBjb2xvcixcbiAgICAgICAgICAgIGdldEVmZmVjdGl2ZVNwZWVkKGZpbmFsU3BlZWQsIHJlZHVjZWRNb3Rpb24pLFxuICAgICAgICAgICAgZGVsYXlcbiAgICAgICAgICApXG4gICAgICAgICk7XG4gICAgICB9XG4gICAgfVxuICAgIHBpeGVsc1JlZi5jdXJyZW50ID0gcHhzO1xuICB9O1xuXG4gIGNvbnN0IGRvQW5pbWF0ZSA9IChmbk5hbWUpID0+IHtcbiAgICBhbmltYXRpb25SZWYuY3VycmVudCA9IHJlcXVlc3RBbmltYXRpb25GcmFtZSgoKSA9PiBkb0FuaW1hdGUoZm5OYW1lKSk7XG4gICAgY29uc3QgdGltZU5vdyA9IHBlcmZvcm1hbmNlLm5vdygpO1xuICAgIGNvbnN0IHRpbWVQYXNzZWQgPSB0aW1lTm93IC0gdGltZVByZXZpb3VzUmVmLmN1cnJlbnQ7XG4gICAgY29uc3QgdGltZUludGVydmFsID0gMTAwMCAvIDYwOyAvLyB+NjAgRlBTXG5cbiAgICBpZiAodGltZVBhc3NlZCA8IHRpbWVJbnRlcnZhbCkgcmV0dXJuO1xuICAgIHRpbWVQcmV2aW91c1JlZi5jdXJyZW50ID0gdGltZU5vdyAtICh0aW1lUGFzc2VkICUgdGltZUludGVydmFsKTtcblxuICAgIGNvbnN0IGN0eCA9IGNhbnZhc1JlZi5jdXJyZW50Py5nZXRDb250ZXh0KFwiMmRcIik7XG4gICAgaWYgKCFjdHggfHwgIWNhbnZhc1JlZi5jdXJyZW50KSByZXR1cm47XG5cbiAgICBjdHguY2xlYXJSZWN0KDAsIDAsIGNhbnZhc1JlZi5jdXJyZW50LndpZHRoLCBjYW52YXNSZWYuY3VycmVudC5oZWlnaHQpO1xuXG4gICAgbGV0IGFsbElkbGUgPSB0cnVlO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcGl4ZWxzUmVmLmN1cnJlbnQubGVuZ3RoOyBpKyspIHtcbiAgICAgIGNvbnN0IHBpeGVsID0gcGl4ZWxzUmVmLmN1cnJlbnRbaV07XG4gICAgICBwaXhlbFtmbk5hbWVdKCk7XG4gICAgICBpZiAoIXBpeGVsLmlzSWRsZSkge1xuICAgICAgICBhbGxJZGxlID0gZmFsc2U7XG4gICAgICB9XG4gICAgfVxuICAgIGlmIChhbGxJZGxlKSB7XG4gICAgICBjYW5jZWxBbmltYXRpb25GcmFtZShhbmltYXRpb25SZWYuY3VycmVudCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUFuaW1hdGlvbiA9IChuYW1lKSA9PiB7XG4gICAgY2FuY2VsQW5pbWF0aW9uRnJhbWUoYW5pbWF0aW9uUmVmLmN1cnJlbnQpO1xuICAgIGFuaW1hdGlvblJlZi5jdXJyZW50ID0gcmVxdWVzdEFuaW1hdGlvbkZyYW1lKCgpID0+IGRvQW5pbWF0ZShuYW1lKSk7XG4gIH07XG5cbiAgY29uc3Qgb25Nb3VzZUVudGVyID0gKCkgPT4gaGFuZGxlQW5pbWF0aW9uKFwiYXBwZWFyXCIpO1xuICBjb25zdCBvbk1vdXNlTGVhdmUgPSAoKSA9PiBoYW5kbGVBbmltYXRpb24oXCJkaXNhcHBlYXJcIik7XG4gIGNvbnN0IG9uRm9jdXMgPSAoZSkgPT4ge1xuICAgIGlmIChlLmN1cnJlbnRUYXJnZXQuY29udGFpbnMoZS5yZWxhdGVkVGFyZ2V0KSkgcmV0dXJuO1xuICAgIGhhbmRsZUFuaW1hdGlvbihcImFwcGVhclwiKTtcbiAgfTtcbiAgY29uc3Qgb25CbHVyID0gKGUpID0+IHtcbiAgICBpZiAoZS5jdXJyZW50VGFyZ2V0LmNvbnRhaW5zKGUucmVsYXRlZFRhcmdldCkpIHJldHVybjtcbiAgICBoYW5kbGVBbmltYXRpb24oXCJkaXNhcHBlYXJcIik7XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpbml0UGl4ZWxzKCk7XG4gICAgY29uc3Qgb2JzZXJ2ZXIgPSBuZXcgUmVzaXplT2JzZXJ2ZXIoKCkgPT4ge1xuICAgICAgaW5pdFBpeGVscygpO1xuICAgIH0pO1xuICAgIGlmIChjb250YWluZXJSZWYuY3VycmVudCkge1xuICAgICAgb2JzZXJ2ZXIub2JzZXJ2ZShjb250YWluZXJSZWYuY3VycmVudCk7XG4gICAgfVxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBvYnNlcnZlci5kaXNjb25uZWN0KCk7XG4gICAgICBjYW5jZWxBbmltYXRpb25GcmFtZShhbmltYXRpb25SZWYuY3VycmVudCk7XG4gICAgfTtcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXG4gIH0sIFtmaW5hbEdhcCwgZmluYWxTcGVlZCwgZmluYWxDb2xvcnMsIGZpbmFsTm9Gb2N1c10pO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgcmVmPXtjb250YWluZXJSZWZ9XG4gICAgICBjbGFzc05hbWU9e2ByZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC14bCBib3JkZXIgYm9yZGVyLXdoaXRlLzEwIGlzb2xhdGUgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwIGVhc2UtW2N1YmljLWJlemllcigwLjUsMSwwLjg5LDEpXSBzZWxlY3Qtbm9uZSBtaW4taC1bNDAwcHhdICR7Y2xhc3NOYW1lfWB9XG4gICAgICBvbk1vdXNlRW50ZXI9e29uTW91c2VFbnRlcn1cbiAgICAgIG9uTW91c2VMZWF2ZT17b25Nb3VzZUxlYXZlfVxuICAgICAgb25Gb2N1cz17ZmluYWxOb0ZvY3VzID8gdW5kZWZpbmVkIDogb25Gb2N1c31cbiAgICAgIG9uQmx1cj17ZmluYWxOb0ZvY3VzID8gdW5kZWZpbmVkIDogb25CbHVyfVxuICAgICAgdGFiSW5kZXg9e2ZpbmFsTm9Gb2N1cyA/IC0xIDogMH1cbiAgICA+XG4gICAgICA8Y2FudmFzXG4gICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgdy1mdWxsIGgtZnVsbCBibG9jayBwb2ludGVyLWV2ZW50cy1ub25lXCJcbiAgICAgICAgcmVmPXtjYW52YXNSZWZ9XG4gICAgICAvPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVJlZiIsIlBpeGVsIiwiZ2V0UmFuZG9tVmFsdWUiLCJtaW4iLCJtYXgiLCJNYXRoIiwicmFuZG9tIiwiZHJhdyIsImNlbnRlck9mZnNldCIsIm1heFNpemVJbnRlZ2VyIiwic2l6ZSIsImN0eCIsImZpbGxTdHlsZSIsImNvbG9yIiwiZmlsbFJlY3QiLCJ4IiwieSIsImFwcGVhciIsImlzSWRsZSIsImNvdW50ZXIiLCJkZWxheSIsImNvdW50ZXJTdGVwIiwibWF4U2l6ZSIsImlzU2hpbW1lciIsInNoaW1tZXIiLCJzaXplU3RlcCIsImRpc2FwcGVhciIsImlzUmV2ZXJzZSIsIm1pblNpemUiLCJzcGVlZCIsImNvbnN0cnVjdG9yIiwiY2FudmFzIiwiY29udGV4dCIsIndpZHRoIiwiaGVpZ2h0IiwiZ2V0RWZmZWN0aXZlU3BlZWQiLCJ2YWx1ZSIsInJlZHVjZWRNb3Rpb24iLCJ0aHJvdHRsZSIsInBhcnNlZCIsInBhcnNlSW50IiwiVkFSSUFOVFMiLCJkZWZhdWx0IiwiYWN0aXZlQ29sb3IiLCJnYXAiLCJjb2xvcnMiLCJub0ZvY3VzIiwicHVycGxlIiwiYmx1ZSIsImhpZ2hsaWdodGVkIiwiUGl4ZWxDYXJkIiwidmFyaWFudCIsImNsYXNzTmFtZSIsImNoaWxkcmVuIiwiY29udGFpbmVyUmVmIiwiY2FudmFzUmVmIiwicGl4ZWxzUmVmIiwiYW5pbWF0aW9uUmVmIiwidGltZVByZXZpb3VzUmVmIiwicGVyZm9ybWFuY2UiLCJub3ciLCJ3aW5kb3ciLCJtYXRjaE1lZGlhIiwibWF0Y2hlcyIsImN1cnJlbnQiLCJ2YXJpYW50Q2ZnIiwiZmluYWxHYXAiLCJmaW5hbFNwZWVkIiwiZmluYWxDb2xvcnMiLCJmaW5hbE5vRm9jdXMiLCJpbml0UGl4ZWxzIiwicmVjdCIsImdldEJvdW5kaW5nQ2xpZW50UmVjdCIsImZsb29yIiwiZ2V0Q29udGV4dCIsInN0eWxlIiwiY29sb3JzQXJyYXkiLCJzcGxpdCIsInB4cyIsImxlbmd0aCIsImR4IiwiZHkiLCJkaXN0YW5jZSIsInNxcnQiLCJwdXNoIiwiZG9BbmltYXRlIiwiZm5OYW1lIiwicmVxdWVzdEFuaW1hdGlvbkZyYW1lIiwidGltZU5vdyIsInRpbWVQYXNzZWQiLCJ0aW1lSW50ZXJ2YWwiLCJjbGVhclJlY3QiLCJhbGxJZGxlIiwiaSIsInBpeGVsIiwiY2FuY2VsQW5pbWF0aW9uRnJhbWUiLCJoYW5kbGVBbmltYXRpb24iLCJuYW1lIiwib25Nb3VzZUVudGVyIiwib25Nb3VzZUxlYXZlIiwib25Gb2N1cyIsImUiLCJjdXJyZW50VGFyZ2V0IiwiY29udGFpbnMiLCJyZWxhdGVkVGFyZ2V0Iiwib25CbHVyIiwib2JzZXJ2ZXIiLCJSZXNpemVPYnNlcnZlciIsIm9ic2VydmUiLCJkaXNjb25uZWN0IiwiZGl2IiwicmVmIiwidW5kZWZpbmVkIiwidGFiSW5kZXgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/ui/pixel-card.js\n"));

/***/ })

});