"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/pricing",{

/***/ "./components/pricing-section.js":
/*!***************************************!*\
  !*** ./components/pricing-section.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PricingCard: function() { return /* binding */ PricingCard; },\n/* harmony export */   PricingFAQ: function() { return /* binding */ PricingFAQ; },\n/* harmony export */   PricingSection: function() { return /* binding */ PricingSection; },\n/* harmony export */   defaultFAQs: function() { return /* binding */ defaultFAQs; },\n/* harmony export */   defaultPricingPlans: function() { return /* binding */ defaultPricingPlans; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _animations_animate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./animations/animate */ \"./components/animations/animate.js\");\n/* harmony import */ var _ui_enhanced_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/enhanced-button */ \"./components/ui/enhanced-button.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"./node_modules/framer-motion/dist/es/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,X!=!lucide-react */ \"__barrel_optimize__?names=Check,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ PricingCard,defaultPricingPlans,PricingFAQ,defaultFAQs,PricingSection auto */ \n\n\n\n\n\n// Pricing card component\nconst PricingCard = (param)=>{\n    let { title, price, description, features, buttonText, buttonLink, highlighted = false, delay = 0 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animations_animate__WEBPACK_IMPORTED_MODULE_1__.Animate, {\n        type: \"slide\",\n        delay: delay,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            className: \"rounded-xl p-5 md:p-6 flex flex-col h-full relative \".concat(highlighted ? \"subtle-glass border-2 border-theme-purple/50 shadow-2xl shadow-theme-purple/20 transform scale-105 z-10 bg-gradient-to-br from-theme-purple/5 to-theme-blue/5\" : \"subtle-glass border border-white/10\"),\n            whileHover: {\n                y: highlighted ? -8 : -5,\n                scale: highlighted ? 1.08 : 1.02,\n                transition: {\n                    duration: 0.2\n                }\n            },\n            children: [\n                highlighted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute -top-3 left-1/2 transform -translate-x-1/2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-theme-purple to-theme-blue text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                        children: \"MOST POPULAR\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 36,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg md:text-xl font-bold mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground mb-3\",\n                            children: description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-2\",\n                            children: price\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-grow\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-2 my-4 text-sm\",\n                        children: features.slice(0, 4).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"flex items-start\",\n                                children: [\n                                    feature.included ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Check, {\n                                        className: \"h-4 w-4 text-theme-blue shrink-0 mr-2 mt-0.5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                        lineNumber: 54,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.X, {\n                                        className: \"h-4 w-4 text-muted-foreground shrink-0 mr-2 mt-0.5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                        lineNumber: 56,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: feature.included ? \"\" : \"text-muted-foreground\",\n                                        children: feature.text\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                        lineNumber: 58,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                lineNumber: 52,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: buttonLink,\n                    className: \"mt-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_enhanced_button__WEBPACK_IMPORTED_MODULE_2__.EnhancedButton, {\n                        size: \"md\",\n                        color: highlighted ? \"purple\" : \"blue\",\n                        className: \"w-full \".concat(highlighted ? \"bg-theme-purple text-white hover:bg-theme-purple/90\" : \"bg-transparent border text-foreground dark:border-white/20 dark:hover:border-white/40 border-black/50 hover:border-black/70\"),\n                        children: buttonText\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PricingCard;\n// Pricing plans data\nconst defaultPricingPlans = [\n    {\n        title: \"Free Plan\",\n        price: \"Free\",\n        description: \"Perfect for trying out our service.\",\n        features: [\n            {\n                text: \"3 free voiceovers\",\n                included: true\n            },\n            {\n                text: \"Basic voice options\",\n                included: true\n            },\n            {\n                text: \"PDF & script upload\",\n                included: true\n            },\n            {\n                text: \"MP4 video generation\",\n                included: true\n            },\n            {\n                text: \"Voice cloning\",\n                included: false\n            },\n            {\n                text: \"Priority support\",\n                included: false\n            }\n        ],\n        buttonText: \"Get Started\",\n        buttonLink: \"/dashboard\",\n        highlighted: false,\n        delay: 0.1\n    },\n    {\n        title: \"Starter Plan\",\n        price: \"$9.99\",\n        description: \"Most popular for regular users.\",\n        features: [\n            {\n                text: \"Unlimited voiceovers\",\n                included: true\n            },\n            {\n                text: \"All voice options\",\n                included: true\n            },\n            {\n                text: \"PDF & script upload\",\n                included: true\n            },\n            {\n                text: \"MP4 video generation\",\n                included: true\n            },\n            {\n                text: \"1 voice clone\",\n                included: true\n            },\n            {\n                text: \"Email support\",\n                included: true\n            }\n        ],\n        buttonText: \"Choose Starter\",\n        buttonLink: \"/\",\n        highlighted: true,\n        delay: 0.2\n    },\n    {\n        title: \"Premium Plan\",\n        price: \"$19.99\",\n        description: \"Best for power users and teams.\",\n        features: [\n            {\n                text: \"Unlimited voiceovers\",\n                included: true\n            },\n            {\n                text: \"All voice options\",\n                included: true\n            },\n            {\n                text: \"PDF & script upload\",\n                included: true\n            },\n            {\n                text: \"MP4 video generation\",\n                included: true\n            },\n            {\n                text: \"5 voice clones\",\n                included: true\n            },\n            {\n                text: \"Priority support\",\n                included: true\n            }\n        ],\n        buttonText: \"Choose Premium\",\n        buttonLink: \"/\",\n        highlighted: false,\n        delay: 0.3\n    }\n];\n// FAQ component\nconst PricingFAQ = (param)=>{\n    let { faqs } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-24 max-w-3xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animations_animate__WEBPACK_IMPORTED_MODULE_1__.Animate, {\n                type: \"fade\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl md:text-3xl font-bold mb-8 text-center\",\n                    children: \"Frequently Asked Questions\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 143,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: faqs.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_animations_animate__WEBPACK_IMPORTED_MODULE_1__.Animate, {\n                        type: \"slide\",\n                        delay: 0.1 * (index + 1),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"subtle-glass rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-2\",\n                                    children: faq.question\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: faq.answer\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, undefined)\n                    }, index, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = PricingFAQ;\n// Default FAQ data\nconst defaultFAQs = [\n    {\n        question: \"How do I choose the right plan?\",\n        answer: \"Choose a plan based on your usage needs. If you're just getting started, the Free plan gives you 3 voiceovers to try our service. For regular use, the Starter plan offers unlimited voiceovers and 1 voice clone. For power users who need multiple voice clones, the Premium plan provides 5 voice clones and priority support.\"\n    },\n    {\n        question: \"Can I change my plan at any time?\",\n        answer: \"Yes, you can upgrade or downgrade your plan at any time. Upgrades take effect immediately, while downgrades will take effect at the end of your current billing cycle.\"\n    },\n    {\n        question: \"What is voice cloning and how does it work?\",\n        answer: \"Voice cloning allows you to create custom AI voices based on audio samples. Free users cannot access this feature. Starter plan users can create 1 custom voice, while Premium users can create up to 5 custom voices for their presentations.\"\n    }\n];\n// Main pricing section component\nfunction PricingSection(param) {\n    let { pricingPlans = defaultPricingPlans, faqs = defaultFAQs, showFAQ = true } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-12 md:py-16\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-5xl mx-auto items-center\",\n                children: pricingPlans.map((plan, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(plan.highlighted ? \"md:mt-0\" : \"md:mt-6\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PricingCard, {\n                            ...plan\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            showFAQ && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PricingFAQ, {\n                faqs: faqs\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n                lineNumber: 193,\n                columnNumber: 19\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\pricing-section.js\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, this);\n}\n_c2 = PricingSection;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PricingCard\");\n$RefreshReg$(_c1, \"PricingFAQ\");\n$RefreshReg$(_c2, \"PricingSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/pricing-section.js\n"));

/***/ })

});