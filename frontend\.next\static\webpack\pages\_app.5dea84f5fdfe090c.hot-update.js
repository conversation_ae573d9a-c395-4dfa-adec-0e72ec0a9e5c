"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\r\\n  :root {\\r\\n    --background: 0 0% 100%;\\r\\n    --foreground: 240 10% 3.9%;\\r\\n    --card: 0 0% 100%;\\r\\n    --card-foreground: 240 10% 3.9%;\\r\\n    --popover: 0 0% 100%;\\r\\n    --popover-foreground: 240 10% 3.9%;\\r\\n    --primary: 240 5.9% 10%;\\r\\n    --primary-foreground: 0 0% 98%;\\r\\n    --secondary: 240 4.8% 95.9%;\\r\\n    --secondary-foreground: 240 5.9% 10%;\\r\\n    --muted: 240 4.8% 95.9%;\\r\\n    --muted-foreground: 240 3.8% 46.1%;\\r\\n    --accent: 240 4.8% 95.9%;\\r\\n    --accent-foreground: 240 5.9% 10%;\\r\\n    --destructive: 0 84.2% 60.2%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 5.9% 90%;\\r\\n    --input: 240 5.9% 90%;\\r\\n    --ring: 240 5.9% 10%;\\r\\n    --radius: 0.5rem;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 50%;\\r\\n    --theme-purple: 260 100% 60%;\\r\\n    --theme-teal: 180 100% 40%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 50%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.5);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.5);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.5);\\r\\n\\r\\n    /* Theme transition */\\r\\n    --theme-transition: 0.3s ease;\\r\\n  }\\r\\n\\r\\n  .dark {\\r\\n    --background: 240 10% 3.9%;\\r\\n    --foreground: 0 0% 98%;\\r\\n    --card: 240 10% 3.9%;\\r\\n    --card-foreground: 0 0% 98%;\\r\\n    --popover: 240 10% 3.9%;\\r\\n    --popover-foreground: 0 0% 98%;\\r\\n    --primary: 0 0% 98%;\\r\\n    --primary-foreground: 240 5.9% 10%;\\r\\n    --secondary: 240 3.7% 15.9%;\\r\\n    --secondary-foreground: 0 0% 98%;\\r\\n    --muted: 240 3.7% 15.9%;\\r\\n    --muted-foreground: 240 5% 64.9%;\\r\\n    --accent: 240 3.7% 15.9%;\\r\\n    --accent-foreground: 0 0% 98%;\\r\\n    --destructive: 0 62.8% 30.6%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 3.7% 15.9%;\\r\\n    --input: 240 3.7% 15.9%;\\r\\n    --ring: 240 4.9% 83.9%;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 60%;\\r\\n    --theme-purple: 260 100% 70%;\\r\\n    --theme-teal: 180 100% 50%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 60%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.7);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.7);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.7);\\r\\n  }\\r\\n  * {\\n  border-color: hsl(var(--border));\\n}\\r\\n  body {\\n  background-color: hsl(var(--background));\\n  color: hsl(var(--foreground));\\r\\n    font-feature-settings: \\\"rlig\\\" 1, \\\"calt\\\" 1;\\r\\n    transition: background-color var(--theme-transition), color var(--theme-transition);\\n}\\r\\n.container {\\n  width: 100%;\\n  margin-right: auto;\\n  margin-left: auto;\\n  padding-right: 2rem;\\n  padding-left: 2rem;\\n}\\r\\n@media (min-width: 1400px) {\\n\\n  .container {\\n    max-width: 1400px;\\n  }\\n}\\r\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\r\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\r\\n.visible {\\n  visibility: visible;\\n}\\r\\n.fixed {\\n  position: fixed;\\n}\\r\\n.absolute {\\n  position: absolute;\\n}\\r\\n.relative {\\n  position: relative;\\n}\\r\\n.inset-0 {\\n  inset: 0px;\\n}\\r\\n.-top-1 {\\n  top: -0.25rem;\\n}\\r\\n.bottom-2 {\\n  bottom: 0.5rem;\\n}\\r\\n.bottom-20 {\\n  bottom: 5rem;\\n}\\r\\n.bottom-40 {\\n  bottom: 10rem;\\n}\\r\\n.bottom-full {\\n  bottom: 100%;\\n}\\r\\n.left-0 {\\n  left: 0px;\\n}\\r\\n.left-1\\\\/2 {\\n  left: 50%;\\n}\\r\\n.left-1\\\\/4 {\\n  left: 25%;\\n}\\r\\n.left-10 {\\n  left: 2.5rem;\\n}\\r\\n.left-2 {\\n  left: 0.5rem;\\n}\\r\\n.left-\\\\[50\\\\%\\\\] {\\n  left: 50%;\\n}\\r\\n.right-0 {\\n  right: 0px;\\n}\\r\\n.right-10 {\\n  right: 2.5rem;\\n}\\r\\n.right-2 {\\n  right: 0.5rem;\\n}\\r\\n.right-20 {\\n  right: 5rem;\\n}\\r\\n.right-4 {\\n  right: 1rem;\\n}\\r\\n.top-0 {\\n  top: 0px;\\n}\\r\\n.top-20 {\\n  top: 5rem;\\n}\\r\\n.top-4 {\\n  top: 1rem;\\n}\\r\\n.top-40 {\\n  top: 10rem;\\n}\\r\\n.top-8 {\\n  top: 2rem;\\n}\\r\\n.top-\\\\[50\\\\%\\\\] {\\n  top: 50%;\\n}\\r\\n.-top-3 {\\n  top: -0.75rem;\\n}\\r\\n.isolate {\\n  isolation: isolate;\\n}\\r\\n.-z-10 {\\n  z-index: -10;\\n}\\r\\n.z-0 {\\n  z-index: 0;\\n}\\r\\n.z-10 {\\n  z-index: 10;\\n}\\r\\n.z-40 {\\n  z-index: 40;\\n}\\r\\n.z-50 {\\n  z-index: 50;\\n}\\r\\n.col-span-1 {\\n  grid-column: span 1 / span 1;\\n}\\r\\n.-mx-1 {\\n  margin-left: -0.25rem;\\n  margin-right: -0.25rem;\\n}\\r\\n.mx-1 {\\n  margin-left: 0.25rem;\\n  margin-right: 0.25rem;\\n}\\r\\n.mx-2 {\\n  margin-left: 0.5rem;\\n  margin-right: 0.5rem;\\n}\\r\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\r\\n.my-1 {\\n  margin-top: 0.25rem;\\n  margin-bottom: 0.25rem;\\n}\\r\\n.my-4 {\\n  margin-top: 1rem;\\n  margin-bottom: 1rem;\\n}\\r\\n.my-6 {\\n  margin-top: 1.5rem;\\n  margin-bottom: 1.5rem;\\n}\\r\\n.-ml-1 {\\n  margin-left: -0.25rem;\\n}\\r\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\r\\n.mb-12 {\\n  margin-bottom: 3rem;\\n}\\r\\n.mb-16 {\\n  margin-bottom: 4rem;\\n}\\r\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\r\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\r\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\r\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\r\\n.mb-8 {\\n  margin-bottom: 2rem;\\n}\\r\\n.ml-1 {\\n  margin-left: 0.25rem;\\n}\\r\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\r\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\r\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\r\\n.mt-0 {\\n  margin-top: 0px;\\n}\\r\\n.mt-0\\\\.5 {\\n  margin-top: 0.125rem;\\n}\\r\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\r\\n.mt-12 {\\n  margin-top: 3rem;\\n}\\r\\n.mt-16 {\\n  margin-top: 4rem;\\n}\\r\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\r\\n.mt-24 {\\n  margin-top: 6rem;\\n}\\r\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\r\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\r\\n.mt-8 {\\n  margin-top: 2rem;\\n}\\r\\n.mt-\\\\[-150px\\\\] {\\n  margin-top: -150px;\\n}\\r\\n.mt-auto {\\n  margin-top: auto;\\n}\\r\\n.block {\\n  display: block;\\n}\\r\\n.inline-block {\\n  display: inline-block;\\n}\\r\\n.flex {\\n  display: flex;\\n}\\r\\n.inline-flex {\\n  display: inline-flex;\\n}\\r\\n.table {\\n  display: table;\\n}\\r\\n.grid {\\n  display: grid;\\n}\\r\\n.hidden {\\n  display: none;\\n}\\r\\n.aspect-square {\\n  aspect-ratio: 1 / 1;\\n}\\r\\n.aspect-video {\\n  aspect-ratio: 16 / 9;\\n}\\r\\n.h-0 {\\n  height: 0px;\\n}\\r\\n.h-1 {\\n  height: 0.25rem;\\n}\\r\\n.h-10 {\\n  height: 2.5rem;\\n}\\r\\n.h-11 {\\n  height: 2.75rem;\\n}\\r\\n.h-12 {\\n  height: 3rem;\\n}\\r\\n.h-16 {\\n  height: 4rem;\\n}\\r\\n.h-2 {\\n  height: 0.5rem;\\n}\\r\\n.h-2\\\\.5 {\\n  height: 0.625rem;\\n}\\r\\n.h-20 {\\n  height: 5rem;\\n}\\r\\n.h-24 {\\n  height: 6rem;\\n}\\r\\n.h-3 {\\n  height: 0.75rem;\\n}\\r\\n.h-3\\\\.5 {\\n  height: 0.875rem;\\n}\\r\\n.h-32 {\\n  height: 8rem;\\n}\\r\\n.h-4 {\\n  height: 1rem;\\n}\\r\\n.h-40 {\\n  height: 10rem;\\n}\\r\\n.h-5 {\\n  height: 1.25rem;\\n}\\r\\n.h-6 {\\n  height: 1.5rem;\\n}\\r\\n.h-7 {\\n  height: 1.75rem;\\n}\\r\\n.h-8 {\\n  height: 2rem;\\n}\\r\\n.h-9 {\\n  height: 2.25rem;\\n}\\r\\n.h-\\\\[1px\\\\] {\\n  height: 1px;\\n}\\r\\n.h-\\\\[var\\\\(--radix-select-trigger-height\\\\)\\\\] {\\n  height: var(--radix-select-trigger-height);\\n}\\r\\n.h-auto {\\n  height: auto;\\n}\\r\\n.h-full {\\n  height: 100%;\\n}\\r\\n.h-px {\\n  height: 1px;\\n}\\r\\n.min-h-\\\\[100px\\\\] {\\n  min-height: 100px;\\n}\\r\\n.min-h-\\\\[150px\\\\] {\\n  min-height: 150px;\\n}\\r\\n.min-h-\\\\[80px\\\\] {\\n  min-height: 80px;\\n}\\r\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\r\\n.w-0 {\\n  width: 0px;\\n}\\r\\n.w-10 {\\n  width: 2.5rem;\\n}\\r\\n.w-12 {\\n  width: 3rem;\\n}\\r\\n.w-16 {\\n  width: 4rem;\\n}\\r\\n.w-2 {\\n  width: 0.5rem;\\n}\\r\\n.w-2\\\\.5 {\\n  width: 0.625rem;\\n}\\r\\n.w-20 {\\n  width: 5rem;\\n}\\r\\n.w-24 {\\n  width: 6rem;\\n}\\r\\n.w-3 {\\n  width: 0.75rem;\\n}\\r\\n.w-3\\\\.5 {\\n  width: 0.875rem;\\n}\\r\\n.w-32 {\\n  width: 8rem;\\n}\\r\\n.w-4 {\\n  width: 1rem;\\n}\\r\\n.w-48 {\\n  width: 12rem;\\n}\\r\\n.w-5 {\\n  width: 1.25rem;\\n}\\r\\n.w-6 {\\n  width: 1.5rem;\\n}\\r\\n.w-8 {\\n  width: 2rem;\\n}\\r\\n.w-\\\\[1px\\\\] {\\n  width: 1px;\\n}\\r\\n.w-fit {\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\r\\n.w-full {\\n  width: 100%;\\n}\\r\\n.min-w-\\\\[8rem\\\\] {\\n  min-width: 8rem;\\n}\\r\\n.min-w-\\\\[var\\\\(--radix-select-trigger-width\\\\)\\\\] {\\n  min-width: var(--radix-select-trigger-width);\\n}\\r\\n.max-w-2xl {\\n  max-width: 42rem;\\n}\\r\\n.max-w-3xl {\\n  max-width: 48rem;\\n}\\r\\n.max-w-4xl {\\n  max-width: 56rem;\\n}\\r\\n.max-w-5xl {\\n  max-width: 64rem;\\n}\\r\\n.max-w-lg {\\n  max-width: 32rem;\\n}\\r\\n.max-w-md {\\n  max-width: 28rem;\\n}\\r\\n.max-w-xs {\\n  max-width: 20rem;\\n}\\r\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\r\\n.shrink-0 {\\n  flex-shrink: 0;\\n}\\r\\n.flex-grow {\\n  flex-grow: 1;\\n}\\r\\n.grow {\\n  flex-grow: 1;\\n}\\r\\n.border-collapse {\\n  border-collapse: collapse;\\n}\\r\\n.-translate-x-1\\\\/2 {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-x-\\\\[-50\\\\%\\\\] {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-1 {\\n  --tw-translate-y: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.translate-y-\\\\[-50\\\\%\\\\] {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.rotate-0 {\\n  --tw-rotate: 0deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.scale-105 {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n@keyframes gradient-x {\\n\\n  0%, 100% {\\n    background-position: 0% 50%;\\n  }\\n\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n}\\r\\n.animate-gradient-x {\\n  animation: gradient-x 3s ease infinite;\\n}\\r\\n@keyframes pulse {\\n\\n  50% {\\n    opacity: .5;\\n  }\\n}\\r\\n.animate-pulse {\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\r\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\r\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\r\\n.cursor-default {\\n  cursor: default;\\n}\\r\\n.cursor-help {\\n  cursor: help;\\n}\\r\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\r\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\r\\n.touch-none {\\n  touch-action: none;\\n}\\r\\n.select-none {\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n}\\r\\n.resize-none {\\n  resize: none;\\n}\\r\\n.resize {\\n  resize: both;\\n}\\r\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\r\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\r\\n.flex-col {\\n  flex-direction: column;\\n}\\r\\n.flex-col-reverse {\\n  flex-direction: column-reverse;\\n}\\r\\n.flex-wrap {\\n  flex-wrap: wrap;\\n}\\r\\n.items-start {\\n  align-items: flex-start;\\n}\\r\\n.items-end {\\n  align-items: flex-end;\\n}\\r\\n.items-center {\\n  align-items: center;\\n}\\r\\n.justify-center {\\n  justify-content: center;\\n}\\r\\n.justify-between {\\n  justify-content: space-between;\\n}\\r\\n.gap-1 {\\n  gap: 0.25rem;\\n}\\r\\n.gap-2 {\\n  gap: 0.5rem;\\n}\\r\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\r\\n.gap-4 {\\n  gap: 1rem;\\n}\\r\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\r\\n.gap-8 {\\n  gap: 2rem;\\n}\\r\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-x-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\r\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-1\\\\.5 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\r\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\r\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\r\\n.overflow-x-auto {\\n  overflow-x: auto;\\n}\\r\\n.truncate {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\r\\n.whitespace-nowrap {\\n  white-space: nowrap;\\n}\\r\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\r\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\r\\n.rounded-lg {\\n  border-radius: var(--radius);\\n}\\r\\n.rounded-md {\\n  border-radius: calc(var(--radius) - 2px);\\n}\\r\\n.rounded-sm {\\n  border-radius: calc(var(--radius) - 4px);\\n}\\r\\n.rounded-xl {\\n  border-radius: 0.75rem;\\n}\\r\\n.border {\\n  border-width: 1px;\\n}\\r\\n.border-2 {\\n  border-width: 2px;\\n}\\r\\n.border-8 {\\n  border-width: 8px;\\n}\\r\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\r\\n.border-b-2 {\\n  border-bottom-width: 2px;\\n}\\r\\n.border-b-\\\\[86px\\\\] {\\n  border-bottom-width: 86px;\\n}\\r\\n.border-l-\\\\[50px\\\\] {\\n  border-left-width: 50px;\\n}\\r\\n.border-r-\\\\[50px\\\\] {\\n  border-right-width: 50px;\\n}\\r\\n.border-t {\\n  border-top-width: 1px;\\n}\\r\\n.border-t-2 {\\n  border-top-width: 2px;\\n}\\r\\n.border-dashed {\\n  border-style: dashed;\\n}\\r\\n.border-amber-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-amber-900\\\\/50 {\\n  border-color: rgb(120 53 15 / 0.5);\\n}\\r\\n.border-black\\\\/50 {\\n  border-color: rgb(0 0 0 / 0.5);\\n}\\r\\n.border-border {\\n  border-color: hsl(var(--border));\\n}\\r\\n.border-destructive {\\n  border-color: hsl(var(--destructive));\\n}\\r\\n.border-destructive\\\\/50 {\\n  border-color: hsl(var(--destructive) / 0.5);\\n}\\r\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-gray-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-gray-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-green-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-green-900\\\\/50 {\\n  border-color: rgb(20 83 45 / 0.5);\\n}\\r\\n.border-input {\\n  border-color: hsl(var(--input));\\n}\\r\\n.border-muted-foreground {\\n  border-color: hsl(var(--muted-foreground));\\n}\\r\\n.border-muted-foreground\\\\/25 {\\n  border-color: hsl(var(--muted-foreground) / 0.25);\\n}\\r\\n.border-primary {\\n  border-color: hsl(var(--primary));\\n}\\r\\n.border-red-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-red-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\\n}\\r\\n.border-red-900\\\\/50 {\\n  border-color: rgb(127 29 29 / 0.5);\\n}\\r\\n.border-theme-blue {\\n  border-color: hsl(var(--theme-blue));\\n}\\r\\n.border-theme-blue\\\\/20 {\\n  border-color: hsl(var(--theme-blue) / 0.2);\\n}\\r\\n.border-theme-purple\\\\/30 {\\n  border-color: hsl(var(--theme-purple) / 0.3);\\n}\\r\\n.border-theme-teal\\\\/30 {\\n  border-color: hsl(var(--theme-teal) / 0.3);\\n}\\r\\n.border-white\\\\/10 {\\n  border-color: rgb(255 255 255 / 0.1);\\n}\\r\\n.border-white\\\\/20 {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n.border-white\\\\/50 {\\n  border-color: rgb(255 255 255 / 0.5);\\n}\\r\\n.border-theme-purple\\\\/50 {\\n  border-color: hsl(var(--theme-purple) / 0.5);\\n}\\r\\n.border-b-theme-teal\\\\/30 {\\n  border-bottom-color: hsl(var(--theme-teal) / 0.3);\\n}\\r\\n.border-l-transparent {\\n  border-left-color: transparent;\\n}\\r\\n.border-r-transparent {\\n  border-right-color: transparent;\\n}\\r\\n.bg-amber-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-amber-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-amber-500\\\\/10 {\\n  background-color: rgb(245 158 11 / 0.1);\\n}\\r\\n.bg-amber-900\\\\/20 {\\n  background-color: rgb(120 53 15 / 0.2);\\n}\\r\\n.bg-background {\\n  background-color: hsl(var(--background));\\n}\\r\\n.bg-background\\\\/50 {\\n  background-color: hsl(var(--background) / 0.5);\\n}\\r\\n.bg-background\\\\/80 {\\n  background-color: hsl(var(--background) / 0.8);\\n}\\r\\n.bg-background\\\\/95 {\\n  background-color: hsl(var(--background) / 0.95);\\n}\\r\\n.bg-black\\\\/20 {\\n  background-color: rgb(0 0 0 / 0.2);\\n}\\r\\n.bg-black\\\\/80 {\\n  background-color: rgb(0 0 0 / 0.8);\\n}\\r\\n.bg-border {\\n  background-color: hsl(var(--border));\\n}\\r\\n.bg-card {\\n  background-color: hsl(var(--card));\\n}\\r\\n.bg-destructive {\\n  background-color: hsl(var(--destructive));\\n}\\r\\n.bg-destructive\\\\/10 {\\n  background-color: hsl(var(--destructive) / 0.1);\\n}\\r\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-300 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-700 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-gray-800 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-green-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-green-500\\\\/20 {\\n  background-color: rgb(34 197 94 / 0.2);\\n}\\r\\n.bg-green-900\\\\/20 {\\n  background-color: rgb(20 83 45 / 0.2);\\n}\\r\\n.bg-muted {\\n  background-color: hsl(var(--muted));\\n}\\r\\n.bg-popover {\\n  background-color: hsl(var(--popover));\\n}\\r\\n.bg-primary {\\n  background-color: hsl(var(--primary));\\n}\\r\\n.bg-primary\\\\/20 {\\n  background-color: hsl(var(--primary) / 0.2);\\n}\\r\\n.bg-red-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-red-500\\\\/10 {\\n  background-color: rgb(239 68 68 / 0.1);\\n}\\r\\n.bg-red-500\\\\/20 {\\n  background-color: rgb(239 68 68 / 0.2);\\n}\\r\\n.bg-red-900\\\\/20 {\\n  background-color: rgb(127 29 29 / 0.2);\\n}\\r\\n.bg-red-950\\\\/20 {\\n  background-color: rgb(69 10 10 / 0.2);\\n}\\r\\n.bg-red-950\\\\/80 {\\n  background-color: rgb(69 10 10 / 0.8);\\n}\\r\\n.bg-secondary {\\n  background-color: hsl(var(--secondary));\\n}\\r\\n.bg-theme-blue {\\n  background-color: hsl(var(--theme-blue));\\n}\\r\\n.bg-theme-blue\\\\/10 {\\n  background-color: hsl(var(--theme-blue) / 0.1);\\n}\\r\\n.bg-theme-blue\\\\/20 {\\n  background-color: hsl(var(--theme-blue) / 0.2);\\n}\\r\\n.bg-theme-blue\\\\/5 {\\n  background-color: hsl(var(--theme-blue) / 0.05);\\n}\\r\\n.bg-theme-purple {\\n  background-color: hsl(var(--theme-purple));\\n}\\r\\n.bg-theme-purple\\\\/10 {\\n  background-color: hsl(var(--theme-purple) / 0.1);\\n}\\r\\n.bg-theme-purple\\\\/20 {\\n  background-color: hsl(var(--theme-purple) / 0.2);\\n}\\r\\n.bg-theme-teal\\\\/10 {\\n  background-color: hsl(var(--theme-teal) / 0.1);\\n}\\r\\n.bg-transparent {\\n  background-color: transparent;\\n}\\r\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\r\\n.bg-white\\\\/10 {\\n  background-color: rgb(255 255 255 / 0.1);\\n}\\r\\n.bg-white\\\\/5 {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\r\\n.bg-gradient-to-b {\\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\\n}\\r\\n.bg-gradient-to-br {\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\r\\n.bg-gradient-to-r {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\r\\n.from-amber-500 {\\n  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-gray-50 {\\n  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-gray-900 {\\n  --tw-gradient-from: #111827 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-green-500 {\\n  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-blue {\\n  --tw-gradient-from: hsl(var(--theme-blue)) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-blue) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-blue\\\\/20 {\\n  --tw-gradient-from: hsl(var(--theme-blue) / 0.2) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-blue) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-purple {\\n  --tw-gradient-from: hsl(var(--theme-purple)) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-purple) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.from-theme-purple\\\\/5 {\\n  --tw-gradient-from: hsl(var(--theme-purple) / 0.05) var(--tw-gradient-from-position);\\n  --tw-gradient-to: hsl(var(--theme-purple) / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\r\\n.via-purple-600 {\\n  --tw-gradient-to: rgb(147 51 234 / 0)  var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), #9333ea var(--tw-gradient-via-position), var(--tw-gradient-to);\\n}\\r\\n.to-black {\\n  --tw-gradient-to: #000 var(--tw-gradient-to-position);\\n}\\r\\n.to-emerald-600 {\\n  --tw-gradient-to: #059669 var(--tw-gradient-to-position);\\n}\\r\\n.to-gray-800 {\\n  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);\\n}\\r\\n.to-orange-600 {\\n  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-500 {\\n  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-500\\\\/20 {\\n  --tw-gradient-to: rgb(168 85 247 / 0.2) var(--tw-gradient-to-position);\\n}\\r\\n.to-purple-600 {\\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\\n}\\r\\n.to-theme-blue {\\n  --tw-gradient-to: hsl(var(--theme-blue)) var(--tw-gradient-to-position);\\n}\\r\\n.to-theme-purple {\\n  --tw-gradient-to: hsl(var(--theme-purple)) var(--tw-gradient-to-position);\\n}\\r\\n.to-white {\\n  --tw-gradient-to: #fff var(--tw-gradient-to-position);\\n}\\r\\n.to-theme-blue\\\\/5 {\\n  --tw-gradient-to: hsl(var(--theme-blue) / 0.05) var(--tw-gradient-to-position);\\n}\\r\\n.bg-\\\\[length\\\\:200\\\\%_100\\\\%\\\\] {\\n  background-size: 200% 100%;\\n}\\r\\n.bg-clip-text {\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n}\\r\\n.fill-current {\\n  fill: currentColor;\\n}\\r\\n.object-cover {\\n  -o-object-fit: cover;\\n     object-fit: cover;\\n}\\r\\n.p-1 {\\n  padding: 0.25rem;\\n}\\r\\n.p-2 {\\n  padding: 0.5rem;\\n}\\r\\n.p-3 {\\n  padding: 0.75rem;\\n}\\r\\n.p-4 {\\n  padding: 1rem;\\n}\\r\\n.p-5 {\\n  padding: 1.25rem;\\n}\\r\\n.p-6 {\\n  padding: 1.5rem;\\n}\\r\\n.p-8 {\\n  padding: 2rem;\\n}\\r\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\r\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\r\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\r\\n.px-8 {\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\r\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\r\\n.py-1\\\\.5 {\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\r\\n.py-10 {\\n  padding-top: 2.5rem;\\n  padding-bottom: 2.5rem;\\n}\\r\\n.py-12 {\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\r\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\r\\n.py-20 {\\n  padding-top: 5rem;\\n  padding-bottom: 5rem;\\n}\\r\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\r\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\r\\n.py-6 {\\n  padding-top: 1.5rem;\\n  padding-bottom: 1.5rem;\\n}\\r\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\r\\n.pb-2 {\\n  padding-bottom: 0.5rem;\\n}\\r\\n.pb-24 {\\n  padding-bottom: 6rem;\\n}\\r\\n.pl-8 {\\n  padding-left: 2rem;\\n}\\r\\n.pr-16 {\\n  padding-right: 4rem;\\n}\\r\\n.pr-2 {\\n  padding-right: 0.5rem;\\n}\\r\\n.pt-0 {\\n  padding-top: 0px;\\n}\\r\\n.pt-2 {\\n  padding-top: 0.5rem;\\n}\\r\\n.pt-6 {\\n  padding-top: 1.5rem;\\n}\\r\\n.text-left {\\n  text-align: left;\\n}\\r\\n.text-center {\\n  text-align: center;\\n}\\r\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\r\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\r\\n.text-4xl {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\r\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\r\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\r\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\r\\n.font-bold {\\n  font-weight: 700;\\n}\\r\\n.font-medium {\\n  font-weight: 500;\\n}\\r\\n.font-semibold {\\n  font-weight: 600;\\n}\\r\\n.leading-none {\\n  line-height: 1;\\n}\\r\\n.tracking-tight {\\n  letter-spacing: -0.025em;\\n}\\r\\n.text-amber-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(251 191 36 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-amber-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(245 158 11 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-amber-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(217 119 6 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-black {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-card-foreground {\\n  color: hsl(var(--card-foreground));\\n}\\r\\n.text-current {\\n  color: currentColor;\\n}\\r\\n.text-destructive {\\n  color: hsl(var(--destructive));\\n}\\r\\n.text-destructive-foreground {\\n  color: hsl(var(--destructive-foreground));\\n}\\r\\n.text-foreground {\\n  color: hsl(var(--foreground));\\n}\\r\\n.text-gray-200 {\\n  --tw-text-opacity: 1;\\n  color: rgb(229 231 235 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-green-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-green-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-muted-foreground {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n.text-popover-foreground {\\n  color: hsl(var(--popover-foreground));\\n}\\r\\n.text-primary {\\n  color: hsl(var(--primary));\\n}\\r\\n.text-primary-foreground {\\n  color: hsl(var(--primary-foreground));\\n}\\r\\n.text-red-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-red-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\r\\n.text-secondary-foreground {\\n  color: hsl(var(--secondary-foreground));\\n}\\r\\n.text-theme-blue {\\n  color: hsl(var(--theme-blue));\\n}\\r\\n.text-theme-purple {\\n  color: hsl(var(--theme-purple));\\n}\\r\\n.text-theme-teal {\\n  color: hsl(var(--theme-teal));\\n}\\r\\n.text-transparent {\\n  color: transparent;\\n}\\r\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n.underline-offset-4 {\\n  text-underline-offset: 4px;\\n}\\r\\n.opacity-0 {\\n  opacity: 0;\\n}\\r\\n.opacity-25 {\\n  opacity: 0.25;\\n}\\r\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\r\\n.opacity-70 {\\n  opacity: 0.7;\\n}\\r\\n.opacity-75 {\\n  opacity: 0.75;\\n}\\r\\n.opacity-80 {\\n  opacity: 0.8;\\n}\\r\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-md {\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-xl {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-2xl {\\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n.shadow-theme-purple\\\\/10 {\\n  --tw-shadow-color: hsl(var(--theme-purple) / 0.1);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\r\\n.shadow-theme-purple\\\\/20 {\\n  --tw-shadow-color: hsl(var(--theme-purple) / 0.2);\\n  --tw-shadow: var(--tw-shadow-colored);\\n}\\r\\n.outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n.outline {\\n  outline-style: solid;\\n}\\r\\n.ring-offset-background {\\n  --tw-ring-offset-color: hsl(var(--background));\\n}\\r\\n.blur {\\n  --tw-blur: blur(8px);\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\r\\n.backdrop-blur-md {\\n  --tw-backdrop-blur: blur(12px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\r\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-opacity {\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.transition-transform {\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\r\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\r\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\r\\n.duration-500 {\\n  transition-duration: 500ms;\\n}\\r\\n.will-change-\\\\[transform\\\\2c filter\\\\2c opacity\\\\] {\\n  will-change: transform,filter,opacity;\\n}\\r\\n@keyframes enter {\\n\\n  from {\\n    opacity: var(--tw-enter-opacity, 1);\\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\\n  }\\n}\\r\\n@keyframes exit {\\n\\n  to {\\n    opacity: var(--tw-exit-opacity, 1);\\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\\n  }\\n}\\r\\n.animate-in {\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n.fade-in-80 {\\n  --tw-enter-opacity: 0.8;\\n}\\r\\n.duration-200 {\\n  animation-duration: 200ms;\\n}\\r\\n.duration-300 {\\n  animation-duration: 300ms;\\n}\\r\\n.duration-500 {\\n  animation-duration: 500ms;\\n}\\r\\n\\r\\n/* Subtle glass effect */\\r\\n.subtle-glass {\\r\\n  background: rgba(255, 255, 255, 0.03);\\r\\n  backdrop-filter: blur(8px);\\r\\n  -webkit-backdrop-filter: blur(8px);\\r\\n  border: 1px solid rgba(255, 255, 255, 0.05);\\r\\n  transition: background-color var(--theme-transition), border-color var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-glass {\\r\\n  background: rgba(0, 0, 0, 0.03);\\r\\n  border: 1px solid rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle border glow */\\r\\n.subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05);\\r\\n  transition: box-shadow var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle hover effect */\\r\\n.subtle-hover {\\r\\n  transition: all 0.2s ease;\\r\\n}\\r\\n\\r\\n.subtle-hover:hover {\\r\\n  background-color: rgba(255, 255, 255, 0.05);\\r\\n}\\r\\n\\r\\n.light .subtle-hover:hover {\\r\\n  background-color: rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Gradient text */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n  transition: background var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .gradient-text {\\r\\n  background: linear-gradient(to right, #000, rgba(0, 0, 0, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n}\\r\\n\\r\\n/* Subtle grid background */\\r\\n.subtle-grid {\\r\\n  background-size: 30px 30px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-grid {\\r\\n  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),\\r\\n    linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Reveal animation classes */\\r\\n.reveal {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n  transition: opacity 0.5s ease, transform 0.5s ease;\\r\\n}\\r\\n\\r\\n.reveal.active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n/* Button shine effect */\\r\\n.btn-shine {\\r\\n  position: relative;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.btn-shine::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: -100%;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-image: linear-gradient(\\r\\n    90deg,\\r\\n    rgba(255, 255, 255, 0) 0%,\\r\\n    rgba(255, 255, 255, 0.05) 50%,\\r\\n    rgba(255, 255, 255, 0) 100%\\r\\n  );\\r\\n  transition: left 0.5s ease;\\r\\n}\\r\\n\\r\\n.btn-shine:hover::after {\\r\\n  left: 100%;\\r\\n}\\r\\n\\r\\n/* Dot pattern */\\r\\n.dot-pattern {\\r\\n  background-image: radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .dot-pattern {\\r\\n  background-image: radial-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Flow border effect */\\r\\n.flow-border {\\r\\n  position: relative;\\r\\n  z-index: 0;\\r\\n}\\r\\n\\r\\n.flow-border::before {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: -2px;\\r\\n  left: -2px;\\r\\n  right: -2px;\\r\\n  bottom: -2px;\\r\\n  background: var(\\r\\n    --bg-flow-border,\\r\\n    linear-gradient(\\r\\n      90deg,\\r\\n      var(--border-start-color),\\r\\n      var(--border-mid-color),\\r\\n      var(--border-end-color),\\r\\n      var(--border-start-color)\\r\\n    )\\r\\n  );\\r\\n  border-radius: inherit;\\r\\n  z-index: -1;\\r\\n  background-size: 300% 100%;\\r\\n  animation: border-flow 3s ease infinite;\\r\\n  opacity: 0;\\r\\n  transition: opacity 0.3s ease;\\r\\n}\\r\\n\\r\\n.flow-border:hover::before {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n/* Glow effect */\\r\\n.glow-effect {\\r\\n  transition: box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\n.glow-effect:hover {\\r\\n  --glow-color: var(--theme-blue-rgb, 33, 150, 243);\\r\\n  box-shadow: 0 0 10px 2px rgba(var(--glow-color), 0.3);\\r\\n}\\r\\n\\r\\n.glow-effect.purple:hover {\\r\\n  --glow-color: var(--theme-purple-rgb, 156, 39, 176);\\r\\n}\\r\\n\\r\\n.glow-effect.teal:hover {\\r\\n  --glow-color: var(--theme-teal-rgb, 0, 188, 212);\\r\\n}\\r\\n\\r\\n/* Theme transition */\\r\\n.theme-transition {\\r\\n  transition: all var(--theme-transition);\\r\\n}\\r\\n\\r\\n/* Page transition */\\r\\n.page-transition-enter {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n}\\r\\n\\r\\n.page-transition-enter-active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n.page-transition-exit {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n.page-transition-exit-active {\\r\\n  opacity: 0;\\r\\n  transform: translateY(-10px);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n/* Nav link with indicator */\\r\\n.nav-link {\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.nav-link::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  bottom: -4px;\\r\\n  left: 0;\\r\\n  width: 0;\\r\\n  height: 2px;\\r\\n  background-color: hsl(var(--theme-blue));\\r\\n  transition: width 0.3s ease;\\r\\n}\\r\\n\\r\\n.nav-link:hover::after,\\r\\n.nav-link.active::after {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n  height: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: transparent;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: rgba(255, 255, 255, 0.1);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(255, 255, 255, 0.2);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb {\\r\\n  background: rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(0, 0, 0, 0.2);\\r\\n}\\r\\n\\r\\n/* Animation for loading spinner */\\r\\n@keyframes border-flow {\\r\\n  0% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n  100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes subtle-pulse {\\r\\n  0% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 1;\\r\\n  }\\r\\n  100% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-subtle-pulse {\\r\\n  animation: subtle-pulse 2s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-rotate-loader {\\r\\n  animation: spin 1s linear infinite;\\r\\n}\\r\\n\\r\\n@keyframes spin {\\r\\n  from {\\r\\n    transform: rotate(0deg);\\r\\n  }\\r\\n  to {\\r\\n    transform: rotate(360deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n.placeholder\\\\:text-muted-foreground::-moz-placeholder {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.placeholder\\\\:text-muted-foreground::placeholder {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.hover\\\\:scale-105:hover {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.hover\\\\:border-black\\\\/20:hover {\\n  border-color: rgb(0 0 0 / 0.2);\\n}\\r\\n\\r\\n.hover\\\\:border-black\\\\/70:hover {\\n  border-color: rgb(0 0 0 / 0.7);\\n}\\r\\n\\r\\n.hover\\\\:border-gray-400:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:border-theme-blue\\\\/50:hover {\\n  border-color: hsl(var(--theme-blue) / 0.5);\\n}\\r\\n\\r\\n.hover\\\\:border-white\\\\/20:hover {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n\\r\\n.hover\\\\:border-white\\\\/30:hover {\\n  border-color: rgb(255 255 255 / 0.3);\\n}\\r\\n\\r\\n.hover\\\\:bg-accent:hover {\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.hover\\\\:bg-black\\\\/5:hover {\\n  background-color: rgb(0 0 0 / 0.05);\\n}\\r\\n\\r\\n.hover\\\\:bg-destructive\\\\/10:hover {\\n  background-color: hsl(var(--destructive) / 0.1);\\n}\\r\\n\\r\\n.hover\\\\:bg-destructive\\\\/90:hover {\\n  background-color: hsl(var(--destructive) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-gray-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:bg-primary\\\\/10:hover {\\n  background-color: hsl(var(--primary) / 0.1);\\n}\\r\\n\\r\\n.hover\\\\:bg-primary\\\\/90:hover {\\n  background-color: hsl(var(--primary) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-secondary\\\\/80:hover {\\n  background-color: hsl(var(--secondary) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:bg-theme-blue\\\\/10:hover {\\n  background-color: hsl(var(--theme-blue) / 0.1);\\n}\\r\\n\\r\\n.hover\\\\:bg-theme-purple\\\\/90:hover {\\n  background-color: hsl(var(--theme-purple) / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:bg-white\\\\/5:hover {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\r\\n\\r\\n.hover\\\\:bg-white\\\\/90:hover {\\n  background-color: rgb(255 255 255 / 0.9);\\n}\\r\\n\\r\\n.hover\\\\:text-accent-foreground:hover {\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-black:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:text-foreground:hover {\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.hover\\\\:text-primary\\\\/80:hover {\\n  color: hsl(var(--primary) / 0.8);\\n}\\r\\n\\r\\n.hover\\\\:text-theme-blue:hover {\\n  color: hsl(var(--theme-blue));\\n}\\r\\n\\r\\n.hover\\\\:text-white:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.hover\\\\:underline:hover {\\n  text-decoration-line: underline;\\n}\\r\\n\\r\\n.hover\\\\:opacity-100:hover {\\n  opacity: 1;\\n}\\r\\n\\r\\n.hover\\\\:opacity-90:hover {\\n  opacity: 0.9;\\n}\\r\\n\\r\\n.hover\\\\:shadow-lg:hover {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.focus\\\\:border-primary:focus {\\n  border-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.focus\\\\:border-theme-blue:focus {\\n  border-color: hsl(var(--theme-blue));\\n}\\r\\n\\r\\n.focus\\\\:bg-accent:focus {\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.focus\\\\:text-accent-foreground:focus {\\n  color: hsl(var(--accent-foreground));\\n}\\r\\n\\r\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus\\\\:ring-primary:focus {\\n  --tw-ring-color: hsl(var(--primary));\\n}\\r\\n\\r\\n.focus\\\\:ring-ring:focus {\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n\\r\\n.focus\\\\:ring-theme-blue\\\\/50:focus {\\n  --tw-ring-color: hsl(var(--theme-blue) / 0.5);\\n}\\r\\n\\r\\n.focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:outline-none:focus-visible {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-2:focus-visible {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-ring:focus-visible {\\n  --tw-ring-color: hsl(var(--ring));\\n}\\r\\n\\r\\n.focus-visible\\\\:ring-offset-2:focus-visible {\\n  --tw-ring-offset-width: 2px;\\n}\\r\\n\\r\\n.disabled\\\\:pointer-events-none:disabled {\\n  pointer-events: none;\\n}\\r\\n\\r\\n.disabled\\\\:cursor-not-allowed:disabled {\\n  cursor: not-allowed;\\n}\\r\\n\\r\\n.disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.group:hover .group-hover\\\\:opacity-100 {\\n  opacity: 1;\\n}\\r\\n\\r\\n.peer:disabled ~ .peer-disabled\\\\:cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\r\\n\\r\\n.peer:disabled ~ .peer-disabled\\\\:opacity-70 {\\n  opacity: 0.7;\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\]\\\\:pointer-events-none[data-disabled] {\\n  pointer-events: none;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:bg-background[data-state=\\\"active\\\"] {\\n  background-color: hsl(var(--background));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:bg-theme-blue[data-state=\\\"active\\\"] {\\n  background-color: hsl(var(--theme-blue));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:bg-accent[data-state=\\\"open\\\"] {\\n  background-color: hsl(var(--accent));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:text-foreground[data-state=\\\"active\\\"] {\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:text-white[data-state=\\\"active\\\"] {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:text-muted-foreground[data-state=\\\"open\\\"] {\\n  color: hsl(var(--muted-foreground));\\n}\\r\\n\\r\\n.data-\\\\[disabled\\\\]\\\\:opacity-50[data-disabled] {\\n  opacity: 0.5;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=active\\\\]\\\\:shadow-sm[data-state=\\\"active\\\"] {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:animate-in[data-state=\\\"open\\\"] {\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:animate-out[data-state=\\\"closed\\\"] {\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-0[data-state=\\\"closed\\\"] {\\n  --tw-exit-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:fade-in-0[data-state=\\\"open\\\"] {\\n  --tw-enter-opacity: 0;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:zoom-out-95[data-state=\\\"closed\\\"] {\\n  --tw-exit-scale: .95;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-95[data-state=\\\"open\\\"] {\\n  --tw-enter-scale: .95;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left-1\\\\/2[data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-x: -50%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top-\\\\[48\\\\%\\\\][data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-y: -48%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left-1\\\\/2[data-state=\\\"open\\\"] {\\n  --tw-enter-translate-x: -50%;\\n}\\r\\n\\r\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top-\\\\[48\\\\%\\\\][data-state=\\\"open\\\"] {\\n  --tw-enter-translate-y: -48%;\\n}\\r\\n\\r\\n.dark\\\\:border-destructive:is(.dark *) {\\n  border-color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.dark\\\\:border-white\\\\/20:is(.dark *) {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n\\r\\n.dark\\\\:text-white:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:border-white\\\\/20:hover:is(.dark *) {\\n  border-color: rgb(255 255 255 / 0.2);\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:border-white\\\\/40:hover:is(.dark *) {\\n  border-color: rgb(255 255 255 / 0.4);\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:bg-white\\\\/5:hover:is(.dark *) {\\n  background-color: rgb(255 255 255 / 0.05);\\n}\\r\\n\\r\\n.dark\\\\:hover\\\\:text-white:hover:is(.dark *) {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\r\\n\\r\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .sm\\\\:max-w-\\\\[600px\\\\] {\\n    max-width: 600px;\\n  }\\n\\n  .sm\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:justify-end {\\n    justify-content: flex-end;\\n  }\\n\\n  .sm\\\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\n\\n  .sm\\\\:rounded-lg {\\n    border-radius: var(--radius);\\n  }\\n\\n  .sm\\\\:text-left {\\n    text-align: left;\\n  }\\n}\\r\\n\\r\\n@media (min-width: 768px) {\\n\\n  .md\\\\:mb-0 {\\n    margin-bottom: 0px;\\n  }\\n\\n  .md\\\\:mt-\\\\[-180px\\\\] {\\n    margin-top: -180px;\\n  }\\n\\n  .md\\\\:mt-0 {\\n    margin-top: 0px;\\n  }\\n\\n  .md\\\\:mt-6 {\\n    margin-top: 1.5rem;\\n  }\\n\\n  .md\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .md\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .md\\\\:gap-8 {\\n    gap: 2rem;\\n  }\\n\\n  .md\\\\:p-6 {\\n    padding: 1.5rem;\\n  }\\n\\n  .md\\\\:py-12 {\\n    padding-top: 3rem;\\n    padding-bottom: 3rem;\\n  }\\n\\n  .md\\\\:py-16 {\\n    padding-top: 4rem;\\n    padding-bottom: 4rem;\\n  }\\n\\n  .md\\\\:py-24 {\\n    padding-top: 6rem;\\n    padding-bottom: 6rem;\\n  }\\n\\n  .md\\\\:py-32 {\\n    padding-top: 8rem;\\n    padding-bottom: 8rem;\\n  }\\n\\n  .md\\\\:text-2xl {\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n\\n  .md\\\\:text-3xl {\\n    font-size: 1.875rem;\\n    line-height: 2.25rem;\\n  }\\n\\n  .md\\\\:text-4xl {\\n    font-size: 2.25rem;\\n    line-height: 2.5rem;\\n  }\\n\\n  .md\\\\:text-5xl {\\n    font-size: 3rem;\\n    line-height: 1;\\n  }\\n\\n  .md\\\\:text-6xl {\\n    font-size: 3.75rem;\\n    line-height: 1;\\n  }\\n\\n  .md\\\\:text-xl {\\n    font-size: 1.25rem;\\n    line-height: 1.75rem;\\n  }\\n}\\r\\n\\r\\n@media (min-width: 1024px) {\\n\\n  .lg\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-5 {\\n    grid-template-columns: repeat(5, minmax(0, 1fr));\\n  }\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\+div\\\\]\\\\:translate-y-\\\\[-3px\\\\]>svg+div {\\n  --tw-translate-y: -3px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:absolute>svg {\\n  position: absolute;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:left-4>svg {\\n  left: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:top-4>svg {\\n  top: 1rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-destructive>svg {\\n  color: hsl(var(--destructive));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-foreground>svg {\\n  color: hsl(var(--foreground));\\n}\\r\\n\\r\\n.\\\\[\\\\&\\\\>svg\\\\~\\\\*\\\\]\\\\:pl-7>svg~* {\\n  padding-left: 1.75rem;\\n}\\r\\n\\r\\n.\\\\[\\\\&_p\\\\]\\\\:leading-relaxed p {\\n  line-height: 1.625;\\n}\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;EAAd;IAAA,uBAAc;IAAd,0BAAc;IAAd,iBAAc;IAAd,+BAAc;IAAd,oBAAc;IAAd,kCAAc;IAAd,uBAAc;IAAd,8BAAc;IAAd,2BAAc;IAAd,oCAAc;IAAd,uBAAc;IAAd,kCAAc;IAAd,wBAAc;IAAd,iCAAc;IAAd,4BAAc;IAAd,kCAAc;IAAd,sBAAc;IAAd,qBAAc;IAAd,oBAAc;IAAd,gBAAc;;IAAd,iBAAc;IAAd,0BAAc;IAAd,4BAAc;IAAd,0BAAc;;IAAd,gBAAc;IAAd,4BAAc;;IAAd,uBAAc;IAAd,6CAAc;IAAd,2CAAc;IAAd,0CAAc;;IAAd,qBAAc;IAAd,6BAAc;EAAA;;EAAd;IAAA,0BAAc;IAAd,sBAAc;IAAd,oBAAc;IAAd,2BAAc;IAAd,uBAAc;IAAd,8BAAc;IAAd,mBAAc;IAAd,kCAAc;IAAd,2BAAc;IAAd,gCAAc;IAAd,uBAAc;IAAd,gCAAc;IAAd,wBAAc;IAAd,6BAAc;IAAd,4BAAc;IAAd,kCAAc;IAAd,wBAAc;IAAd,uBAAc;IAAd,sBAAc;;IAAd,iBAAc;IAAd,0BAAc;IAAd,4BAAc;IAAd,0BAAc;;IAAd,gBAAc;IAAd,4BAAc;;IAAd,uBAAc;IAAd,6CAAc;IAAd,2CAAc;IAAd,0CAAc;EAAA;EAAd;EAAA;AAAc;EAAd;EAAA,wCAAc;EAAd,6BAAc;IAAd,yCAAc;IAAd;AAAc;AACd;EAAA,WAAoB;EAApB,kBAAoB;EAApB,iBAAoB;EAApB,mBAAoB;EAApB;AAAoB;AAApB;;EAAA;IAAA;EAAoB;AAAA;AACpB;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,yBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;;EAAnB;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yBAAmB;KAAnB,sBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,gEAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,oEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,kEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA,2EAAmB;EAAnB,2EAAmB;EAAnB;AAAmB;AAAnB;EAAA,iFAAmB;EAAnB,2EAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,6EAAmB;EAAnB;AAAmB;AAAnB;EAAA,oFAAmB;EAAnB,6EAAmB;EAAnB;AAAmB;AAAnB;EAAA,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;KAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,gDAAmB;EAAnB,6DAAmB;EAAnB;AAAmB;AAAnB;EAAA,iDAAmB;EAAnB;AAAmB;AAAnB;EAAA,iDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA,mCAAmB;IAAnB;EAAmB;AAAA;AAAnB;;EAAA;IAAA,kCAAmB;IAAnB;EAAmB;AAAA;AAAnB;EAAA,qBAAmB;EAAnB,yBAAmB;EAAnB,2BAAmB;EAAnB,yBAAmB;EAAnB,0BAAmB;EAAnB,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AAyFnB,wBAAwB;AACxB;EACE,qCAAqC;EACrC,0BAA0B;EAC1B,kCAAkC;EAClC,2CAA2C;EAC3C,0FAA0F;AAC5F;;AAEA;EACE,+BAA+B;EAC/B,qCAAqC;AACvC;;AAEA,uBAAuB;AACvB;EACE,+CAA+C;EAC/C,8CAA8C;AAChD;;AAEA;EACE,yCAAyC;AAC3C;;AAEA,wBAAwB;AACxB;EACE,yBAAyB;AAC3B;;AAEA;EACE,2CAA2C;AAC7C;;AAEA;EACE,qCAAqC;AACvC;;AAEA,kBAAkB;AAClB;EACE,sEAAsE;EACtE,6BAA6B;EAC7B,oCAAoC;EACpC,qBAAqB;EACrB,8CAA8C;AAChD;;AAEA;EACE,gEAAgE;EAChE,6BAA6B;EAC7B,oCAAoC;EACpC,qBAAqB;AACvB;;AAEA,2BAA2B;AAC3B;EACE,0BAA0B;EAC1B,oDAAoD;AACtD;;AAEA;EACE;wEACsE;AACxE;;AAEA,6BAA6B;AAC7B;EACE,UAAU;EACV,2BAA2B;EAC3B,kDAAkD;AACpD;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA,wBAAwB;AACxB;EACE,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,WAAW;EACX,WAAW;EACX,YAAY;EACZ;;;;;GAKC;EACD,0BAA0B;AAC5B;;AAEA;EACE,UAAU;AACZ;;AAEA,gBAAgB;AAChB;EACE,gFAAgF;EAChF,0BAA0B;EAC1B,oDAAoD;AACtD;;AAEA;EACE,0EAA0E;AAC5E;;AAEA,uBAAuB;AACvB;EACE,kBAAkB;EAClB,UAAU;AACZ;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,SAAS;EACT,UAAU;EACV,WAAW;EACX,YAAY;EACZ;;;;;;;;;GASC;EACD,sBAAsB;EACtB,WAAW;EACX,0BAA0B;EAC1B,uCAAuC;EACvC,UAAU;EACV,6BAA6B;AAC/B;;AAEA;EACE,UAAU;AACZ;;AAEA,gBAAgB;AAChB;EACE,gCAAgC;AAClC;;AAEA;EACE,iDAAiD;EACjD,qDAAqD;AACvD;;AAEA;EACE,mDAAmD;AACrD;;AAEA;EACE,gDAAgD;AAClD;;AAEA,qBAAqB;AACrB;EACE,uCAAuC;AACzC;;AAEA,oBAAoB;AACpB;EACE,UAAU;EACV,2BAA2B;AAC7B;;AAEA;EACE,UAAU;EACV,wBAAwB;EACxB,wCAAwC;AAC1C;;AAEA;EACE,UAAU;EACV,wBAAwB;AAC1B;;AAEA;EACE,UAAU;EACV,4BAA4B;EAC5B,wCAAwC;AAC1C;;AAEA,4BAA4B;AAC5B;EACE,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,YAAY;EACZ,OAAO;EACP,QAAQ;EACR,WAAW;EACX,wCAAwC;EACxC,2BAA2B;AAC7B;;AAEA;;EAEE,WAAW;AACb;;AAEA,qBAAqB;AACrB;EACE,UAAU;EACV,WAAW;AACb;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,8BAA8B;AAChC;;AAEA,kCAAkC;AAClC;EACE;IACE,2BAA2B;EAC7B;EACA;IACE,6BAA6B;EAC/B;EACA;IACE,2BAA2B;EAC7B;AACF;;AAEA;EACE;IACE,YAAY;EACd;EACA;IACE,UAAU;EACZ;EACA;IACE,YAAY;EACd;AACF;;AAEA;EACE,+CAA+C;AACjD;;AAEA;EACE,kCAAkC;AACpC;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,yBAAyB;EAC3B;AACF;;AArXA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,kBA6XA;EA7XA,kBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,sBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,kBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,+EA6XA;EA7XA,mGA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,8BA6XA;EA7XA;AA6XA;;AA7XA;EAAA,2GA6XA;EA7XA,yGA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,8BA6XA;EA7XA;AA6XA;;AA7XA;EAAA,2GA6XA;EA7XA,yGA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,0CA6XA;EA7XA,uDA6XA;EA7XA;AA6XA;;AA7XA;EAAA,qBA6XA;EA7XA,yBA6XA;EA7XA,2BA6XA;EA7XA,yBA6XA;EA7XA,0BA6XA;EA7XA,+BA6XA;EA7XA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA,yBA6XA;EA7XA,0BA6XA;EA7XA,wBA6XA;EA7XA,yBA6XA;EA7XA,8BA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA,oBA6XA;EA7XA;AA6XA;;AA7XA;;EAAA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA,uBA6XA;IA7XA,sDA6XA;IA7XA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;AAAA;;AA7XA;;EAAA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,iBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,mBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,kBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,eA6XA;IA7XA;EA6XA;;EA7XA;IAAA,kBA6XA;IA7XA;EA6XA;;EA7XA;IAAA,kBA6XA;IA7XA;EA6XA;AAAA;;AA7XA;;EAAA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;;EA7XA;IAAA;EA6XA;AAAA;;AA7XA;EAAA,sBA6XA;EA7XA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA;;AA7XA;EAAA;AA6XA\",\"sourcesContent\":[\"@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n\\r\\n@layer base {\\r\\n  :root {\\r\\n    --background: 0 0% 100%;\\r\\n    --foreground: 240 10% 3.9%;\\r\\n    --card: 0 0% 100%;\\r\\n    --card-foreground: 240 10% 3.9%;\\r\\n    --popover: 0 0% 100%;\\r\\n    --popover-foreground: 240 10% 3.9%;\\r\\n    --primary: 240 5.9% 10%;\\r\\n    --primary-foreground: 0 0% 98%;\\r\\n    --secondary: 240 4.8% 95.9%;\\r\\n    --secondary-foreground: 240 5.9% 10%;\\r\\n    --muted: 240 4.8% 95.9%;\\r\\n    --muted-foreground: 240 3.8% 46.1%;\\r\\n    --accent: 240 4.8% 95.9%;\\r\\n    --accent-foreground: 240 5.9% 10%;\\r\\n    --destructive: 0 84.2% 60.2%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 5.9% 90%;\\r\\n    --input: 240 5.9% 90%;\\r\\n    --ring: 240 5.9% 10%;\\r\\n    --radius: 0.5rem;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 50%;\\r\\n    --theme-purple: 260 100% 60%;\\r\\n    --theme-teal: 180 100% 40%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 50%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.5);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.5);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.5);\\r\\n\\r\\n    /* Theme transition */\\r\\n    --theme-transition: 0.3s ease;\\r\\n  }\\r\\n\\r\\n  .dark {\\r\\n    --background: 240 10% 3.9%;\\r\\n    --foreground: 0 0% 98%;\\r\\n    --card: 240 10% 3.9%;\\r\\n    --card-foreground: 0 0% 98%;\\r\\n    --popover: 240 10% 3.9%;\\r\\n    --popover-foreground: 0 0% 98%;\\r\\n    --primary: 0 0% 98%;\\r\\n    --primary-foreground: 240 5.9% 10%;\\r\\n    --secondary: 240 3.7% 15.9%;\\r\\n    --secondary-foreground: 0 0% 98%;\\r\\n    --muted: 240 3.7% 15.9%;\\r\\n    --muted-foreground: 240 5% 64.9%;\\r\\n    --accent: 240 3.7% 15.9%;\\r\\n    --accent-foreground: 0 0% 98%;\\r\\n    --destructive: 0 62.8% 30.6%;\\r\\n    --destructive-foreground: 0 0% 98%;\\r\\n    --border: 240 3.7% 15.9%;\\r\\n    --input: 240 3.7% 15.9%;\\r\\n    --ring: 240 4.9% 83.9%;\\r\\n\\r\\n    /* Theme colors */\\r\\n    --theme-blue: 210 100% 60%;\\r\\n    --theme-purple: 260 100% 70%;\\r\\n    --theme-teal: 180 100% 50%;\\r\\n\\r\\n    /* Glow colors */\\r\\n    --glow-color: 210, 100%, 60%;\\r\\n\\r\\n    /* Flow border colors */\\r\\n    --border-start-color: rgba(33, 150, 243, 0.7);\\r\\n    --border-mid-color: rgba(156, 39, 176, 0.7);\\r\\n    --border-end-color: rgba(0, 188, 212, 0.7);\\r\\n  }\\r\\n}\\r\\n\\r\\n@layer base {\\r\\n  * {\\r\\n    @apply border-border;\\r\\n  }\\r\\n  body {\\r\\n    @apply bg-background text-foreground;\\r\\n    font-feature-settings: \\\"rlig\\\" 1, \\\"calt\\\" 1;\\r\\n    transition: background-color var(--theme-transition), color var(--theme-transition);\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Subtle glass effect */\\r\\n.subtle-glass {\\r\\n  background: rgba(255, 255, 255, 0.03);\\r\\n  backdrop-filter: blur(8px);\\r\\n  -webkit-backdrop-filter: blur(8px);\\r\\n  border: 1px solid rgba(255, 255, 255, 0.05);\\r\\n  transition: background-color var(--theme-transition), border-color var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-glass {\\r\\n  background: rgba(0, 0, 0, 0.03);\\r\\n  border: 1px solid rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle border glow */\\r\\n.subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05);\\r\\n  transition: box-shadow var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-border-glow {\\r\\n  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Subtle hover effect */\\r\\n.subtle-hover {\\r\\n  transition: all 0.2s ease;\\r\\n}\\r\\n\\r\\n.subtle-hover:hover {\\r\\n  background-color: rgba(255, 255, 255, 0.05);\\r\\n}\\r\\n\\r\\n.light .subtle-hover:hover {\\r\\n  background-color: rgba(0, 0, 0, 0.05);\\r\\n}\\r\\n\\r\\n/* Gradient text */\\r\\n.gradient-text {\\r\\n  background: linear-gradient(to right, #fff, rgba(255, 255, 255, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n  transition: background var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .gradient-text {\\r\\n  background: linear-gradient(to right, #000, rgba(0, 0, 0, 0.75));\\r\\n  -webkit-background-clip: text;\\r\\n  -webkit-text-fill-color: transparent;\\r\\n  background-clip: text;\\r\\n}\\r\\n\\r\\n/* Subtle grid background */\\r\\n.subtle-grid {\\r\\n  background-size: 30px 30px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .subtle-grid {\\r\\n  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),\\r\\n    linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Reveal animation classes */\\r\\n.reveal {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n  transition: opacity 0.5s ease, transform 0.5s ease;\\r\\n}\\r\\n\\r\\n.reveal.active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n/* Button shine effect */\\r\\n.btn-shine {\\r\\n  position: relative;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n.btn-shine::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: -100%;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background-image: linear-gradient(\\r\\n    90deg,\\r\\n    rgba(255, 255, 255, 0) 0%,\\r\\n    rgba(255, 255, 255, 0.05) 50%,\\r\\n    rgba(255, 255, 255, 0) 100%\\r\\n  );\\r\\n  transition: left 0.5s ease;\\r\\n}\\r\\n\\r\\n.btn-shine:hover::after {\\r\\n  left: 100%;\\r\\n}\\r\\n\\r\\n/* Dot pattern */\\r\\n.dot-pattern {\\r\\n  background-image: radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);\\r\\n  background-size: 20px 20px;\\r\\n  transition: background-image var(--theme-transition);\\r\\n}\\r\\n\\r\\n.light .dot-pattern {\\r\\n  background-image: radial-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px);\\r\\n}\\r\\n\\r\\n/* Flow border effect */\\r\\n.flow-border {\\r\\n  position: relative;\\r\\n  z-index: 0;\\r\\n}\\r\\n\\r\\n.flow-border::before {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  top: -2px;\\r\\n  left: -2px;\\r\\n  right: -2px;\\r\\n  bottom: -2px;\\r\\n  background: var(\\r\\n    --bg-flow-border,\\r\\n    linear-gradient(\\r\\n      90deg,\\r\\n      var(--border-start-color),\\r\\n      var(--border-mid-color),\\r\\n      var(--border-end-color),\\r\\n      var(--border-start-color)\\r\\n    )\\r\\n  );\\r\\n  border-radius: inherit;\\r\\n  z-index: -1;\\r\\n  background-size: 300% 100%;\\r\\n  animation: border-flow 3s ease infinite;\\r\\n  opacity: 0;\\r\\n  transition: opacity 0.3s ease;\\r\\n}\\r\\n\\r\\n.flow-border:hover::before {\\r\\n  opacity: 1;\\r\\n}\\r\\n\\r\\n/* Glow effect */\\r\\n.glow-effect {\\r\\n  transition: box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\n.glow-effect:hover {\\r\\n  --glow-color: var(--theme-blue-rgb, 33, 150, 243);\\r\\n  box-shadow: 0 0 10px 2px rgba(var(--glow-color), 0.3);\\r\\n}\\r\\n\\r\\n.glow-effect.purple:hover {\\r\\n  --glow-color: var(--theme-purple-rgb, 156, 39, 176);\\r\\n}\\r\\n\\r\\n.glow-effect.teal:hover {\\r\\n  --glow-color: var(--theme-teal-rgb, 0, 188, 212);\\r\\n}\\r\\n\\r\\n/* Theme transition */\\r\\n.theme-transition {\\r\\n  transition: all var(--theme-transition);\\r\\n}\\r\\n\\r\\n/* Page transition */\\r\\n.page-transition-enter {\\r\\n  opacity: 0;\\r\\n  transform: translateY(10px);\\r\\n}\\r\\n\\r\\n.page-transition-enter-active {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n.page-transition-exit {\\r\\n  opacity: 1;\\r\\n  transform: translateY(0);\\r\\n}\\r\\n\\r\\n.page-transition-exit-active {\\r\\n  opacity: 0;\\r\\n  transform: translateY(-10px);\\r\\n  transition: opacity 0.3s, transform 0.3s;\\r\\n}\\r\\n\\r\\n/* Nav link with indicator */\\r\\n.nav-link {\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.nav-link::after {\\r\\n  content: \\\"\\\";\\r\\n  position: absolute;\\r\\n  bottom: -4px;\\r\\n  left: 0;\\r\\n  width: 0;\\r\\n  height: 2px;\\r\\n  background-color: hsl(var(--theme-blue));\\r\\n  transition: width 0.3s ease;\\r\\n}\\r\\n\\r\\n.nav-link:hover::after,\\r\\n.nav-link.active::after {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n/* Custom scrollbar */\\r\\n::-webkit-scrollbar {\\r\\n  width: 8px;\\r\\n  height: 8px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-track {\\r\\n  background: transparent;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb {\\r\\n  background: rgba(255, 255, 255, 0.1);\\r\\n  border-radius: 4px;\\r\\n}\\r\\n\\r\\n::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(255, 255, 255, 0.2);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb {\\r\\n  background: rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.light ::-webkit-scrollbar-thumb:hover {\\r\\n  background: rgba(0, 0, 0, 0.2);\\r\\n}\\r\\n\\r\\n/* Animation for loading spinner */\\r\\n@keyframes border-flow {\\r\\n  0% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n  50% {\\r\\n    background-position: 100% 50%;\\r\\n  }\\r\\n  100% {\\r\\n    background-position: 0% 50%;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes subtle-pulse {\\r\\n  0% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n  50% {\\r\\n    opacity: 1;\\r\\n  }\\r\\n  100% {\\r\\n    opacity: 0.7;\\r\\n  }\\r\\n}\\r\\n\\r\\n.animate-subtle-pulse {\\r\\n  animation: subtle-pulse 2s ease-in-out infinite;\\r\\n}\\r\\n\\r\\n.animate-rotate-loader {\\r\\n  animation: spin 1s linear infinite;\\r\\n}\\r\\n\\r\\n@keyframes spin {\\r\\n  from {\\r\\n    transform: rotate(0deg);\\r\\n  }\\r\\n  to {\\r\\n    transform: rotate(360deg);\\r\\n  }\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});