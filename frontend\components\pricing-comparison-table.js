"use client"

import { motion } from "framer-motion"
import { Check, X, HelpCircle } from "lucide-react"
import { AnimatedTableRow, AnimatedTableCell } from "./animations/PricingAnimations"
import { EnhancedButton } from "./ui/enhanced-button"
import Link from "next/link"

// Pricing comparison table component
export function PricingComparisonTable({ plans = defaultPricingPlans }) {
  // Extract all features from all plans
  const allFeatures = plans.reduce((acc, plan) => {
    plan.features.forEach(feature => {
      if (!acc.some(f => f.text === feature.text)) {
        acc.push(feature)
      }
    })
    return acc
  }, [])

  return (
    <div className="w-full overflow-x-auto">
      <motion.table
        className="w-full border-collapse text-sm"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <thead>
          <tr>
            <th className="p-3 text-left border-b border-white/10">Features</th>
            {plans.map((plan, index) => (
              <th
                key={index}
                className={`p-3 text-center border-b border-white/10 ${
                  plan.highlighted ? "bg-theme-purple/10" : ""
                }`}
              >
                {plan.title}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {allFeatures.map((feature, featureIndex) => (
            <AnimatedTableRow key={featureIndex} index={featureIndex}>
              <td className="p-3 text-left">
                <div className="flex items-center">
                  <span>{feature.text}</span>
                  {feature.tooltip && (
                    <motion.div
                      className="relative ml-2 cursor-help group"
                      whileHover={{ scale: 1.1 }}
                    >
                      <HelpCircle className="h-3 w-3 text-muted-foreground" />
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 p-2 bg-black/80 rounded-md text-xs text-white opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-50">
                        {feature.tooltip}
                      </div>
                    </motion.div>
                  )}
                </div>
              </td>
              {plans.map((plan, planIndex) => {
                const planFeature = plan.features.find(f => f.text === feature.text)
                return (
                  <AnimatedTableCell
                    key={planIndex}
                    highlight={plan.highlighted}
                  >
                    {planFeature?.value ? (
                      <div className="text-center">{planFeature.value}</div>
                    ) : planFeature?.included ? (
                      <Check className="h-4 w-4 text-theme-blue mx-auto" />
                    ) : (
                      <X className="h-4 w-4 text-muted-foreground mx-auto" />
                    )}
                  </AnimatedTableCell>
                )
              })}
            </AnimatedTableRow>
          ))}
          <AnimatedTableRow index={allFeatures.length}>
            <td className="p-3 text-left"></td>
            {plans.map((plan, index) => (
              <td
                key={index}
                className={`p-3 text-center ${
                  plan.highlighted ? "bg-theme-purple/10" : ""
                }`}
              >
                <Link href={plan.buttonLink}>
                  <EnhancedButton
                    size="sm"
                    color={plan.highlighted ? "purple" : "blue"}
                    className={`w-full ${
                      plan.highlighted
                        ? "bg-theme-purple text-white hover:bg-theme-purple/90"
                        : "bg-transparent border text-foreground dark:border-white/20 dark:hover:border-white/40 border-black/50 hover:border-black/70"
                    }`}
                  >
                    {plan.buttonText}
                  </EnhancedButton>
                </Link>
              </td>
            ))}
          </AnimatedTableRow>
        </tbody>
      </motion.table>
    </div>
  )
}

// Default pricing plans data
export const defaultPricingPlans = [
  {
    title: "Free Plan",
    price: "Free",
    description: "Perfect for trying out our service.",
    features: [
      { text: "Voiceover generations", included: true, value: "3" },
      { text: "Basic voice options", included: true },
      { text: "PDF & script upload", included: true },
      { text: "MP4 video generation", included: true },
      { text: "Voice cloning", included: false },
      { text: "Priority support", included: false },
    ],
    buttonText: "Get Started",
    buttonLink: "/dashboard",
    highlighted: false,
  },
  {
    title: "Starter Plan",
    price: "$9.99",
    description: "Most popular for regular users.",
    features: [
      { text: "Voiceover generations", included: true, value: "Unlimited" },
      { text: "All voice options", included: true },
      { text: "PDF & script upload", included: true },
      { text: "MP4 video generation", included: true },
      { text: "Voice cloning", included: true, value: "1 clone" },
      { text: "Email support", included: true },
    ],
    buttonText: "Choose Starter",
    buttonLink: "/",
    highlighted: true,
  },
  {
    title: "Premium Plan",
    price: "$19.99",
    description: "Best for power users and teams.",
    features: [
      { text: "Voiceover generations", included: true, value: "Unlimited" },
      { text: "All voice options", included: true },
      { text: "PDF & script upload", included: true },
      { text: "MP4 video generation", included: true },
      { text: "Voice cloning", included: true, value: "5 clones" },
      { text: "Priority support", included: true },
    ],
    buttonText: "Choose Premium",
    buttonLink: "/",
    highlighted: false,
  }
];
