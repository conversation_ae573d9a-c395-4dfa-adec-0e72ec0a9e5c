"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/pricing",{

/***/ "./components/ui/pixel-card.js":
/*!*************************************!*\
  !*** ./components/ui/pixel-card.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PixelCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nclass Pixel {\n    getRandomValue(min, max) {\n        return Math.random() * (max - min) + min;\n    }\n    draw() {\n        const centerOffset = this.maxSizeInteger * 0.5 - this.size * 0.5;\n        this.ctx.fillStyle = this.color;\n        this.ctx.fillRect(this.x + centerOffset, this.y + centerOffset, this.size, this.size);\n    }\n    appear() {\n        this.isIdle = false;\n        if (this.counter <= this.delay) {\n            this.counter += this.counterStep;\n            return;\n        }\n        if (this.size >= this.maxSize) {\n            this.isShimmer = true;\n        }\n        if (this.isShimmer) {\n            this.shimmer();\n        } else {\n            this.size += this.sizeStep;\n        }\n        this.draw();\n    }\n    disappear() {\n        this.isShimmer = false;\n        this.counter = 0;\n        if (this.size <= 0) {\n            this.isIdle = true;\n            return;\n        } else {\n            this.size -= 0.15;\n        }\n        this.draw();\n    }\n    shimmer() {\n        if (this.size >= this.maxSize) {\n            this.isReverse = true;\n        } else if (this.size <= this.minSize) {\n            this.isReverse = false;\n        }\n        if (this.isReverse) {\n            this.size -= this.speed;\n        } else {\n            this.size += this.speed;\n        }\n    }\n    constructor(canvas, context, x, y, color, speed, delay){\n        this.width = canvas.width;\n        this.height = canvas.height;\n        this.ctx = context;\n        this.x = x;\n        this.y = y;\n        this.color = color;\n        this.speed = this.getRandomValue(0.1, 0.9) * speed;\n        this.size = 0;\n        this.sizeStep = Math.random() * 0.6 + 0.2;\n        this.minSize = 1;\n        this.maxSizeInteger = 4;\n        this.maxSize = this.getRandomValue(this.minSize, this.maxSizeInteger);\n        this.delay = delay;\n        this.counter = 0;\n        this.counterStep = Math.random() * 4 + (this.width + this.height) * 0.01;\n        this.isIdle = false;\n        this.isReverse = false;\n        this.isShimmer = false;\n    }\n}\nfunction getEffectiveSpeed(value, reducedMotion) {\n    const min = 0;\n    const max = 100;\n    const throttle = 0.001;\n    const parsed = parseInt(value, 10);\n    if (parsed <= min || reducedMotion) {\n        return min;\n    } else if (parsed >= max) {\n        return max * throttle;\n    } else {\n        return parsed * throttle;\n    }\n}\n/**\n * Variants adapted for the pricing theme\n */ const VARIANTS = {\n    default: {\n        activeColor: null,\n        gap: 6,\n        speed: 35,\n        colors: \"rgba(226,232,240,0.3),rgba(203,213,225,0.4),rgba(148,163,184,0.3)\",\n        noFocus: false\n    },\n    purple: {\n        activeColor: \"#e9d5ff\",\n        gap: 6,\n        speed: 40,\n        colors: \"rgba(233,213,255,0.4),rgba(196,181,253,0.5),rgba(139,92,246,0.3)\",\n        noFocus: false\n    },\n    blue: {\n        activeColor: \"#dbeafe\",\n        gap: 6,\n        speed: 40,\n        colors: \"rgba(219,234,254,0.4),rgba(147,197,253,0.5),rgba(59,130,246,0.3)\",\n        noFocus: false\n    },\n    highlighted: {\n        activeColor: \"#e9d5ff\",\n        gap: 5,\n        speed: 50,\n        colors: \"rgba(233,213,255,0.4),rgba(196,181,253,0.5),rgba(139,92,246,0.4),rgba(219,234,254,0.3),rgba(147,197,253,0.4)\",\n        noFocus: false\n    }\n};\nfunction PixelCard(param) {\n    let { variant = \"default\", gap, speed, colors, noFocus, className = \"\", children } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const pixelsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const timePreviousRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(typeof performance !== \"undefined\" ? performance.now() : Date.now());\n    const [reducedMotion, setReducedMotion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (true) {\n            setReducedMotion(window.matchMedia(\"(prefers-reduced-motion: reduce)\").matches);\n        }\n    }, []);\n    const variantCfg = VARIANTS[variant] || VARIANTS.default;\n    const finalGap = gap !== null && gap !== void 0 ? gap : variantCfg.gap;\n    const finalSpeed = speed !== null && speed !== void 0 ? speed : variantCfg.speed;\n    const finalColors = colors !== null && colors !== void 0 ? colors : variantCfg.colors;\n    const finalNoFocus = noFocus !== null && noFocus !== void 0 ? noFocus : variantCfg.noFocus;\n    const initPixels = ()=>{\n        if (!containerRef.current || !canvasRef.current) return;\n        const rect = containerRef.current.getBoundingClientRect();\n        const width = Math.floor(rect.width);\n        const height = Math.floor(rect.height);\n        const ctx = canvasRef.current.getContext(\"2d\");\n        canvasRef.current.width = width;\n        canvasRef.current.height = height;\n        canvasRef.current.style.width = \"\".concat(width, \"px\");\n        canvasRef.current.style.height = \"\".concat(height, \"px\");\n        const colorsArray = finalColors.split(\",\");\n        const pxs = [];\n        for(let x = 0; x < width; x += parseInt(finalGap, 10)){\n            for(let y = 0; y < height; y += parseInt(finalGap, 10)){\n                const color = colorsArray[Math.floor(Math.random() * colorsArray.length)];\n                const dx = x - width / 2;\n                const dy = y - height / 2;\n                const distance = Math.sqrt(dx * dx + dy * dy);\n                const delay = reducedMotion ? 0 : distance;\n                pxs.push(new Pixel(canvasRef.current, ctx, x, y, color, getEffectiveSpeed(finalSpeed, reducedMotion), delay));\n            }\n        }\n        pixelsRef.current = pxs;\n    };\n    const doAnimate = (fnName)=>{\n        var _canvasRef_current;\n        animationRef.current = requestAnimationFrame(()=>doAnimate(fnName));\n        const timeNow = typeof performance !== \"undefined\" ? performance.now() : Date.now();\n        const timePassed = timeNow - timePreviousRef.current;\n        const timeInterval = 1000 / 60; // ~60 FPS\n        if (timePassed < timeInterval) return;\n        timePreviousRef.current = timeNow - timePassed % timeInterval;\n        const ctx = (_canvasRef_current = canvasRef.current) === null || _canvasRef_current === void 0 ? void 0 : _canvasRef_current.getContext(\"2d\");\n        if (!ctx || !canvasRef.current) return;\n        ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);\n        let allIdle = true;\n        for(let i = 0; i < pixelsRef.current.length; i++){\n            const pixel = pixelsRef.current[i];\n            pixel[fnName]();\n            if (!pixel.isIdle) {\n                allIdle = false;\n            }\n        }\n        if (allIdle) {\n            cancelAnimationFrame(animationRef.current);\n        }\n    };\n    const handleAnimation = (name)=>{\n        cancelAnimationFrame(animationRef.current);\n        animationRef.current = requestAnimationFrame(()=>doAnimate(name));\n    };\n    const onMouseEnter = ()=>handleAnimation(\"appear\");\n    const onMouseLeave = ()=>handleAnimation(\"disappear\");\n    const onFocus = (e)=>{\n        if (e.currentTarget.contains(e.relatedTarget)) return;\n        handleAnimation(\"appear\");\n    };\n    const onBlur = (e)=>{\n        if (e.currentTarget.contains(e.relatedTarget)) return;\n        handleAnimation(\"disappear\");\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initPixels();\n        const observer = new ResizeObserver(()=>{\n            initPixels();\n        });\n        if (containerRef.current) {\n            observer.observe(containerRef.current);\n        }\n        return ()=>{\n            observer.disconnect();\n            cancelAnimationFrame(animationRef.current);\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        finalGap,\n        finalSpeed,\n        finalColors,\n        finalNoFocus\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"relative overflow-hidden rounded-xl border border-white/10 isolate transition-colors duration-200 ease-[cubic-bezier(0.5,1,0.89,1)] select-none min-h-[400px] \".concat(className),\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onFocus: finalNoFocus ? undefined : onFocus,\n        onBlur: finalNoFocus ? undefined : onBlur,\n        tabIndex: finalNoFocus ? -1 : 0,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                className: \"absolute inset-0 w-full h-full block pointer-events-none\",\n                ref: canvasRef\n            }, void 0, false, {\n                fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\pixel-card.js\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AiPresenter\\\\frontend\\\\components\\\\ui\\\\pixel-card.js\",\n        lineNumber: 260,\n        columnNumber: 5\n    }, this);\n}\n_s(PixelCard, \"HeekizPLUKtvxmyXJ0VDaWjZs9A=\");\n_c = PixelCard;\nvar _c;\n$RefreshReg$(_c, \"PixelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ui/pixel-card.js\n"));

/***/ })

});